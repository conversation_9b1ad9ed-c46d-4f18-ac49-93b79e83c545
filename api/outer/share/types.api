syntax = "v1"

// web/app获取分享配置的接口
// -------------------------------------------------------
type GetShareConfigRequest struct{
	Source string `json:"source"`
	Language string `json:"language"`
	Platform string `json:"platform"`
}

type ShareConfig struct{
	ID 	int64 `json:"id"`
	BaseImage string `json:"base_image"`
	UnionLogo string `json:"union_logo"`
	Tip string `json:"tip"`
}

type GetShareConfigResponse struct {
	ShareConfig ShareConfig `json:"share_config"`
}
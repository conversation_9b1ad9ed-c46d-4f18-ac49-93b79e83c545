syntax = "v1"

// 红牛落地页红牛落地页 - 详细展示（照片墙、视频、赛事）
// -------------------------------------------------------
type BrandPartnerDetailRequest struct {
	BrandPartnerId int64 `form:"brand_partner_id"`	// 品牌合作方ID（1-红牛品牌合作、2-国米品牌合作）
}

type BrandPartnerDetailResponse struct {
	PhotoWallConfig   []*PhotoWallItem   `json:"photo_wall_config"`		// 照片墙配置
	VideoConfig       []*VideoItem       `json:"video_config"`			// 视频配置
	CompetitionConfig []*CompetitionItem `json:"competition_config"`	// 赛事配置
}

// 照片墙配置结构体
type PhotoWallItem struct {
	Url    string `json:"url"`			// 图片URL
	Width  int    `json:"width"`		// 图片宽度（px）
	Height int    `json:"height"`		// 图片高度（px）
}

// 视频配置结构体
type VideoItem struct {
	Poster string `json:"poster"`	    // 视频封面
	Url       string `json:"url"`	    // 视频URL
}

// 赛事配置结构体
type CompetitionItem struct {
	MatchNumber int `json:"match_number"`	// 比赛编号
	IsChampion  int `json:"is_champion"`	// 是否夺冠（1-是、0-否）
}


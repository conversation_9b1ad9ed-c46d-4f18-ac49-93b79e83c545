syntax = "v1"

import "./types.api"


@server(
	prefix: /api/web/v1/marketing-activity
	group: outer/activity
	middleware: AuthAllowMiddleware
	timeout : 5s
)

service marketing_activity {
    @doc "测试方法"
    @handler NftTest
    post /nftTest (NftTestReq) returns (NftTestResp)

    @doc "页面初始化接口"
    @handler PageInit
    post /pageInit (PageInitReq) returns (PageInitResp)

    @doc "活动列表查询"
    @handler NftActivityList
    get /nftActivityList (ActivityListReq) returns (ActivityListResp)

    @doc "任务列表查询"
    @handler NftTaskList
    get /nftTaskList (NftTaskListReq) returns (NftTaskListResp)

    @doc "web3客户端Nft验证及初始化"
    @handler ClientVerifyNftMint
    post /clientVerifyNftMint (ClientVerifyNftMintReq) returns (ClientVerifyNftMintResp)

    @doc "加密数据"
    @handler EncryptData
    post /encryptData (EncryptDataReq) returns (EncryptDataResp)

    //@doc "web3后端 NFT铸造验证"
    //@handler WebVerifyNftMint
    //post /webVerifyNftMint (WebVerifyNftMintReq) returns (WebVerifyNftMintResp)

    @doc "助力弹框详情"
    @handler ShareHelpDetail
    post /shareHelpDetail (ShareHelpRecordReq) returns (shareHelpDetailResp)

    @doc "助力"
    @handler ShareHelpRecord
    post /shareHelpRecord (ShareHelpRecordReq) returns (ShareHelpRecordResp)

    //@doc "接收web3 nft铸造结果"
    //@handler ReceiveMintResult
    //post /receiveMintResult (ReceiveMintResultReq) returns (ReceiveMintResultResp)

    @doc "NFT列表查询"
    @handler NftDetailList
    get /nftDetailList (ActivityNftListReq) returns (ActivityNftListResp)

    @doc "铸造nft（中心化）"
    @handler Casting
    post /casting (CastingReq) returns (CastingResp)

    @doc "NFT铸造结果轮询接口"
    @handler RollNftMintResult
    get /rollNftMintResult (RollNftMintResultReq) returns (RollNftMintResultResp)

    @doc "nft收集进度查询"
    @handler CollectionNftProgress
    get /collectionNftProgress (CollectionNftProgressReq) returns (CollectionNftProgressResp)

    @doc "完成任务触发"
    @handler TaskTrigger
    post /taskTrigger (TaskTriggerReq) returns (TaskTriggerResp)

     @doc "web3客户端防女巫验证"
     @handler CaptchaVerify
     post /captchaVerify (CaptchaVerifyReq) returns (CaptchaVerifyResp)

    @doc "惊喜加时-任务列表"
    @handler SurpriseExtensionTaskList
    get /surpriseExtensionTaskList (SurpriseExtensionTaskListReq) returns (SurpriseExtensionTaskListResp)

    @doc "用户抽奖信息"
    @handler UserNftPrizeInfo
    get /userNFTPrizeInfo (UserNFTPrizeInfoReq) returns (UserNFTPrizeInfoResp)

    @doc "所有中奖记录查询"
    @handler NftActivityPrizeInfo
    get /nftActivityPrizeInfo (NftActivityPrizeInfoReq) returns (NftActivityPrizeInfoResp)

    @doc "兑奖"
    @handler ClaimPrize
    post /claimPrize (ClaimPrizeReq) returns (ClaimPrizeResp)

}

syntax = "v1"

//页面初始化接口
// -------------------------------------------------------
type PageInitReq {
      UserId            int64  `json:"user_id,optional"`              // 用户ID 中心化必传
      WalletAddress     string `json:"wallet_address,optional"`   // 唯一钱包标识  去中心化必传
      ActivityID        int64  `json:"activity_id"`               // 活动ID
}
type PageInitResp {
}

//活动列表查询
// -------------------------------------------------------
type ActivityListReq {
      IsCentralized     int   `form:"is_centralized,optional"`       // 是否中心化 1:中心化，其他非中心化
      ActivityType      int   `form:"activity_type,optional"`        // 活动类型 1正常4期NFT活动 2特殊的3期活动
}

type ActivityListResp {
      List []*ActivityInfo `json:"list"` //活动信息
}

type ActivityInfo {
      ActivityID       int64  `json:"activity_id"`
      ActivityName     string `json:"activity_name"`
      StartTime        string `json:"start_time"`
      EndTime          string `json:"end_time"`
      ReceiveStartTime string `json:"receive_start_time"`
      ReceiveEndTime   string `json:"receive_end_time"`
      TaskStartTime    string `json:"task_start_time"`
      TaskEndTime      string `json:"task_end_time"`
      ActivityStatus   int    `json:"activity_status"`
      SurpriseExtensionStartTime string `json:"surprise_extension_start_time"`      // 惊喜加时阶段-开始时间
      SurpriseExtensionEndTime   string `json:"surprise_extension_end_time"`        // 惊喜加时阶段-结束时间
      TaskStep         int    `json:"task_step"`                                    // 任务阶段：0未开始、1免费领取、2加时阶段、3惊喜加时
      SettlementStartTime    int64 `json:"settlement_start_time"`      // 结算-开始时间
      SettlementEndTime      int64 `json:"settlement_end_time"`        // 结算-结束时间
      DrawStartTime    int64 `json:"draw_start_time"`      // 抽奖-开始时间
      DrawEndTime      int64 `json:"draw_end_time"`        // 抽奖-结束时间
      AnnouncementStartTime    int64 `json:"announcement_start_time"`      // 结果展示-开始时间
      AnnouncementEndTime      int64 `json:"announcement_end_time"`        // 结果展示-结束时间
}

//任务列表查询
// -------------------------------------------------------
type NftTaskListReq {
      Uid               int64  `form:"uid,optional"`        // 用户ID 中心化必传
      WalletAddress     string `form:"wallet_address,optional"`   // 唯一钱包标识  去中心化必传
      IsUserIdReq       bool   `form:"is_uid_req,optional"` // 是否用户ID请求（内部临时变量用）
}

type NftTaskListResp {
      BaseInfo          BaseInfo `json:"base_info"`         // 基础信息
      List []*NftTaskListItem `json:"list"`                 // 活动信息
}
type BaseInfo {
      TaskStatus        int      `json:"task_status"`         // 任务状态 1:未开始 2:进行中 3:已完成普通 Nft 收集 4:已结束
      TotalToCollect    int64    `json:"total_to_collect"`    // 共需收集数量
      Collected         int64    `json:"collected"`           // 已收集数量
      TimeLeft          int64    `json:"time_left"`           // 剩余时间，单位：秒
      ActivityId        int64    `json:"activity_id"`         // 任务列表
      MintingNft        int      `json:"minting_nft"`           // 当前是否有铸造的nft 1:有 其他值：没有 有正在铸造的nft,点击领取按钮需要特殊提醒
}

type NftTaskListItem {
      TaskId           int64    `json:"task_id"`              // 任务ID
      TaskName         string `json:"task_name"`            // 任务名称
      TaskType         int64    `json:"task_type"`            // 任务类型
      CompletionLimit  int64    `json:"completion_limit"`     // 计划需要完成的数量
      DoneLimit        int64    `json:"done_limit"`           // 已完成的数量
      MediaId          string   `json:"media_id"`           // 任务对应的社媒账号
      SubTaskList      []SubTaskItem `json:"sub_task_list"` // 子任务列表
}

// 新增的结构体
type SubTaskItem {
      SubTaskId         int64   `json:"sub_task_id"`               // 子任务ID
      HelpUserId        string  `json:"help_user_id"`              // 去中心化助力人钱包标识没有头像
      HelpUserAvatar    string  `json:"help_user_avatar"`          // 头像
      ReceiveNftStatus  int64   `json:"receive_nft_status"`        // 领取nft状态：0: 未完成任务，无法领取、1：任务已完成，可以领取、2: 已领取（铸造中）、3：已领取（铸造)
      OperationID       int64  `json:"operation_id"`               //唯一操作ID 当nft处于铸造中的状态会返回这个值，前端用于轮询时传参数
}

// 加密函数
// -------------------------------------------------------
type EncryptDataReq {
      Uid                     int64    `json:"uid,optional"`              // 用户ID 中心化必传
      WalletAddress           string   `json:"wallet_address,optional"`   // 唯一钱包标识  去中心化必传
      ActivityId              int64    `json:"activity_id,optional"`      // 活动ID
}
type EncryptDataResp {
      EncryptedUid            string `json:"encrypted_uid"`             // 加密的用户ID
      EncryptedWalletAddress  string `json:"encrypted_wallet_address"`  // 加密的钱包地址
      EncryptedActivityId     string `json:"encrypted_activity_id"`     // 加密的活动ID
}

//web3客户端防女巫、IP验证请求参数
type ClientVerifyNftMintReq {
    ActivityId       int64  `json:"activity_id"`          //活动ID
	ParentNftID      int64  `json:"parent_nft_id"`        //需要透传的NFT ID 活动期间正常领取必传，做任务领取期间不传
	WalletAddress    string `json:"wallet_address"`       //钱包唯一标识
	NftUserSubTaskID int64  `json:"nft_user_sub_task_id"` //用户关联的子任务ID 在额外活动期间完成任务铸造NFT需要透传的信息
	Amount           int    `json:"amount"`               //铸造数量
	NftID            int64  `json:"nft_id"`               //nft_id nft列表页面铸造必传
}

// web3客户端防女巫、IP验证返回值
type ClientVerifyNftMintResp {
	ParentNftID     int    `json:"parent_nft_id"`     //可铸造的需要透传的NFT ID
	MintStatus      int    `json:"mint_status"`       //是否可铸造 1:是 2:否 3:验证未通过15分钟冷静期
	MintStatusCause string `json:"mint_status_cause"` //不可以铸造原因 可铸造情况下为空
	OperationID     int64  `json:"operation_id"`      //需要透传的操作ID 裂变这边用于区分用户重复铸造相同的nft的操作
}

// 助力详情
// -------------------------------------------------------
type shareHelpDetailResp {
      TimeLeft     int64    `json:"time_left"`       // 剩余时间
      UserId       string `json:"user_id"`         // 用户ID或钱包地址
      AvatarUrl    string `json:"avatar_url"`      // 头像地址
      Status       int64    `json:"status"`          // 助力状态：1（助力活动进行中）、2（该助力已被完成）、3（活动已结束）、4（您已助力过）
}

// 助力
// -------------------------------------------------------
type ShareHelpRecordReq {
      EncryptedActivityId     string `json:"encrypted_activity_id"`              // 活动ID（加密）
      EncryptedUid            string `json:"encrypted_uid,optional"`             // 邀请人用户ID（加密） 中心化必传
      EncryptedWalletAddress  string `json:"encrypted_wallet_address,optional"`  // 邀请人用户ID（加密） 去中心化必传
      Uid                     int64    `json:"uid,optional"`                       // 助力人用户ID 中心化必传
      WalletAddress           string `json:"wallet_address,optional"`            // 助力人唯一钱包标识 去中心化必传

      // 内部当结构体用，解密后的值
      ActivityId              int64 `json:"activity_id,optional,optional"`       // 活动ID
      InviteUid               int64 `json:"invite_uid,optional,optional"`         // 邀请人用户ID
      InviteWalletAddress     string `json:"invite_wallet_address,optional"`      // 邀请人唯一钱包标识
}
type ShareHelpRecordResp {
}

//通用无参数请求结构体
type UniversalNoParam {}

//NFT列表查询请求参数
type ActivityNftListReq {
    IsCentralization  int    `json:"is_centralization"`    // 中心化和去中心化判断 1:中心化 2:去中心化
	ActivityID        int64  `json:"activity_id"`          // 活动 ID
	UID               int64  `json:"uid"`                  // 用户 ID 中心化必传，和 wallet_address 只能传一个(用于查询 nft 铸造状态)
	WalletAddress     string `json:"wallet_address"`       // 钱包唯一标识 去中心化必传，和 uid 只能传一个(用于查询 nft 铸造状态)
}

// NFT列表查询返回值
type ActivityNftListResp {
	ActivityID           int64          `json:"activity_id"`             // 活动 ID
	TaskStatus           int            `json:"task_status"`             // 任务状态 1:未开始 2:进行中 3:已完成普通 Nft 收集 4:已结束
	AllBasicNftCount     int            `json:"all_basic_nft_count"`     // 需要收集的普通 Nft 全部数量
	CollectBasicNftCount int            `json:"collect_basic_nft_count"` // 已经收集的 Nft 数量
	List                 []*CampaignDay `json:"list"`                    // 活动日列表
}

// CampaignDay 表示活动的某一天的信息
type CampaignDay {
	CampaignDay           int     `json:"campaign_day"`        // 活动日 1:第一天 2:第二天 3:第三天 通常为 3 天
	CampaignDayStatus     int     `json:"campaign_day_status"` // 活动状态 1:未开始 2:进行中 3:已错过
	CampaignStartTime     string  `json:"campaign_start_time"` // 活动日开始时间
	CampaignEndTime       string  `json:"campaign_end_time"`   // 活动日结束时间
	CampaignStartTimeLeft int64     `json:"campaign_start_time_left"` // 活动日开始时间
    CampaignEndTimeLeft   int64     `json:"campaign_end_time_left"`   // 活动日结束时间
	NftList               []*NftInfo `json:"nft_list"`            // 该活动日的 Nft 列表
}

// NftInfo 表示单个 Nft 的信息
type NftInfo {
    ID                int64   `json:"id"`                  // 用于前端判读唯一性
	NftID             int64   `json:"nft_id"`              // Nft 主键 ID 当不是普通 Nft 的时候值为 0
	ParentNftID       int64   `json:"parent_nft_id"`       // 上一层 nftID，用于铸造过程的数据传输
	NftName           string  `json:"nft_name"`            // nft 名称
	NftType           int     `json:"nft_type"`            // nft 类型 1 普通 nft 2 白银 nft 3 黄金 nft
	CampaignDay       int     `json:"campaign_day"`        // 活动日信息 1:第一天 2:第二天 3:第三天 通常为 3 天
	NftIconURL        string  `json:"nft_icon_url"`        // Nft 图片地址
	NftGifURL         string  `json:"nft_gif_url"`         // 高等级 Nft 展示的动图(黄金，白银等级来回切换的动图)
	NftPrice          float64 `json:"nft_price"`           // Nft 价格 普通 Nft 在活动免费领取时间范围内为 0
	CampaignNftStatus int     `json:"campaign_nft_status"` // 活动状态 1:可领取/铸造 2:未到活动期 3:已过期 4:已领取 5:铸造中(按钮不可操作) 6:额外活动期普通 Nft 可购买
	CastStatusCause   string  `json:"cast_status_cause"`   // 上次铸造失败原因
	CampaignStartTimeLeft int64     `json:"campaign_start_time_left"` // 活动日开始时间
    CampaignEndTimeLeft   int64     `json:"campaign_end_time_left"`   // 活动日结束时间
    OperationID           int64  `json:"operation_id"`      //唯一操作ID 当nft处于铸造中的状态会返回这个值，前端用于轮询时传参数
}

// 铸造nft（中心化）
// -------------------------------------------------------
type CastingReq {
      ActivityId        int64    `json:"activity_id"`                     // 活动ID
      Scene             int64    `json:"scene"`                           // 任务场景：1-免费、购买，2-做任务
      UserId            int64    `json:"user_id"`                         // 用户ID
      UserSubTaskId     int64    `json:"user_sub_task_id,optional"`       // 用户子任务ID（做任务场景必传）
      ParentNftId       int64    `json:"parent_nft_id,optional"`          // 父NFT ID（免费、购买场景必传）
      NftId             int64    `json:"nft_id,optional"`                 // NFT ID（免费场景必传）
      Number            int64    `json:"number,optional"`                 // 购买数量
      NftType           int64    `json:"nft_type,optional"`               // nft类型：1普通、2银、3金（免费、购买场景必传）

      // 非参数，接口当变量使用
      OrderId           int64    `json:"order_id,optional"`               // 订单ID
}

type CastingResp {
      OrderId  int64    `json:"order_id"`            // 订单ID
      NftList []*NftItem `json:"nft_list"`           // 获得NFT 列表
}

type NftItem struct {
      NftID      int64     `json:"nft_id"`           // nft表主键ID
      NftName    string    `json:"nft_name"`         // NFT名称
      NftType    int64     `json:"nft_type"`         // nft类型 1普通nft 2白银nft 3黄金nft 4特殊活动对应的NFT
      NftIconURL string    `json:"nft_icon_url"`     // nft图片地址
      NftGifURL  string    `json:"nft_gif_url"`      // nft动图
      Amount     int64     `json:"amount"`           // nft数量
}

//NFT铸造结果轮询接口请求参数
type RollNftMintResultReq {
	ActivityID       int64  `json:"activity_id"`          // 活动 ID
	UID              int64  `json:"uid,omitempty"`        // 用户 ID，中心化场景下必传，和 wallet_address 只能传一个，用于查询 nft 铸造状态
	WalletAddress    string `json:"wallet_address"`       // 钱包唯一标识，去中心化场景下必传，和 uid 只能传一个，用于查询 nft 铸造状态
	ParentNftID      int64  `json:"parent_nft_id"`        // Parent NFT ID
	NftUserSubTaskId int64  `json:"nft_user_sub_task_id"` //做任务免费领取关联的子任务ID 后端用于区分铸造来源 ("额外活动期的免费领取"，"正常购买或活动期免费领取")
	OperationID      int64  `json:"operation_id"`      //唯一操作ID 当nft处于铸造中的状态会返回这个值，前端用于轮询时传参数
}

//NFT铸造结果轮询接口返回值
type RollNftMintResultResp {
	MintStatus int        `json:"mint_status"` // 铸造状态 1:进行中 2:成功 3:失败 当状态是 2 的时候会展示铸造成功的 nft 信息
	NftList    []*NftProf `json:"nft_list"`    // NFT 列表
}

type NftProf {
 	NftID      int64    `json:"nft_id"`       // NFT 主键 ID，当不是普通 NFT 的时候值为 0
 	NftName    string `json:"nft_name"`     // nft 名称
 	NftIconURL string `json:"nft_icon_url"` // NFT 图片地址
 	NftAmount  int    `json:"nft_amount"`   // NFT 数量，注意原 JSON 中是 nft_amount 但注释写的是 NFT 价格，这里按名称来处理
}

// 测试方法
type NftTestReq {
      Business    int64 `json:"business"`      // 业务类型
 	ParamStr    string `json:"paramStr"`     // 业务参数
      Sign        string `json:"sign"`         // 签名
}
type NftTestResp {
      Result      string `json:"result"`
}

//NFT收集进度请求参数
type CollectionNftProgressReq {
	UID              int64  `json:"uid,omitempty"`        // 用户 ID，中心化场景下必传
	WalletAddress    string `json:"wallet_address"`       // 钱包唯一标识，去中心化场景下必传
    NftType          int    `json:"nft_type"`             // nft类型 1普通nft 2白银nft 3黄金nft 4特殊活动对应的NFT
}

type NftProgress struct {
	NftID         int64    `json:"nft_id"`
	NftName       string `json:"nft_name"`
	NftIconURL    string `json:"nft_icon_url"`
	NftAmount     int    `json:"nft_amount"`
	ReleaseStatus int    `json:"release_status"`
}

type ActivityRound struct {
	RoundNum        int            `json:"round_num"`
	AllNftCount     int            `json:"all_nft_count"`
	CollectNftCount int            `json:"collect_nft_count"`
	CampaignStatus  int            `json:"campaign_status"` //活动状态 1:未开始 2:进行中 3:已错过
	NftList         []*NftProgress `json:"nft_list"`
}

//NFT收集进度返回值
type CollectionNftProgressResp struct {
	List []*ActivityRound `json:"list"`
}

//完成任务触发
// -------------------------------------------------------
type TaskTriggerReq {
      SubTaskId         int64  `json:"sub_task_id"`    // 子任务ID
      UserId            int64  `json:"user_id"`        // 用户ID
}
type TaskTriggerResp {}

// NFT接口结构体
type NftMintResponse {
      Mid int64 `json:"mid"`
}

//防女巫验证请求参数
type CaptchaVerifyReq {
    WalletAddress    string `json:"wallet_address"`       // 钱包唯一标识
    CaptchaType      int    `json:"captcha_type"`         //验证码类型 0 极验，1 谷歌 根据/api/web/v1/validation/captcha_init结果传输
	Scene            string `json:"scene"`                //验证场景 根据/api/web/v1/validation/captcha_init结果传输
	CaptchaID        string `json:"captcha_id"`           //极验验证 id 极验必填 根据/api/web/v1/validation/captcha_init结果传输
	CaptchaOutput    string `json:"captcha_output"`       //极验验证输出信息 极验必填 根据/api/web/v1/validation/captcha_init结果传输
	GenTime          int    `json:"gen_time"`             //验证通过时间戳 极验必填 根据/api/web/v1/validation/captcha_init结果传输
	LotNumber        string `json:"lot_number"`           //极验验证流水号 极验必填 根据/api/web/v1/validation/captcha_init结果传输
	PassToken        string `json:"pass_token"`           //验证通过标识  谷歌极验都必填 根据/api/web/v1/validation/captcha_init结果传输
}

//防女巫验证返回值
type CaptchaVerifyResp {
    VerifyStatus      int    `json:"verify_status"`       //验证是否通过 1:通过 2:未通过
}


// 惊喜加时-任务列表请求参数
type SurpriseExtensionTaskListReq {
      UserId            int64  `json:"user_id,optional"`              // 用户ID 中心化必传
      WalletAddress     string `json:"wallet_address,optional"`       // 唯一钱包标识  去中心化必传
      IsFromNftChain    int    `json:"is_from_nft_chain,optional"`    // 是否通过nft落地页ch码注册（后端做变量用，非前端传）
}

// 惊喜加时-任务列表返回值
type SurpriseExtensionTaskListResp {
      BaseInfo          SurpriseExtensionTaskBaseInfo `json:"base_info"`         // 基础信息
      List []*SurpriseExtensionTaskItem `json:"list"`
}

type SurpriseExtensionTaskBaseInfo {
      ActivityId        int64    `json:"activity_id"`         // 任务列表
      TotalToCollect    int64    `json:"total_to_collect"`    // 共需收集数量
      Collected         int64    `json:"collected"`           // 已收集数量
      IsFromNftChain    int      `json:"is_from_nft_chain"`   // 是否通过nft落地页ch码注册
      TaskStatus        int      `json:"task_status"`         // 任务状态 1:未开始 2:进行中 3:已完成普通 Nft 收集 4:已结束
}

type SurpriseExtensionTaskItem {
      TaskId           int64    `json:"task_id"`              // 任务ID
      TaskName         string `json:"task_name"`              // 任务名称
      TaskType         int64    `json:"task_type"`            // 任务类型
      CompletionLimit  int64    `json:"completion_limit"`     // 计划需要完成的数量
      DoneLimit        int64    `json:"done_limit"`           // 已完成的数量
      SubTaskList      []*SurpriseExtensionTaskSubTaskItem `json:"sub_task_list"` // 子任务列表
}

// 新增的结构体
type SurpriseExtensionTaskSubTaskItem {
      SubTaskId         int64   `json:"sub_task_id"`               // 子任务ID
      HelpUserId        string  `json:"help_user_id"`              // 去中心化助力人钱包标识没有头像
      HelpUserAvatar    string  `json:"help_user_avatar"`          // 头像
      ReceiveNftStatus  int64   `json:"receive_nft_status"`        // 领取nft状态：0: 未完成任务，无法领取、1：任务已完成，可以领取、2: 已领取（铸造中）、3：已领取（铸造)
}


type NFTPrizeDetail {
    PrizeInfoID int64  `json:"prize_info_id"`
    PrizeType int64 `json:"prize_type"`
    PrizeCategory int64 `json:"prize_category"` // 1: 实物 2：虚拟
    Status int  `json:"status"`  // 是否兑奖: 1：未兑奖 2：已兑奖
}

type UserNFTPrizeInfoReq{
    IsCentralization     int64   `form:"is_centralization,optional"`       // 是否中心化 1:中心化，其他非中心化
    ActivityID      int64   `form:"activity_id"`        // 活动id
    WalletAddress string `form:"wallet_address,optional"` // 钱包地址
}

type UserNFTPrizeInfo{
    DrawCnt int64  `json:"draw_cnt"`
    DrawNumber []string `json:"draw_number"`
    PrizeDetail []NFTPrizeDetail `json:"prize_detail"`
}

type UserNFTPrizeInfoResp{
    UserPrizeInfo UserNFTPrizeInfo `json:"user_prize_info"`
}

type NFTActivityPrizeInfo {
    PrizeDrawNumber string `json:"prize_draw_number"`
    RaffleHash      string `json:"raffle_hash"`
    PrizeType           int64 `json:"prize_type"`
}


type NftActivityPrizeInfoReq{
    ActivityID      int64   `form:"activity_id"`        // 活动id
}

type NftActivityPrizeInfoResp{
    ActivityPrizeInfo []NFTActivityPrizeInfo `json:"activity_prize_info"`
}

type ClaimInfo{
    Name string `json:"name,optional,omitempty"`
    Phone string `json:"phone,optional,omitempty"`
    Email string `json:"email,optional,omitempty"`
    Address string `json:"address,optional,omitempty"`
    City string `json:"city,optional,omitempty"`
    State string `json:"state,optional,omitempty"`
    Country string `json:"country,optional,omitempty"`
    PostalCode string `json:"postal_code,optional,omitempty"`
    GateUid string `json:"gate_uid,optional,omitempty"`
    RegisteredEmail string `json:"registered_email,optional,omitempty"`
}

type ClaimPrizeReq{
    PrizeInfoID  int64 `json:"prize_info_id"`
    IsCentralization int64 `json:"is_centralization"`
    WalletAddress string `json:"wallet_address,optional"`
    ClaimInfo ClaimInfo `json:"claim_info"`
}

type ClaimPrizeResp{}

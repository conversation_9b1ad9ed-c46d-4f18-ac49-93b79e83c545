syntax = "v1"

import "./types.api"
// import "./contract_transform/contract_transform.api"
import "./activity/activity.api"
import "./nft/nft.api"
import "./share/share_web.api"
import "./share/share_app.api"
import "./download_activity/base.api"
import "./brand_partner/brand_partner.api"
import "./vip_airdrop/vip_airdrop.api"
@server(
	prefix: /api/web/v1/marketing-activity
	group: outer/test
)

service marketing_activity {
    @doc "测试接口"
    @handler GetHelloOuter
    get /get_hello (TestApiOuterRequest) returns (TestApiOuterResponse)

    @doc "测试接口"
    @handler GetHello2Outer
    get /get_hello2 (TestApiOuterRequest) returns (TestApiOuterResponse)

    @doc "测试接口"
    @handler GetTestOuter
    get /test (TestApiOuterRequest) returns (TestApiOuterResponse)
}

syntax = "v1"

import "./types.api"

@server(
	prefix: /api/web/v1/marketing-activity
	group: outer/help_get_coupons
	middleware: AuthAllowMiddleware
	timeout : 5s
)

service marketing_activity {

	@doc "获取助力领券奖励额"
	@handler HelpGetReward
	get /helpGetReward returns (HelpGetRewardResp)

	@doc "页面初始化接口"
	@handler HelpPageInit
	get /helpPageInit returns (HelpPageInitResp)

	@doc "用户报名"
	@handler HelpSignup
	post /helpSignup(HelpSignupReq) returns (HelpSignupResp)

	@doc "邀请人领奖"
	@handler HelpGetCoupon
	post /helpGetCoupon(HelpCouponReq) returns (HelpCouponResp)

	@doc "查询被邀请人记录"
	@handler HelpInviteeRecord
	get /helpInviteeRecord (HelpInviteeRecordReq) returns (HelpInviteeRecordResp)

	@doc "查询邀请人记录"
	@handler HelpInviterRecord
	get /helpInviterRecord (HelpInviterRecordReq) returns (HelpInviterRecordResp)

}
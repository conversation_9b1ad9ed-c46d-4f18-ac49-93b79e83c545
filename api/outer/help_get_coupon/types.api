syntax = "v1"

// 获取奖励额
type HelpGetRewardResp {
      InviterRewardValue      int         `json:"inviter_reward_value"` // 邀请人奖励券额
      InviteeRewardValue      int         `json:"invitee_reward_value"` // 被邀请人奖励券额
}

// 页面初始化
type HelpPageInitResp {
      Login             int         `json:"login"`    
      TimeLeft          int64       `json:"time_left"`            // 剩余时间
      Status            int         `json:"status"`               // 邀请人助力状态 1.未报名 2.待助力 3.助力成功 4.助力未完成 5.已领奖 6.命中风控
      HelpUsers         []*HelpUser `json:"help_user_list"`       // 助力用户
}

type HelpUser {
      UserId            int64       `json:"user_id"`              // 用户ID
      Avatar            string      `json:"avatar"`           // 头像地址
      Name              string      `json:"name"`                 // 名字
      Status            int         `json:"status"`          // 完成状态 0未创建 1注册 2入金/交易 3已完成 4已结束 5命中风控
      Sort              int         `json:"sort"`           // 排序
      Steps             []*Step     `json:"steps"`
}
type Step {
      TaskId            int64       `json:"task_id"`              // 任务ID
      Label             string       `json:"label"`                // 进度名 "signup" "deposit" "trade"
      Completed         bool         `json:"completed"`            // 是否完成
      TaskConditionValue  int        `json:"task_condition_value"` // 入金/交易要求金额。天数前端写死5
}

// 报名
type HelpSignupReq {
      ConstID         string  `json:"const_id"`                   // 设备指纹
}
type HelpSignupResp {
      NextTime               int64       `json:"next_time"`    // 开始时间
}

// 邀请人领奖
type HelpCouponReq {
      ConstID           string      `json:"const_id"`             // 设备指纹
}
type HelpCouponResp {
      InviterRewardValue  int       `json:"inviter_reward_value"` // 邀请人奖励券额"`
}

// 被邀请人记录
type HelpInviteeRecordReq {
      PerPage                 int         `form:"per_page"`    // 每页显示的记录数量，默认值为10
	Page                    int         `form:"page"`        // 当前页码
}
type HelpInviteeRecordResp {
      InviteeList       []*InviteeRecord `json:"list"`
      Total                   int        `json:"totalCount"`        // 数据总数量
}
type InviteeRecord {
      Uid               int64       `json:"uid"`                 // 被邀请人uid
      RegisterTime        int64     `json:"register_time"`           // 注册时间
      Valid             int         `json:"valid"`                // 是否有效助力 0无效 1有效
      FinishTime        int64       `json:"finish_time"`           // 助力完成时间
}

// 邀请人记录
type HelpInviterRecordReq {
      ProgressStatus          int         `form:"progress_status,optional"` // 1进行中 2已结束
      PerPage                 int         `form:"per_page"`    // 每页显示的记录数量，默认值为10
	Page                    int         `form:"page"`        // 当前页码
}
type HelpInviterRecordResp {
      InviterList             []*InviterRecord       `json:"list"`
      Total                   int                     `json:"totalCount"`        // 数据总数量
}
type InviterRecord {
      StartTime               int64       `json:"start_time"`    // 开始时间
      ProgressStatus          int         `json:"progress_status"` // 助力状态 1进行中 2已结束
      ProgressNumber          int         `json:"progress_number"`  // 助力完成人数
      InviterRewardValue      int         `json:"inviter_reward_value"` // 邀请人奖励券额
      ReceiveStatus           int         `json:"receive_status"` // 领奖状态 0初始化 1未领取 2发放中 3发放成功 4发放失败
}


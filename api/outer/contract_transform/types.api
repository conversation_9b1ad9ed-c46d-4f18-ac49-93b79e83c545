syntax = "v1"

// 获取用户需要展示的任务
type (
    GetUserNeedShowTaskRequest {}

    GetUserNeedShowTaskResponse {
        ActivityId int64 `json:"activity_id"`
        TaskType int64 `json:"task_type"`
        IsGetTask int64 `json:"is_get_task"`
        TaskId int64 `json:"task_id"`
        TaskAmount float64 `json:"task_amount"`
        UserAmountTotal float64 `json:"user_amount_total"`
        PrizeNum int64 `json:"prize_num"`
        UserNeedDealAmount float64 `json:"user_need_deal_amount"`
        TaskRanges []TaskRange `json:"task_ranges"`
        ActivityStartTime int64 `json:"activity_start_time"`
        ActivityEndTime int64 `json:"activity_end_time"`
        CheckInTaskData ActivityCheckInData `json:"check_in_task_data"`
    }

    TaskRange {
        PrizeNum int64 `json:"prize_num"`
        Max int64  `json:"max"`
    }

    ActivityCheckInData {
        Info Info `json:"info"`
        List []ContractTransformCheckInTask `json:"list"`
    }

    Info {
        Number int64 `json:"number"`
        CouponAmount int64 `json:"coupon_amount"`
    }

    ContractTransformCheckInTask {
    	Number            int64   `json:"number"`
    	PrizeType         int64   `json:"prize_type"`
    	PrizeTypeNum      int64   `json:"prize_type_num"`
    	PrizeTypeNumRatio float64 `json:"prize_type_num_ratio"`
    	IsCheckIn         int64   `json:"is_check_in"`
    }





    UserToDoContractTransformTaskRequest {
        TaskId int64 `json:"task_id"`
    }

    UserToDoContractTransformTaskResponse{}
)


syntax = "v1"

import "./types.api"

@server(
	prefix: /api/web/v1/marketing-activity
	group: outer/contract_transform
	middleware: AuthAllowMiddleware
)

service marketing_activity {
    @doc "获取用户需要展示对应人群任务的接口"
    @handler GetUserNeedShowTask
    post /getUserNeedShowTask (GetUserNeedShowTaskRequest) returns (GetUserNeedShowTaskResponse)
}

@server(
	prefix: /api/web/v1/marketing-activity
	group: outer/contract_transform
	middleware: AuthAllowMiddleware
)

service marketing_activity {
    @doc "用户领取任务接口"
    @handler UserToDoContractTransformTask
    post /userToDoContractTransformTask (UserToDoContractTransformTaskRequest) returns (UserToDoContractTransformTaskResponse)
}
syntax = "v1"

type CouponInfo struct {
    Nickname   string `json:"nickname"`
    CouponType string `json:"coupon_type"`
}

type TaskInfo struct {
    TaskID         int    `json:"-"`
    TaskType       string `json:"task_type"`
    TaskStatus     int64  `json:"task_status"`
    InvitedUserNum int64  `json:"invited_user_num"`
    Progress       int64  `json:"progress"`
}

type ActivityInfoResponse struct {
    ActivityStatus     int           `json:"activity_status"`
    Aid                int64         `json:"aid"`
    IsUserParticipated bool          `json:"is_user_participated"`
    StartTime          int64         `json:"start_time"`
    EndTime            int64         `json:"end_time"`
    LatestCouponInfo   []*CouponInfo `json:"latest_coupon_info"`
    TaskInfo           []*TaskInfo   `json:"task_info"`
}

type ParticipateDownloadResponse struct {
}

type GetUserNumsRequest struct {
    AID int64 `form:"aid"`
}

type GetUserNumsResponse struct {
    UserNum int64 `json:"user_num"`
}

type CanAutoParticipateResponse struct {
    CanAutoParticipate bool `json:"can_auto_participate"`
}

type ParticipateRequest struct {
    DownloadActivityCh string `form:"download_activity_ch"`
}
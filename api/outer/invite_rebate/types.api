syntax = "v1"

// 被邀请人记录
type GetRebateActivityReq {
}

type GetRebateActivityResp {
    List       []*Competition `json:"list"`
}

type Competition {
    Id              int64       `json:"id"`
    Weight          int         `json:"weight"`
    ParentId        int64       `json:"parent_id"`   
    Lang            string      `json:"lang"`
    TypeId          int64       `json:"type_id"`
    CompetitionName string      `json:"competition_name"`
    StartAt         int64       `json:"start_at"`   
    EndAt           int64       `json:"end_at"`
    Url             string      `json:"url"`
    Img             string      `json:"img"`
    ImgDark         string      `json:"img_dark"`   
    Hot             int         `json:"hot"`
    Status          int         `json:"status"`
    DeletedAt       int64       `json:"deleted_at"`
    TopId           int64       `json:"top_id"`
    Extra           ExtraInfo   `json:"extra"`

}
type ExtraInfo {
    TitleGroup      TitleGroupInfo      `json:"title_group"`
}
type TitleGroupInfo {
    MasterOneLine       string      `json:"master_one_line"`
    MasterTwoLine       string      `json:"master_two_line"`
    SlaveOneLine        string      `json:"slave_one_line"`
    SlaveTwoLine        string      `json:"slave_two_line"`
    CompetitionTitle    string      `json:"competition_title"`
}
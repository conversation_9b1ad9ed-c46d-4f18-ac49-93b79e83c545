syntax = "v1"

type vipAirdropApplyReq struct {
    Season string `json:"season"`
}

type vipAirdropApplyResp struct {

}

type vipAirdropTaskListReq struct {
    Season string `form:"season"`
}

type vipAirdropTaskListResp struct {
    List []VipAirdropTaskItem `json:"list"`
}

type VipAirdropTaskItem struct {
    Status int `json:"status"`
    TaskId int `json:"task_id"`
    PrizeNum int `json:"prize_num"`
    AmountTotal int `json:"amount_total"`
    AliasName string `json:"alias_name"`
    Min int `json:"min"`
    CompleteTimes int `json:"complete_times"`
    HighlightNextDay bool `json:"highlight_next_day"`
}

type vipAirdropSeasonInfoReq struct {
    Season string `form:"season"`
}

type vipAirdropSeasonInfoResp struct {
    ApplyTotal string `json:"apply_total"`
    AirdropCoin string `json:"airdrop_coin"`
    AirdropCoinUrl string `json:"airdrop_coin_url"`
    AirdropCoinNum string `json:"airdrop_coin_num"`
    CompetitionStartTime int64 `json:"competition_start_time"`
    CompetitionEndTime int64 `json:"competition_end_time"`
}

type vipAirdropUserInfoReq struct {
    Season string `form:"season"`
}

type vipAirdropUserInfoResp struct {
    IsApply bool `json:"is_apply"`
}
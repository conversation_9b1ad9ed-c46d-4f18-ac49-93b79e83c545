syntax = "v1"

import "./types.api"


@server(
	prefix: /api/web/v1/marketing-activity
	group: outer/activity
	middleware: AuthAllowMiddleware
	timeout : 5s
)

service marketing_activity {
    @doc "VipAirdrop报名"
    @handler VipAirdropApply
    post /vipAirdropApply (vipAirdropApplyReq) returns (vipAirdropApplyResp)

    @doc "VipAirdrop任务列表"
    @handler VipAirdropTaskList
    get /vipAirdropTaskList (vipAirdropTaskListReq) returns (vipAirdropTaskListResp)

    @doc "VipAirdrop赛季信息"
    @handler VipAirdropSeasonInfo
    get /vipAirdropSeasonInfo (vipAirdropSeasonInfoReq) returns (vipAirdropSeasonInfoResp)

    @doc "VipAirdrop用户信息"
    @handler VipAirdropUserInfo
    get /vipAirdropUserInfo (vipAirdropUserInfoReq) returns (vipAirdropUserInfoResp)
}

syntax = "v1"

import "./types.api"


@server(
	prefix: /api/inner/v1/marketing-activity
	group: outer/activity
	middleware: AuthAllowMiddleware
	timeout : 5s
)

service marketing_activity {
    @doc "web3后端 NFT铸造验证"
    @handler WebVerifyNftMint
    post /webVerifyNftMint (WebVerifyNftMintReq) returns (WebVerifyNftMintResp)

    @doc "接收web3 nft铸造结果"
    @handler ReceiveMintResult
    post /receiveMintResult (ReceiveMintResultReq) returns (ReceiveMintResultResp)

     @doc "接收任务完成通知"
     @handler ReceiveSubTaskStatus
     post /receiveSubTaskStatus (ReceiveSubTaskStatusReq) returns (ReceiveSubTaskStatusResp)
}

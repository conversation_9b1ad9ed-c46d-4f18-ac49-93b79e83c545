syntax = "v1"


// web3后端 NFT铸造验证请求参数
type WebVerifyNftMintReq {
    ActivityID       int64    `json:"activity_id"`          //互动ID
    ParentNftID      int64    `json:"parent_nft_id"`        //需要透传的NFT ID
    WalletAddress    string   `json:"wallet_address"`       //钱包唯一标识
    OperationID      int64    `json:"operation_id"`         //需要透传的操作ID 裂变这边用于区分用户重复铸造相同的nft的操作
}

// web3后端 NFT铸造验证返回值
type WebVerifyNftMintResp {
    MintStatus      int64    `json:"mint_status"`       //是否可铸造 1:是 2:否
    MintStatusCause string `json:"mint_status_cause"` //不可以铸造原因 可铸造情况下为空
}

// 接收web3 nft铸造结果请求参数
type ReceiveMintResultReq {
	ActivityID    int64         `json:"activity_id"`    // 透传信息 活动ID
	ParentNftID   int64         `json:"parent_nft_id"`  // 需要透传的 NFT ID
	WalletAddress string        `json:"wallet_address"` // 钱包唯一标识
	OperationID   int64         `json:"operation_id"`   // 需要透传的操作 ID 裂变这边用于区分用户重复铸造相同的 nft 的操作
	MintStatus    int           `json:"mint_status"`    // 铸造状态 1:成功 2:失败
	MintResults   []*MintResult `json:"mint_results"`   // 铸造 NFT 的结果
}

// 铸造 NFT 的结果
type MintResult {
	TokenURL string `json:"token_url"` // 回调传回的参数 https://data.gatenft.io/redbullNFT/1 1 对应的就是 nft ID
	TokenID  string `json:"token_id"`  // 回调传回的参数
}

// 接收web3 nft铸造结果返回值
type ReceiveMintResultResp {
    ReceiveStatus      int    `json:"receive_status"`       //接收领取结果状态 1:成功 2:失败
    receiveStatusCause string `json:"receive_status_cause"` //领取失败原因
}

//接收任务完成通知请求参数
type ReceiveSubTaskStatusReq {
   NftUserSubTaskId     int64    `json:"nft_user_sub_task_id"`       // 用户子任务ID
}

//接收任务完成通知请求返回值
type ReceiveSubTaskStatusResp {
   ReceiveStatus     int    `json:"receive_status"`       // 1:接收成功 2接收失败
}


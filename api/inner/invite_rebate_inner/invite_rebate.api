syntax = "v1"

import "./types.api"

@server(
	prefix: /api/inner/v1/marketing-activity
	group: inner/invite_rebate
	timeout : 5s
)

service marketing_activity {
	@doc "获取邀请反佣页活动列表"
	@handler GetRebateActivities
	get /getRebateActivities (GetRebateActivitiesReq) returns (GetRebateActivitiesResp)

	@doc "新增邀请反佣页活动"
	@handler UpsertRebateActivities
	post /upsertRebateActivities (UpsertRebateActivitiesReq) returns (UpsertRebateActivitiesResp)

	@doc "启用邀请反佣页活动"
	@handler OnlineRebateActivities
	post /onlineRebateActivities (OnlineRebateActivitiesReq) returns (OnlineRebateActivitiesResp)

	@doc "禁用邀请反佣页活动"
	@handler OfflineRebateActivities
	post /offlineRebateActivities (OfflineRebateActivitiesReq) returns (OfflineRebateActivitiesResp)

	@doc "删除邀请反佣页活动"
	@handler DeleteRebateActivities
	post /deleteRebateActivities (DeleteRebateActivitiesReq) returns (DeleteRebateActivitiesResp)
}
syntax = "v1"

// 获取邀请反佣页活动列表
type GetRebateActivitiesReq {
    CompetitionName string  `form:"competition_name,optional"`
    Status          int     `form:"status,optional"`             // 活动状态 1未开始 2进行中 3已结束
    ConfigStatus    int     `form:"config_status,optional"`      // 配置状态 1启用 2禁用
    Weight          int     `form:"weight,optional"`             
    ActivityId      int64   `form:"activity_id,optional"`        
}

type GetRebateActivitiesResp {
    List            []*RebateActivity `json:"list"`
}
type RebateActivity struct {
    Id              int64   `json:"id"`
    ActivityId      int64   `json:"activity_id"`
    CompetitionName string  `json:"competition_name"`
    Url             string  `json:"url"`
    Img             string  `json:"img"`
    StartAt         int64   `json:"start_at"`
    EndAt           int64   `json:"end_at"`
    Status          int     `json:"status"`
    ConfigStatus    int     `json:"config_status"`
    Weight          int     `json:"weight"`
}

// 新增/修改邀请反佣页活动
type UpsertRebateActivitiesReq {
    Id              int64   `json:"id"`
    ActivityId      int64   `json:"activity_id"`
    Weight          int     `json:"weight"`
}
type UpsertRebateActivitiesResp {
}

// 启用邀请反佣页活动
type OnlineRebateActivitiesReq {
    Id              int64   `json:"id"`
}
type OnlineRebateActivitiesResp {
}

// 禁用邀请反佣页活动
type OfflineRebateActivitiesReq {
    Id              int64   `json:"id"`
}
type OfflineRebateActivitiesResp {
}

// 删除邀请反佣页活动
type DeleteRebateActivitiesReq {
    Id              int64   `json:"id"`
}
type DeleteRebateActivitiesResp {
}

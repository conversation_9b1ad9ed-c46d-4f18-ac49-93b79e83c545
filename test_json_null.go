package main

import (
	"encoding/json"
	"fmt"
)

type HelpUser struct {
	UserId int64  `json:"user_id"`
	Name   string `json:"name"`
}

func main() {
	var helpUsers []*HelpUser
	
	// 测试 "null" 字符串
	redisVal := "null"
	err := json.Unmarshal([]byte(redisVal), &helpUsers)
	
	fmt.Printf("redisVal: %s\n", redisVal)
	fmt.Printf("err: %v\n", err)
	fmt.Printf("helpUsers: %v\n", helpUsers)
	fmt.Printf("helpUsers == nil: %t\n", helpUsers == nil)
	fmt.Printf("len(helpUsers): %d\n", len(helpUsers))
	
	// 测试空数组
	redisVal2 := "[]"
	var helpUsers2 []*HelpUser
	err2 := json.Unmarshal([]byte(redisVal2), &helpUsers2)
	
	fmt.Printf("\nredisVal2: %s\n", redisVal2)
	fmt.Printf("err2: %v\n", err2)
	fmt.Printf("helpUsers2: %v\n", helpUsers2)
	fmt.Printf("helpUsers2 == nil: %t\n", helpUsers2 == nil)
	fmt.Printf("len(helpUsers2): %d\n", len(helpUsers2))
}

{
  "swagger": "2.0",
  "info": {
    "title": "",
    "version": ""
  },
  "schemes": [
    "http",
    "https"
  ],
  "consumes": [
    "application/json"
  ],
  "produces": [
    "application/json"
  ],
  "paths": {
    git reset --soft HEAD~1    "/api/web/v1/marketing-activity/getRebeteActivity": {
      "get": {
        "summary": "分页查询活动列表",
        "operationId": "GetRebeteActivity",
        "responses": {
          "200": {
            "description": "A successful response.",
            "schema": {
              "$ref": "#/definitions/GetRebateActivityResp"
            }
          }
        },
        "tags": [
          "outer/invite_rebate"
        ]
      }
    }
  },
  "definitions": {
    "Competition": {
      "type": "object",
      "properties": {
        "id": {
          "type": "integer",
          "format": "int64"
        },
        "parent_id": {
          "type": "integer",
          "format": "int64"
        },
        "lang": {
          "type": "string"
        },
        "type_id": {
          "type": "integer",
          "format": "int64"
        },
        "competition_name": {
          "type": "string"
        },
        "start_at": {
          "type": "integer",
          "format": "int64"
        },
        "end_at": {
          "type": "integer",
          "format": "int64"
        },
        "url": {
          "type": "string"
        },
        "img": {
          "type": "string"
        },
        "img_dark": {
          "type": "string"
        },
        "hot": {
          "type": "integer",
          "format": "int32"
        },
        "status": {
          "type": "integer",
          "format": "int32"
        },
        "deleted_at": {
          "type": "integer",
          "format": "int64"
        },
        "top_id": {
          "type": "integer",
          "format": "int64"
        },
        "extra": {
          "$ref": "#/definitions/ExtraInfo"
        }
      },
      "title": "Competition",
      "required": [
        "id",
        "parent_id",
        "lang",
        "type_id",
        "competition_name",
        "start_at",
        "end_at",
        "url",
        "img",
        "img_dark",
        "hot",
        "status",
        "deleted_at",
        "top_id",
        "extra"
      ]
    },
    "ExtraInfo": {
      "type": "object",
      "properties": {
        "title_group": {
          "$ref": "#/definitions/TitleGroupInfo"
        }
      },
      "title": "ExtraInfo",
      "required": [
        "title_group"
      ]
    },
    "GetRebateActivityReq": {
      "type": "object",
      "title": "GetRebateActivityReq"
    },
    "GetRebateActivityResp": {
      "type": "object",
      "properties": {
        "list": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/Competition"
          }
        }
      },
      "title": "GetRebateActivityResp",
      "required": [
        "list"
      ]
    },
    "TitleGroupInfo": {
      "type": "object",
      "properties": {
        "master_one_line": {
          "type": "string"
        },
        "master_two_line": {
          "type": "string"
        },
        "slave_one_line": {
          "type": "string"
        },
        "slave_two_line": {
          "type": "string"
        },
        "competition_title": {
          "type": "string"
        }
      },
      "title": "TitleGroupInfo",
      "required": [
        "master_one_line",
        "master_two_line",
        "slave_one_line",
        "slave_two_line",
        "competition_title"
      ]
    }
  },
  "securityDefinitions": {
    "apiKey": {
      "type": "apiKey",
      "description": "Enter JWT Bearer token **_only_**",
      "name": "Authorization",
      "in": "header"
    }
  }
}

package utils

import (
	"encoding/json"
	"fmt"
	"testing"
)

func TestCalcParam(t *testing.T) {
	originStr := "abcd"
	originStrBase64 := Base64Encode(originStr)
	md5Str, err := CalcMd5Test(originStrBase64)
	if err != nil {
		t.Error(err)
		return
	}

	m := make(map[string]interface{})
	m["business"] = 1
	m["paramStr"] = originStrBase64
	m["sign"] = md5Str

	paramJson, _ := json.Marshal(m)
	fmt.Println(string(paramJson))
}

package utils

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/marketing"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	"context"
	"fmt"
	"strings"
	"sync"
)

type TaskList []task.Task
type TaskIdScheduleMap map[int64]*task.RecordScheduleResponse
type UserInCrowdMap map[int64]bool

func (t TaskList) GetTaskIds() []int64 {
	ids := make([]int64, 0)
	for _, item := range t {
		ids = append(ids, item.TaskID)
	}
	return ids
}

// BatchTaskInfo 批量获取任务信息、任务进度和用户人群信息。
//
// 参数:
//
//	ctx         请求上下文，用于控制超时或取消操作。
//	uid         用户 UID，用于标识请求用户。
//	businessId  业务唯一标识，用于区分不同业务场景。
//	TaskIds     任务 ID 字符串，多个任务 ID 以逗号分隔，例如 "101,102,103"。
//	businessType 业务类型，用于匹配具体的任务分类。
//	chanNum     并发协程数，限制同时发起的任务进度查询数量。
//
// 返回:
//
//	taskList              任务列表。
//	taskRecordScheduleMap 任务进度信息映射（map[taskID]进度信息）。
//	userCrowdInfo         用户所属人群信息。
//	err                   错误信息，若成功为 nil。
func BatchTaskInfo(ctx context.Context, uid int64, businessId, TaskIds string, businessType int64, chanNum int) (taskList TaskList, taskRecordScheduleMap TaskIdScheduleMap, userCrowdInfo UserInCrowdMap, err error) {
	taskList, err = BatchTaskList(ctx, uid, businessId, TaskIds, businessType)
	if err != nil {
		return
	}
	if len(taskList) == 0 {
		return
	}
	taskRecordScheduleMap, err = BatchGetTaskRecordSchedule(ctx, taskList.GetTaskIds(), uid, businessType, businessId, chanNum)
	if err != nil {
		return
	}
	userCrowdInfo, err = UserInCrowdInfo(ctx, uid, taskList)
	if err != nil {
		return
	}
	return
}

// BatchTaskList 批量获取指定业务下的任务列表。
//
// 参数:
//
//	ctx         上下文，用于控制超时和取消操作。
//	uid         用户 UID，用于标识当前操作用户。
//	businessId  业务唯一标识字符串，用于区分不同的业务场景。
//	TaskIds     任务 ID 列表，多个任务 ID 使用英文逗号分隔，例如 "101,102,103"。
//	businessType 业务类型 ID，用于指定任务所属的业务分类。
//
// 返回:
//
//	TaskList 任务列表，包含所有符合条件的任务信息。
//	error    错误信息，如无错误则为 nil。
func BatchTaskList(ctx context.Context, uid int64, businessId, TaskIds string, businessType int64) (TaskList, error) {
	taskCli := task.NewClient()
	taskResponse, err := taskCli.BatchTaskAll(ctx, &task.BatchTaskAllRequest{
		UserID:       uid,
		TaskIDs:      TaskIds,
		BusinessType: businessType,
		BusinessID:   businessId,
	})
	if err != nil {
		logx.Errorf("BatchTaskList err:", err)
		return nil, err
	}
	return taskResponse.List, nil
}

// BatchGetTaskRecordSchedule 并发获取用户在多个任务下的进度信息。
//
// 参数:
//
//	ctx          上下文对象，用于控制请求超时或取消操作。
//	taskIds      任务 ID 列表，需查询的任务 ID 切片。
//	userId       用户 ID，用于查询该用户对应的任务记录进度。
//	businessType 业务类型，用于区分不同的业务模块或场景。
//	businessID   业务唯一标识字符串，用于进一步标识具体业务实体。
//	chanNum      并发协程数量限制，控制并发获取数据的最大线程数。
//
// 返回:
//
//	TaskIdScheduleMap 任务 ID 到记录进度信息的映射结果。
//	error             错误信息，如无错误则为 nil。
func BatchGetTaskRecordSchedule(ctx context.Context, taskIds []int64, userId, businessType int64, businessID string, chanNum int) (TaskIdScheduleMap, error) {
	result := make(TaskIdScheduleMap)
	if userId == 0 || len(taskIds) == 0 {
		return result, nil
	}
	var (
		mu        sync.Mutex
		wg        sync.WaitGroup
		semaphore = make(chan struct{}, chanNum)
		errs      []error
	)
	for _, taskId := range taskIds {
		tid := taskId
		wg.Add(1)
		go func() {
			semaphore <- struct{}{}
			defer func() {
				<-semaphore
				wg.Done()
			}()
			taskCli := task.NewClient()
			taskResp, err := taskCli.RecordSchedule(ctx, &task.RecordScheduleRequest{
				TaskID:       tid,
				UserID:       userId,
				BusinessType: businessType,
				BusinessID:   businessID,
			})
			if err != nil {
				errs = append(errs, fmt.Errorf("taskId %d: %w", tid, err))
				return
			}
			mu.Lock()
			result[tid] = taskResp
			mu.Unlock()
		}()
	}
	wg.Wait()
	if len(errs) > 0 {
		errMsgs := make([]string, 0, len(errs))
		for _, e := range errs {
			errMsgs = append(errMsgs, e.Error())
		}
		return result, fmt.Errorf("BatchGetTaskRecordSchedule multiple errors: %s", strings.Join(errMsgs, "; "))
	}
	return result, nil
}

// UserInCrowdInfo 获取指定用户在任务列表中所属人群（圈层）信息。
//
// 参数:
//
//	ctx       上下文对象，用于控制请求超时或中断等操作。
//	uid       用户 ID，目标用户的唯一标识。
//	taskList  任务列表，包含需要查询人群信息的任务集合。
//
// 返回:
//
//	UserInCrowdMap 用户在人群中的映射关系（任务 ID => 所属人群信息）。
//	error          错误信息，若查询成功则为 nil。
func UserInCrowdInfo(ctx context.Context, uid int64, taskList TaskList) (UserInCrowdMap, error) {
	result := make(UserInCrowdMap)
	if uid == 0 {
		return result, nil
	}
	mc := marketing.NewClient()
	for _, item := range taskList {
		if item.CrowdID > 0 {
			crowdRes, err := mc.CheckUserInCrowd(ctx, &marketing.CheckUserInCrowdRequest{CrowdID: item.CrowdID, UserID: uid})
			if err != nil {
				logx.Errorf("UserInCrowdInfo err:%v", err)
				return nil, err
			}
			result[item.TaskID] = crowdRes.UserInCrowd
		}
	}
	return result, nil
}

package utils

import (
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/engine/auth"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/svc"
	"math/rand"
	"strings"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"
)

const (
	alphabet = "q3XJvR6kLpDfHw9yZ1Kb7Nc4Tg5U8Vm2A0eSdQYxPhIjOnWlMtaGzFsBrEuCo"
	length   = uint64(len(alphabet))
)

// Encode 将数字编码为62进制字符串
func Encode(n uint64) string {
	if n == 0 {
		return string(alphabet[0])
	}

	var sb strings.Builder
	for n > 0 {
		sb.WriteByte(alphabet[n%length])
		n = n / length
	}

	// 反转字符串
	runes := []rune(sb.String())
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}

	return string(runes)
}

// Decode 将62进制字符串解码为数字
func Decode(s string) (uint64, error) {
	var n uint64
	for _, c := range s {
		index := strings.IndexRune(alphabet, c)
		if index == -1 {
			return 0, fmt.Errorf("invalid character: %c", c)
		}
		n = n*length + uint64(index)
	}
	return n, nil
}

/**
 * 生成唯一的订单ID
 */
func GetOrderId(ctx context.Context, svcCtx *svc.ServiceContext) (int64, error) {
	// 生成一个随机订单ID
	currentTime := time.Now().Unix()

	var incrNumber int64

	// 先尝试从Redis获取自增数字
	incrNumber, err := svcCtx.Redis.IncrCtx(ctx, consts.NftOrderId)
	if err != nil {
		logx.Info("Redis操作失败，使用随机数作为fallback err: ", err)

		// Redis操作失败时，使用随机数作为fallback
		rng := rand.New(rand.NewSource(time.Now().UnixNano())) // 创建局部随机数生成器
		incrNumber = rng.Int63n(9000) + 1000                   // 生成1000-9999之间的随机数
	} else {
		// Redis操作成功，设置过期时间
		svcCtx.Redis.Expire(consts.NftOrderId, 1)
	}

	// 生成订单ID
	orderId := currentTime*10000 + incrNumber

	// 返回ID
	return orderId, nil
}

/**
 * 特殊md5计算-测试用
 */
func CalcMd5Test(originStr string) (string, error) {
	hash := md5.Sum([]byte(originStr + alphabet))
	hashStr := hex.EncodeToString(hash[:])
	return hashStr, nil
}

// Base64Encode 将输入字符串编码为Base64字符串
func Base64Encode(input string) string {
	// 将字符串转换为字节切片
	data := []byte(input)
	// 使用StdEncoding进行Base64编码
	encoded := base64.StdEncoding.EncodeToString(data)
	return encoded
}

func GetUserInfo(ctx context.Context) (*auth.UserInfo, error) {
	request, ok := requestools.GetRequestFromCtx(ctx)
	if !ok {
		logx.Infof("GetUserInfo not get request from context")
		return nil, errors.New(-1, "not get request from context")
	}
	return requestools.GetUserInfo(request), nil
}

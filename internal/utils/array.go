package utils

import (
	"fmt"
	"strings"
)

// 判断字符串是否在数组中.
func InArray(needle string, haystack []string) bool {
	for _, item := range haystack {
		if item == needle {
			return true
		}
	}
	return false
}

// Int64InSlice 判断整数是否在切片中
func Int64InSlice(needle int64, haystack []int64) bool {
	for _, item := range haystack {
		if item == needle {
			return true
		}
	}
	return false
}

// FindUniqueElements 找出在 slice1 中且不在 slice2 中的元素
func FindUniqueElements(slice1, slice2 []int64) []int64 {
	// 用于存储 slice2 中的元素，方便快速查找
	elementMap := make(map[int64]bool)
	for _, element := range slice2 {
		elementMap[element] = true
	}

	var result []int64
	// 遍历 slice1
	for _, element := range slice1 {
		// 如果该元素不在 slice2 中，则添加到结果切片中
		if !elementMap[element] {
			result = append(result, element)
		}
	}
	return result
}

// Intersection 函数用于求取两个整数切片的交集
func Intersection(slice1, slice2 []int64) []int64 {
	// 为了减少空间使用，选择较短的切片存入哈希表
	if len(slice1) > len(slice2) {
		slice1, slice2 = slice2, slice1
	}
	elementMap := make(map[int64]bool, len(slice1))
	var result []int64

	// 将较短切片中的元素添加到 map 中
	for _, v := range slice1 {
		elementMap[v] = true
	}

	// 遍历较长切片，检查元素是否存在于 map 中
	for _, v := range slice2 {
		if elementMap[v] {
			result = append(result, v)
			// 避免重复添加相同元素
			delete(elementMap, v)
		}
	}

	return result
}

// 将int64列表转化成字符串
func IntListToStr(nums []int64) string {
	if len(nums) == 0 {
		return ""
	}

	strs := make([]string, len(nums))
	for i, num := range nums {
		strs[i] = fmt.Sprintf("%d", num)
	}

	return strings.Join(strs, ",")
}

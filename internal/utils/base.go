package utils

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"time"

	"bitbucket.org/gateio/gateio-lib-base-go/environment"
)

// SecureRandomInt 生成安全的随机数.
func SecureRandomInt(max int64) (int64, error) {
	n, err := rand.Int(rand.Reader, big.NewInt(max))
	if err != nil {
		return 0, err
	}
	return n.Int64(), nil
}

// CopyMap 复制一个map[string]interface{}类型的map.
func CopyMap(original map[string]interface{}) map[string]interface{} {
	// 创建一个新的map
	copied := make(map[string]interface{}, len(original))
	// 逐个复制原map中的键值对
	for key, value := range original {
		copied[key] = value
	}
	return copied
}

func InStringSlice(slice []string, val string) bool {
	for _, item := range slice {
		if item == val {
			return true
		}
	}
	return false
}

// CheckDev 是否为测试环境
func CheckDev() bool {
	if environment.IsProd() || environment.IsPre() {
		return false
	}
	return true
}

// UnifyTopic 获取对应环境的topic名称
func UnifyTopic(topic string) string {
	if environment.IsPre() {
		topic = fmt.Sprintf("%s-Pre", topic)
	}
	return topic
}

// 生成一个唯一ID（时间戳+随机数）
func GetUnionId(randNumberMax int64) (int64, error) {
	// 获取当前时间戳
	currentTime := time.Now().Unix()

	// 生成4位随机数
	randomNumber, err := SecureRandomInt(randNumberMax) // 范围内的随机数
	if err != nil {
		return 0, err
	}

	// 组合成订单号
	orderId := currentTime*10000 + int64(randomNumber)
	return orderId, nil
}

package utils

import (
	"math/rand"
	"time"
)

var imageUrls = []string{
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-ed1e42be-0a3e-4b4a-b8e0-673546982645.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-1d768ab3-c193-4ef3-8dbe-d929feb8d9e8.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-67d81cfb-4260-4d1c-9642-95b9513d57b9.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-06d01082-2f6a-4a14-ab1f-ffc61bfd2180.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-5c872b65-8170-49e8-a065-55b09d9ef88e.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-e792ea6c-ab38-4093-bdcb-c6603fed85ef.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-9a0c7596-20bf-487d-992d-dfd7733d8054.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-a6ae0a30-f6fa-4ae4-bc17-3565f88b0281.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-de1ee16d-6b3a-431f-9e12-798541e81deb.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-4d0f411a-7f20-41af-aaa9-d1fcfce1b57d.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-44476c04-0516-4f91-82da-e46f63e39ad0.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-47f1a865-55bb-4978-87ee-09ec86de8897.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-e6e31ddc-af3e-4e09-bbe0-6df68369dea1.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-a8a970b2-20b5-4825-85cc-720def20518d.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-fc562281-fbfc-4023-9544-a505352697ac.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-a8a39809-3919-443a-95ea-cd224928b447.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-5f4c54bc-8986-430b-83d2-e849836f1a6e.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-109c21d0-160e-420e-9ffe-352cdfad7339.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-38299feb-3280-48d2-941d-debdd015837c.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-b4de995e-c342-455c-9056-740669645dba.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-413fa1d2-6394-40ac-b36c-afbbe33a4c2d.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-82c12a60-8a18-450f-a733-dcfc1dc7f7d6.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-2e2ecc77-c7a2-4456-8a5c-de0eb3a28166.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-10e8de25-aced-4f61-9c60-05a7046d2e10.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-40ecfe24-51d7-4ad8-88ea-a7ea68cc71f4.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-435042ec-923e-4e23-b40a-8b8f2eeb72e0.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-49b58319-7cce-4535-bdd6-65d196848b16.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-774b89c9-0647-4e14-9d90-0187cbbe5ab9.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-6f8fe983-2d5c-4a04-8f1a-e117d98a1b7c.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-242f0983-9fd2-4b08-b703-ff6121f40600.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-e884b297-bb15-4a96-86c5-570b939c3222.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-3f400120-4fee-4e6e-8cbe-31ee1776a668.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-00aaa0dd-8c00-4441-8373-b839c6587df4.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-39fbf958-c425-401c-92f7-054f7bc7878c.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-fec221ce-2f81-4601-b7c3-066efb2e632c.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-26fa894c-a27e-46f2-9d38-075f608afb4b.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-f81c4761-f089-4cf1-a52f-c5e778e67e86.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-e94ad0ee-5743-4593-b0ae-066d1b8204d0.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-8c184ecc-90dc-46e1-9261-1f7fb5a6f44e.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-7dfec2d6-326f-40b4-a0d1-27b9e60a427c.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-32922d17-901e-48d2-8c37-a887ac940218.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-9a07e534-a08d-4a65-8657-1da74daec865.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-cb3df185-ad68-4f8c-bf52-72c63095da18.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-df962751-e18f-46d9-a829-58898eeb99b8.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-5f1d47ea-ed88-48c2-8ad4-0dd11ff17460.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-1c7d1eac-520a-4fd1-a621-322722770276.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-cead0c01-d6e5-4615-b7bd-af314823fe2c.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-73f09c05-2162-4197-a7a0-18d6a6244627.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-cd445b80-81cd-4639-bb75-24be28bf5072.webp",
	"https://gavatar.gateimg.com/marketing_manager/2025-04-08/20250408-04fda992-9db5-402f-bfce-1c24dd5ca4e9.webp",
}

/**
 * 随机返回一张图片
 */
func GetRandomImageUrl() string {
	// 创建局部随机数生成器
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))

	// 返回随机图片 URL
	return imageUrls[rng.Intn(len(imageUrls))]
}

/**
 * 根据钱包地址返回特定的图片地址
 */
func GetImageUrlByWallet(walletAddress string) string {
	// 根据钱包地址的哈希值选择图片索引（示例）
	index := hash(walletAddress) % len(imageUrls)
	return imageUrls[index]
}

// 哈希函数示例（您可以根据需要实现具体的哈希逻辑）
func hash(s string) int {
	// 这里可以使用简单的哈希算法
	h := 0
	for _, char := range s {
		h += int(char)
	}
	return h
}

/**
 * 截取展示钱包地址
 */
func AbbreviateString(s string) string {
	// 检查是否为纯数字字符串
	isPureNumber := true
	for _, char := range s {
		if char < '0' || char > '9' {
			isPureNumber = false
			break
		}
	}
	if isPureNumber {
		return s
	}

	// 隐藏处理
	if len(s) <= 10 {
		return s
	}
	return s[:6] + "..." + s[len(s)-4:]
}

package utils

import (
	"fmt"
	"time"
)

const (
	DateYmdFormat  = "20060102"
	DateFormat     = "2006-01-02"
	TimeFormat     = "15:04:05"
	DateTimeFormat = "2006-01-02 15:04:05"
)

// TimeUtil 封装了 time 包的一些常用功能
type TimeUtil struct{}

// Now 返回当前时间
func (tu *TimeUtil) Now() time.Time {
	return time.Now()
}

// GetNowTime 获取当前时间戳
func (tu *TimeUtil) GetNowTime(t time.Time) int64 {
	return t.Unix()
}

func (tu *TimeUtil) GetNowMilliTime(t time.Time) int64 {
	return t.UnixMilli()
}

func (tu *TimeUtil) FormatTime(t time.Time) string {
	return t.Format(DateTimeFormat)
}

func (tu *TimeUtil) FormatYmd(t time.Time) int64 {
	return MustInt64(t.Format(DateYmdFormat), 0)
}

func TransformDateStringToTime(timeStr string) (time.Time, error) {
	// 解析时间字符串为 time.Time 类型
	t, err := time.Parse(DateTimeFormat, timeStr)
	if err != nil {
		fmt.Println("Error parsing time:", err)
		return time.Time{}, err
	}
	return t, nil
}

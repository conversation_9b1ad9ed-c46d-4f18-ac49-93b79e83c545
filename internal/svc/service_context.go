package svc

import (
	"context"

	"gateio_service_marketing_activity/internal/config"
	"gateio_service_marketing_activity/internal/middleware"
	"gateio_service_marketing_activity/internal/models/contract_transform_model"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/core/proc"
	"bitbucket.org/gatebackend/go-zero/core/stores/redis"
	"bitbucket.org/gatebackend/go-zero/rest"
	"bitbucket.org/gatebackend/gorm-zero/gormc/config/mysql"
	"bitbucket.org/gateio/gateio-lib-base-go/encoding"
	cmiddleware "bitbucket.org/gateio/gateio-lib-common-go/middleware"
	"gorm.io/gorm"
)

type ServiceContext struct {
	Config                     *config.Config
	waitForCalled              func()
	JsonHandler                encoding.Codec
	Redis                      *redis.Redis
	NftRedis                   *redis.Redis
	DBMarketingActivities      *gorm.DB
	DBCompetitions             *gorm.DB
	DBActivities               *gorm.DB
	DBNft                      *gorm.DB
	FeeSettingDB               *gorm.DB
	KafkaConf                  *config.KafkaConf
	UploadKafkaConf            *config.KafkaConf // 上报打点的kafka配置
	CallWebTaskServiceUrl      string
	CallWebWalletNftServiceUrl string
	// 中间件
	Signature           rest.Middleware // 验签中间件，验证请求数据的完整性和合法性，常用于防止数据篡改和伪造请求
	Authorization       rest.Middleware // 身份认证授权中间件，验证用户身份和访问权限，保护受限资源
	AuthMiddleware      rest.Middleware
	AuthAllowMiddleware rest.Middleware
	RiskServiceUrl      string
	RiskGatewayAppId    string
	MailHeraldMailUrl   string

	// model
	ContractTransformUserTaskModel contract_transform_model.ContractTransformUserTaskModel
	SpotEngine                     config.SpotEngine
}

func NewServiceContext(c *config.Config) *ServiceContext {
	svcCtx := &ServiceContext{}
	ctx := context.Background()
	logc.Infof(ctx, "NewServiceContext")

	// 营销活动数据库
	dBMarketingActivities, err := mysql.Connect(c.MysqlMarketingActivities)
	if err != nil {
		logx.Errorf("mysql MarketingActivities connection is not initialized, err: %v", err)
	}
	// 营销活动数据库
	dBCompetitions, err := mysql.Connect(c.MysqlCompetitionCenter)
	if err != nil {
		logx.Errorf("mysql Competitions connection is not initialized, err: %v", err)
	}

	// 活动数据库
	dBActivities, err := mysql.Connect(c.MysqlActivities)
	if err != nil {
		logx.Errorf("mysql MarketingActivities connection is not initialized, err: %v", err)
	}

	nftDB, err := mysql.Connect(c.MysqlNft)
	if err != nil {
		logx.Errorf("mysql Nft connection is not initialized, err: %v", err)
	}

	dBFeeSetting, err := mysql.Connect(c.MysqlFeeSettings)
	if err != nil {
		logx.Errorf("mysql MysqlFeeSetting connection is not initialized, err: %v", err)
	}

	// 初始化Redis
	logx.Must(c.Redis.Validate())
	rds := redis.MustNewRedis(c.Redis)
	logc.Infof(ctx, "redis connected")

	logx.Must(c.NftRedis.Validate())
	rdsNft := redis.MustNewRedis(c.NftRedis)
	logc.Infof(ctx, "redis of nft connected")

	// 用来监听 os.Signals, 当收到信号后关闭资源
	waitForCalled := proc.AddShutdownListener(func() {
		logx.Info("closing resources...")
		marketingActivitiesDb, err := dBMarketingActivities.DB()
		if err != nil {
			logx.Errorf("获取 dBMarketingActivities 数据库链接失败, err: %v", err)
			return
		}
		err = marketingActivitiesDb.Close()
		if err != nil {
			logx.Errorf("关闭 dBMarketingActivities 数据库链接失败, err: %v", err)
		}
		if err != nil {
			return
		}

		activitiesDb, err := dBActivities.DB()
		if err != nil {
			logx.Errorf("获取 dBActivities 数据库链接失败, err: %v", err)
			return
		}
		err = activitiesDb.Close()
		if err != nil {
			logx.Errorf("关闭 dBActivities 数据库链接失败, err: %v", err)
		}
		if err != nil {
			return
		}

		feeSetingDb, err := dBFeeSetting.DB()
		if err != nil {
			logx.Errorf("获取 feeSetingDb 数据库链接失败, err: %v", err)
			return
		}
		err = feeSetingDb.Close()
		if err != nil {
			logx.Errorf("关闭 feeSetingDb 数据库链接失败, err: %v", err)
			return
		}

		competitionsDb, err := dBCompetitions.DB()
		if err != nil {
			logx.Errorf("获取 competitionsDb 数据库链接失败, err: %v", err)
			return
		}
		err = competitionsDb.Close()
		if err != nil {
			logx.Errorf("关闭 competitionsDb 数据库链接失败, err: %v", err)
			return
		}
	})
	svcCtx = &ServiceContext{
		Config:                     c,
		waitForCalled:              waitForCalled,
		Redis:                      rds,
		NftRedis:                   rdsNft,
		DBMarketingActivities:      dBMarketingActivities,
		DBCompetitions:             dBCompetitions,
		DBActivities:               dBActivities,
		DBNft:                      nftDB,
		FeeSettingDB:               dBFeeSetting,
		KafkaConf:                  c.KafkaConf,
		CallWebTaskServiceUrl:      c.CallWebTaskServiceUrl,
		CallWebWalletNftServiceUrl: c.CallWebWalletNftServiceUrl,
		// 中间件
		Signature:           cmiddleware.Signature(),
		Authorization:       cmiddleware.Authorize(),
		AuthMiddleware:      middleware.NewAuthMiddleware().Handle,
		AuthAllowMiddleware: middleware.NewAuthAllowMiddleware().Handle,
		RiskServiceUrl:      c.RiskServiceUrl,
		RiskGatewayAppId:    c.RiskGatewayAppId,
		MailHeraldMailUrl:   c.MailHeraldMailUrl,

		// 数据表模型
		ContractTransformUserTaskModel: contract_transform_model.NewContractTransformUserTaskModel(dBMarketingActivities),
		SpotEngine:                     c.SpotEngine,
		UploadKafkaConf:                c.UploadKafkaConf,
	}
	return svcCtx
}

func (sc *ServiceContext) Close() {
	if sc.waitForCalled != nil {
		sc.waitForCalled()
	}
}

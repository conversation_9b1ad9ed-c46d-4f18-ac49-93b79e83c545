package types

type GetUserIdentityResp struct {
	IsMarketplace  int    `json:"is_marketplace"`    // 是否做市商用户 1:是 (切换为：mm_white_list_new表)
	IsEnterprise   int    `json:"is_enterprise"`     // 是否企业用户 1:是
	IsSub          int    `json:"is_sub"`            // 是否子账号 1:是
	AgencyType     int    `json:"agency_type"`       //  agency_type 代理商类型 0: 普通用户 1: 代理商 2: 代理商推荐注册用户 3:合伙人身份；4:合伙人邀请的用户
	Region         string `json:"region"`            // 用户区域
	IsBlack        int    `json:"is_black"`          // 是否是黑名单地区用户 1:是 （取config数据）
	UserIdentity   int    `json:"user_identity"`     // 用户新老客身份 1:新客 2:老客 3:旧福利中心新客 0:身份未知 （快照信息）
	NewUserEndTime int64  `json:"new_user_end_time"` // 新客结束时间戳 秒（快照信息）
	IsVisitNewbie  int    `json:"is_visit_newbie"`   // 是否访问过新客福利中心（访问新客接口）1是0否
	IsPopUp        int    `json:"is_pop_up"`         // 是否弹过弹窗 1是0否
}

type HeraldMessageParams struct {
	App          string            `json:"app"`
	Id           string            `json:"id"`
	Methods      []string          `json:"methods"`
	TemplateArgs []string          `json:"template_args"`
	Users        []string          `json:"users"`
	Extras       map[string]string `json:"extras"`
}

type HelpTaskDetail struct {
	Accumulated int    `json:"accumulated"`
	Spot        int    `json:"spot"`
	Future      int    `json:"future"`
	CouponId    int64  `json:"coupon_id"`
	Source      string `json:"source"`
}

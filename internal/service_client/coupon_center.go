package service_client

import (
	"context"
	"fmt"

	"gateio_service_marketing_activity/internal/utils"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/coupon"
)

type CouponCenterCall struct {
	logx.Logger
	ctx context.Context
}

func NewCouponCenterCall(ctx context.Context) *CouponCenterCall {
	return &CouponCenterCall{Logger: logx.WithContext(ctx), ctx: ctx}
}

// 发放卡劵奖励
func (cc *CouponCenterCall) SendCouponById(uid, couponId int64, requestID, amount, source string) (resp *coupon.SendCouponByIdResponse, err error) {
	date := utils.TimeUtil{}
	milliTime := date.GetNowMilliTime(date.Now())
	req := coupon.SendCouponByIdRequest{
		UserID:    uid,
		CouponID:  couponId,
		Source:    source,
		OnlyID:    fmt.Sprintf("%d_%d", uid, milliTime),
		RequestID: fmt.Sprintf("%d_%s", uid, requestID),
		Amount:    amount,
	}
	resp = &coupon.SendCouponByIdResponse{}
	logx.Info("SendCouponById req is: %v", req)
	resp, err = coupon.NewClient().SendCouponById(cc.ctx, &req)
	logx.Info("SendCouponById resp is: %v", resp)

	return resp, err
}

package service_client

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"

	"bitbucket.org/gatebackend/go-zero/core/logx"
)

func SendHeraldMessages(svcCtx *svc.ServiceContext, param *types.HeraldMessageParams) (bool, error) {
	paramBytes, _ := json.Marshal(param)
	url := fmt.Sprintf("%s/messages", svcCtx.MailHeraldMailUrl)

	// 创建新的 HTTP 请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(paramBytes))
	if err != nil {
		logx.Infof("SendHeraldMessages NewRequest is err,err is:%v", err)
		return false, err
	}

	// 设置 Content-Type 头
	req.Header.Set("Content-Type", "application/json")

	// 创建 HTTP 客户端并发送请求
	client := &http.Client{}
	// 测试环境忽略证书验证
	if utils.CheckDev() {
		// 创建一个忽略证书验证的 HTTP 客户端
		client = &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true, // 跳过证书验证
				},
			},
		}
	}

	resp, err := client.Do(req)
	if err != nil {
		logx.Infof("SendHeraldMessages sending request is err,err is:%v", err)
		return false, err
	}
	defer resp.Body.Close()
	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logx.Infof("SendHeraldMessages ioutil.ReadAll is err,err is:%v", err)
		return false, err
	}
	// 打印响应
	logx.Infof("SendHeraldMessages bady is:%s", string(body))
	// 打印响应状态码
	fmt.Println("Response status:", resp.Status)

	if resp.StatusCode != 204 {
		logx.Infof("SendHeraldMessages status error, code is:%d", resp.StatusCode)
		return false, fmt.Errorf("status error, code is: %d", resp.StatusCode)
	}

	return true, nil
}

package activity

import (
	"gateio_service_marketing_activity/internal/utils"
	"time"
)

// 营销活动id
const (
	ContractTransformActivityId = 1 + iota
)

// 业务来源
const ContractTransformTaskSource = "marketing_activity"

// 对应任务系统的业务枚举值
const ContractTransformTaskBusinessType = 11

// 红牛NFT落地页
const NftTaskBusinessType = 15

// vip空投
const VipAirdropBusinessType = 20

// 业务方--合约交易量任务
const NoContractNewUserTaskId int64 = 1
const SilenceUserTaskId int64 = 2

// 任务系统对应的任务id
const SysNoContractNewUserTaskIdDev int64 = 498
const SysSilenceUserTaskIdDev int64 = 505
const SysNoContractNewUserTaskIdProd int64 = 543
const SysSilenceUserTaskIdProd int64 = 544

const ContractTransformActivityStartTimeInt int64 = 1733760000 // 2024-12-10 00:00:00
const ContractTransformActivityEndTimeInt int64 = 1738166400   // 2025-01-30 00:00:00

// 人群id
const NoContractNewUserCrowdIdDev = 382 // 测试环境-未合约交易的新用户人群id
const NoContractNewUserCrowdIdProd = 72 // 生产环境-未合约交易的新用户人群id
const SilenceUserCrowdIdDev = 383       // 测试环境-沉默用户人群id
const SilenceUserCrowdIdProd = 70       // 生产环境-沉默用户人群id
const BlastUserCrowdIdDev = 384         // 测试环境-爆仓新用户人群id
const BlastUserCrowdIdProd = 71         // 生产环境-爆仓新用户人群id

const MockUserId = 123       // 虚构的用户id
const MockBusinessId = "123" // 虚构的业务任务id

// 活动时间
type ActivityTimeInfo struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}

// 活动时间区间，字符串格式
type ActivityTimeRange struct {
	StartTimeInt int64 `json:"start_time_int"`
	EndTimeInt   int64 `json:"end_time_int"`
}

// 前端应展示任务类型
const ShowTaskTypeCouponUser = 1          // 新用户未合约交易用户，展示卡劵页面
const ShowTaskTypeAlreadyContractUser = 2 // 新用户已合约交易用户，展示对应交易量任务
const ShowTaskTypeSilenceUser = 3         // 沉默用户，对应对应交易量任务
const ShowTaskTypeBlastUser = 4           // 爆仓用户，展示签到打卡任务
const ShowTaskTypeNotInCrowdUser = 5      // 非圈群用户，不展示任务

// PrizeType:奖品类型 1无奖励 2卡券
const CheckInPrizeTypeNoPrize = 1 // 无奖励
const CheckInPrizeTypeCoupon = 2  // 卡券

// 合约交易任务进度数据的redis-key
const ContractTransformTRSData = "ContractTransformTaskRecordSchedule:%d:%d"

// 检查用户是否在某个人群当中的redis-key
const UserIsInCrowdData = "UserIsInCrowd:%d:%d"

// 合约交易任务批量查询缓存数据的redis-key
const ContractTransformTaskSysTaskInfoList = "ContractTransformTaskSysTaskInfoList:%s:%d"

// 任务基本配置
type ActivityBaseConf struct {
	ActivityId    int    `json:"activity_id"`    // 活动id
	ActivityTitle string `json:"activity_title"` // 活动标题
	ActivityTimeRange
	ContractTransformPhaseTaskList []ContractTransformPhaseTask `json:"contract_transform_phase_task_list"`
	ContractTransformCouponInfo
	ContractTransformCheckInTaskList []ContractTransformCheckInTask `json:"contract_transform_check_in_task_list"`
}

// 合约定向转换--阶段任务
type ContractTransformPhaseTask struct {
	TaskId        int64     `json:"task_id"`
	SysId         int64     `json:"sys_id"`
	Title         string    `json:"title"`
	TaskType      string    `json:"task_type"`
	Status        int64     `json:"status"`
	Points        int64     `json:"points"`
	FinishNum     int64     `json:"finish_num"`
	OneStageQuota int64     `json:"one_stage_quota"`
	TwoStageQuota int64     `json:"two_stage_quota"`
	StartTime     time.Time `json:"start_time"`
	EndTime       time.Time `json:"end_time"`
}

// 合约定向转换--卡券信息
type ContractTransformCouponInfo struct {
	Source   string `json:"source"`
	CouponId int64  `json:"coupon_id"`
}

// 合约定向转换--签到任务
type ContractTransformCheckInTask struct {
	Number            int64   `json:"number"`
	PrizeType         int64   `json:"prize_type"`
	PrizeTypeNum      int64   `json:"prize_type_num"`
	PrizeTypeNumRatio float64 `json:"prize_type_num_ratio"`
	IsCheckIn         int64   `json:"is_check_in"`
}

func GetContractTransformTask() *ActivityBaseConf {
	activityBaseConf := &ActivityBaseConf{
		ActivityId:    ContractTransformActivityId,
		ActivityTitle: "合约定向转化活动",
		ActivityTimeRange: ActivityTimeRange{
			StartTimeInt: ContractTransformActivityStartTimeInt,
			EndTimeInt:   ContractTransformActivityEndTimeInt,
		},
	}

	contractTransformPhaseTaskOne := ContractTransformPhaseTask{
		TaskId:    1,
		Title:     "新人已合约交易阶梯任务",
		TaskType:  "is_not_done",
		StartTime: time.Now(),
		EndTime:   time.Now(),
	}
	contractTransformPhaseTaskTwo := ContractTransformPhaseTask{
		TaskId:    2,
		Title:     "沉默用户阶梯任务",
		TaskType:  "is_not_done",
		StartTime: time.Now(),
		EndTime:   time.Now(),
	}
	var ContractTransformCouponInfoDetail ContractTransformCouponInfo
	if utils.CheckDev() {
		contractTransformPhaseTaskOne.SysId = SysNoContractNewUserTaskIdDev
		contractTransformPhaseTaskTwo.SysId = SysSilenceUserTaskIdDev
		ContractTransformCouponInfoDetail = ContractTransformCouponInfo{
			CouponId: 266,
			Source:   "zutqlocezq",
		}
	} else {
		contractTransformPhaseTaskOne.SysId = SysNoContractNewUserTaskIdProd
		contractTransformPhaseTaskTwo.SysId = SysSilenceUserTaskIdProd
		// todo 待补充修改
		ContractTransformCouponInfoDetail = ContractTransformCouponInfo{
			CouponId: 266,
			Source:   "zutqlocezq",
		}
	}
	contractTransformPhaseTaskList := make([]ContractTransformPhaseTask, 0, 2)
	contractTransformPhaseTaskList = append(contractTransformPhaseTaskList, contractTransformPhaseTaskOne, contractTransformPhaseTaskTwo)
	activityBaseConf.ContractTransformPhaseTaskList = contractTransformPhaseTaskList

	activityBaseConf.ContractTransformCouponInfo = ContractTransformCouponInfoDetail

	// 签到任务
	ContractTransformCheckInTaskList := make([]ContractTransformCheckInTask, 7)
	Numbers := []int64{1, 2, 3, 4, 5, 6, 7}

	for i, number := range Numbers {
		task := ContractTransformCheckInTask{
			Number:            number,
			PrizeType:         0,
			PrizeTypeNum:      0,
			IsCheckIn:         0,
			PrizeTypeNumRatio: 0.0,
		}

		// PrizeType:奖品类型 1无奖励 2卡券
		switch number {
		case 1:
			task.PrizeType = CheckInPrizeTypeCoupon
			task.PrizeTypeNumRatio = 0.2
		case 2:
			task.PrizeType = CheckInPrizeTypeNoPrize
		case 3:
			task.PrizeType = CheckInPrizeTypeCoupon
			task.PrizeTypeNumRatio = 0.2
		case 4:
			task.PrizeType = CheckInPrizeTypeNoPrize
		case 5:
			task.PrizeType = CheckInPrizeTypeCoupon
			task.PrizeTypeNumRatio = 0.2
		case 6:
			task.PrizeType = CheckInPrizeTypeNoPrize
		case 7:
			task.PrizeType = CheckInPrizeTypeCoupon
			task.PrizeTypeNumRatio = 0.4
		}
		ContractTransformCheckInTaskList[i] = task
	}
	activityBaseConf.ContractTransformCheckInTaskList = ContractTransformCheckInTaskList

	return activityBaseConf
}

// GetNeedCheckCrowdIds 获取人群id集合
func GetNeedCheckCrowdIds() []int {
	crowdIds := make([]int, 0, 3)
	if utils.CheckDev() {
		// 测试环境
		crowdIds = append(crowdIds, NoContractNewUserCrowdIdDev, SilenceUserCrowdIdDev, BlastUserCrowdIdDev)
	} else {
		// 生产环境
		crowdIds = append(crowdIds, NoContractNewUserCrowdIdProd, SilenceUserCrowdIdProd, BlastUserCrowdIdProd)
	}
	return crowdIds
}

// GetTaskAndSysTaskMap 获取合约定向转化任务对应任务系统任务的id映射
func GetTaskAndSysTaskMap() map[int64]int64 {
	taskAndSysTaskMap := make(map[int64]int64, 2)
	if utils.CheckDev() {
		// 测试环境
		taskAndSysTaskMap[NoContractNewUserTaskId] = SysNoContractNewUserTaskIdDev
		taskAndSysTaskMap[SilenceUserTaskId] = SysSilenceUserTaskIdDev
	} else {
		// 生产环境
		taskAndSysTaskMap[NoContractNewUserTaskId] = SysNoContractNewUserTaskIdProd
		taskAndSysTaskMap[SilenceUserTaskId] = SysSilenceUserTaskIdProd
	}

	return taskAndSysTaskMap
}

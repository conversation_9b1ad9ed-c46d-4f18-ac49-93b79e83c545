package consts

import (
	"fmt"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/language"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
)

// 错误码规范 https://gtglobal.jp.larksuite.com/wiki/IiNjwntThibmIYkdwFTjghuCp1b

// 基础错误码
const (
	Success      = 200
	TgVisitor    = 1403 // tg小程序:游客提示去绑定
	ErrUserLogin = 2002 // 请先登录

	ErrSystemError  = 51300000 //系统错误
	ErrInvalidParam = 51300001 //参数错误
	ErrDbError      = 51300002 //数据库错误
	ErrRedisError   = 51300003 //redis错误
	ErrApiError     = 51300004 //第三方接口错误

)

// 通用业务错误码
const (
	ErrStatusWrong     = 51301001 //状态异常
	ErrUnmarshalFailed = 51301002 //json解析异常
	ErrPermission      = 51301003 //无权限
	ErrRecordNotExist  = 51301004 //记录不存在
	ManyAttempts       = 51301005 //请求过快
)

const (
	AttendedToday        = 51302001 //今日已签到
	CheckInFailed        = 51302002 //签到失败，请稍后重试！
	ErrInvalidActivity   = 51302003 //无效活动
	ErrActivitySuspended = 51302004 //活动维护中
)

// nft活动
const (
	NFT_ACTIVITY_END                 = 51303005 // 活动已结束
	NFT_ACTIVITY_NOT_START           = 51303006 // 活动未开始
	NFT_ACTIVITY_REPEAT_HELP         = 51303007 // 重复助力
	NFT_ACTIVITY_HELPD               = 51303008 // 该助力已被完成
	NFT_ACTIVITY_CAN_NOT_CASTING     = 51303009 // 没有可以铸造的NFT
	NFT_ACTIVITY_ALREADY_CASTING     = 51303010 // 所有的普通nft都已产生领取记录
	NFT_ACTIVITY_SUB_TASK_INCOMPLETE = 51303011 // 子任务未完成
	NFT_ACTIVITY_SUB_TASK_CASTED     = 51303012 // 任务已铸造过NFT，不能重复铸造
	NFT_ACTIVITY_NOT_KYC             = 51303013 // 需要KYC才可以免费领取
	NFT_ACTIVITY_BUY_NUM_ERR1        = ******** // 购买数量不能为0
	NFT_ACTIVITY_BUY_NUM_MAX_LIMIT   = ******** // 购买数量超过最大限制
	NFT_ACTIVITY_ASSETS_INSUFFICIENT = ******** // 资产不足
	NFT_ACTIVITY_HAS_NFT_PROGRESS    = ******** // 有铸造中的NFT
	NFT_ACTIVITY_COMMON_ERROR        = ******** // 活动通用错误
	NFT_ACTIVITY_HELP_YOURSELF       = ******** // 自己为自己助力
	NFT_ACTIVITY_REPEAT_RECEIVE      = ******** // parentNftId已有领取记录
	NFT_SUB_ACCOUNT_NO_ALLOW         = ******** // 子账户不能参与助力
	NFT_AIR_DROP_QUERY_STOCK_ERR     = ******** // nft空投领取：查询库存失败
	NFT_AIR_DROP_NO_STOCK            = ******** // nft空投领取：库存不足
	NFT_AIR_DROP_TRANSACTION_FAIL    = ******** // nft空投领取：事务失败
	NFT_AIR_DROP_FAIL                = ******** // nft空投领取：失败
	NFT_AIR_DROP_LOCK_FAIL           = ******** // nft空投领取：加锁失败
)

// 下载专项活动
const (
	ErrDownloadActivityInvalid             = ********
	ErrDownloadActivityParticipateRepeated = ********
)

// vip空投
const (
	VIP_AIRDROP_END          = ********
	VIP_AIRDROP_NOT_START    = ********
	VIP_AIRDROP_IN_CROWD     = ********
	VIP_AIRDROP_TIER_TOO_LOW = ********
)

// 助力领券
const (
	ErrHelpSignupLimitTimes  = ******** // 一个月内最多领取5次
	ErrHelpGetCouponInvalid  = ******** // 没有可领取的卡券
	ErrTaskNotFinished       = ******** // 任务未完成
	ErrPrizeAlreadyClaimed   = 51305004 // 奖励已领取
	ErrHelpGetCouponRisk     = 51305005 // 命中风控
	ErrHelpGetCouponNotAllow = 51305006 // 市商用户|机构用户|企业用户
	ErrHelpGetCouponSubUser  = 51305007 // 子账号
	ErrHelpGetCouponUnGot    = 51305008 // 卡券未领取
)

// 报错码code
var ErrorCodeKeyMap = map[int]string{
	TgVisitor:                "TgVisitor",
	ErrUserLogin:             "Please login first",
	ManyAttempts:             "manyAttempts",
	AttendedToday:            "Attended Today",
	CheckInFailed:            "check_in_failed",
	ErrInvalidParam:          "invalid parameter",
	ErrInvalidActivity:       "Invalid activity",
	ErrSystemError:           "Refreshbalance_Fail",
	ErrActivitySuspended:     "Suspended for maintenance",
	VIP_AIRDROP_IN_CROWD:     "user in crowd",
	ErrHelpSignupLimitTimes: "Up to 5 registrations per month",
	ErrHelpGetCouponInvalid: "Task is invalid",
	ErrTaskNotFinished:      "Task is not finished",
	ErrPrizeAlreadyClaimed:  "Prize already claimed",
	VIP_AIRDROP_TIER_TOO_LOW: "Tier too low",
}

func GetErrorMsgWithLabel(r *http.Request, code int, label string) error {
	opts := []errors.Option{
		errors.WithLabel(label),
	}
	value, exists := ErrorCodeKeyMap[code]
	if exists {
		return errors.New(code, language.GetLangDescByKey(value, requestools.GetUserLanguage(r)), opts...)
	} else {
		return errors.New(code, language.GetLangDescByKey(ErrorCodeKeyMap[ErrSystemError], requestools.GetUserLanguage(r)), opts...)
	}
}

func GetErrorMsg(r *http.Request, code int) error {
	return GetErrorMsgWithLabel(r, code, "")
}

/**
 * 格式化error
 * 返回label字段，用于输出真实错误和请求ID，便于排查问题
 */
func ErrFormat(r *http.Request, err error, reqId string) error {
	if err == nil {
		return nil
	}
	if cm, ok := err.(*errors.CodeMsg); ok {
		return GetErrorMsgWithLabel(r, cm.Code, fmt.Sprintf("%v order_id: %v", err, reqId))
	}
	return err
}

package download

// 下载专项
const DOWNLOAD_START_DATE = "2025-01-01 00:00:00"

type Task struct {
	Id         int                    `json:"id"`          // 任务id
	Type       string                 `json:"type"`        //任务类型
	Sort       int                    `json:"sort"`        //任务排序
	Status     int                    `json:"status"`      //任务状态 0 未领取  1  进行中  2 完成 3 失败
	Msg        map[string]interface{} `json:"msg"`         //任务扩展信息  例如 邀请任务的人数  头衔等
	CouponType string                 `json:"coupon_type"` // 任务发放的券类型
}

const REGISTER = "register"
const APP = "app"
const INVITE = "invite"
const CHANGE = 1

const REGISTERID = 1
const APPID = 2
const INVITEID = 3

const (
	CouponTypeContractBonusNew = "contract_bonus_new"

	CouponTypeCommissionRebate = "commission_rebate"
)

var TaskListMap = map[int]Task{
	REGISTERID: {Id: REGISTERID, Type: REGISTER, Sort: 1, Status: 0, Msg: nil, CouponType: CouponTypeCommissionRebate},
	APPID:      {Id: APPID, Type: APP, Sort: 2, Status: 0, Msg: nil, CouponType: CouponTypeCommissionRebate},
	INVITEID:   {Id: INVITEID, Type: INVITE, Sort: 3, Status: 0, Msg: nil, CouponType: CouponTypeContractBonusNew},
}

const (
	ActivityStatusPending = 1
	ActivityStatusValid   = 2
	ActivityStatusEnd     = 3
)

const (
	TaskStatusInProgress        = 1
	TaskStatusInCompleted       = 2
	TaskStatusFailed            = 3
	TaskStatusLostQualification = 4
)

const (
	ChannelWeb = "1imqeirV"
	ChannelH5  = "lfdS7xLF"
)

var ChannelMap = map[string]struct{}{
	ChannelWeb: {},
	ChannelH5:  {},
}

const (
	ActivityPlatformIDOffline int64 = 586
	ActivityPlatformIDOnline  int64 = 767
)

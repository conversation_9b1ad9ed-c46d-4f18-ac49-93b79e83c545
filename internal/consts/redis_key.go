package consts

import "fmt"

const (
	prefixKey           = "marketing_activity:"
	testKey             = "test"
	testKey2            = "test:"
	activityCheckInLock = "checkIn:lock"
	activityCheckInList = "checkIn:list"

	// 活动列表 nft:activity:list:v2:{activity_type}:{is_centralized}
	NftBaseTypeActivityListKey = "nft:activity:list:v3:%d:%d"
	// 客户端验证
	NftClientVerifyTimeKey = "nft:client:verify:%s:%d:%d"

	NftActivityDetailKey       = "nft:activity:detail:v1:%d:%d"
	NftActivityNftListKey      = "nft:activity:nft:list:%d"
	NftUserMintLogKey          = "nft:user:mint:%d:%d"
	NftWalletAddressMintLogKey = "nft:wallet:address:mint:%s:%d"

	NftRollMintLogKey = "nft:roll:mint:%d:%d:%s:%d"

	NftTypeNftListKey = "nft:type:nft:list:%d"

	NftUserSubTaskKey = "nft:user:sub:task:%d:%d:%s"

	NftOrderId           = "nft:order:id"
	NftCastingRateLimits = "nft:casting:rate:limit:%d"

	VipAirdropApplyKey      = "vip:airdrop:apply:%s:%d"
	VipAirdropApplyTotalKey = "vip:airdrop:apply:total:%s"

	FeeSeettingMmWhiteListKey = "marketing:activity:fee:setting:mm:white:list"

	HelpGetCouponPageInitUsersKey  = "help:page:init"
	HelpGetCouponTaskConditionInfo = "help:task:condition:info"
)

// CachePrefixKey 获取缓存前缀.
func CachePrefixKey() string {
	return prefixKey
}

// CacheTestKey 获取测试key.
func CacheTestKey() string {
	return CachePrefixKey() + testKey
}

// CacheTestKeyByParam 获取测试key.
func CacheTestKeyByParam(uid int) string {
	return CachePrefixKey() + fmt.Sprintf("%s:%d", testKey2, uid)
}

// ActivityCheckInLock 签到lock
func ActivityCheckInLock(actId int64, uid int64) string {
	return CachePrefixKey() + fmt.Sprintf("%s:%d:%d", activityCheckInLock, actId, uid)
}

// ActivityCheckInList 签到列表
func ActivityCheckInList(actId int64, uid int64) string {
	return CachePrefixKey() + fmt.Sprintf("%s:%d:%d", activityCheckInList, actId, uid)
}

// Vip空投报名lock
func VipAirdropApplyLock(season string, uid int64) string {
	return CachePrefixKey() + fmt.Sprintf(VipAirdropApplyKey, season, uid)
}

// Vip空投报名人数
func VipAirdropApplyTotal(season string) string {
	return CachePrefixKey() + fmt.Sprintf(VipAirdropApplyTotalKey, season)
}

func HelpTaskConditionKey(taskId int64) string {
	return CachePrefixKey() + fmt.Sprintf("%s:%d", HelpGetCouponTaskConditionInfo, taskId)
}

func HelpPageInitUsersKey(uid int64) string {
	return CachePrefixKey() + fmt.Sprintf("%s:%d", HelpGetCouponPageInitUsersKey, uid)
}
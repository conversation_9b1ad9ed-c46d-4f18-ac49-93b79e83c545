package consts

import "bitbucket.org/gateio/gateio-lib-base-go/environment"

const (
	// 活动状态
	ActivityStatusNotStart   = 0 // 未开始
	ActivityStatusInProgress = 1 // 进行中
	ActivityStatusOverdue    = 2 // 已过期

	// 任务状态
	TaskStatusNotFinish   = 0 // 未开始
	TaskStatusInProgress  = 1 // 进行中
	TaskStatusFinish      = 2 // 已完成
	TaskStatusInPrize     = 3 // 结算中
	TaskStatusFinishPrize = 4 // 已结算
	TaskStatusExpires     = 5 // 已过期

	// 任务上报状态
	TaskStatusInitial   = 0 // 初始状态
	TaskStatusReported  = 1 // 任务上报成功
	TaskStatusCompleted = 2 // 任务已完成
	TaskStatusInvalid   = 3 // 任务已失效

	// 任务系统回调消息call_type类型
	SysTaskCallTypeAck       = "ack"
	SysTaskCallTypeSubDone   = "sub_done"
	SysTaskCallTypeDone      = "done"
	SysTaskCallTypeSendPrize = "send_prize"
	SysTaskCallTypeExpire    = "expire"

	GoFormatTimeStr = "2006-01-02 15:04:05"

	ChCountryCode = 37

	NftMintStatusInit   = 1 // nft铸造状态 初始化状态
	NftMintStatusOn     = 2 // nft铸造状态 进行中状态
	NftMintStatusFinish = 3 // nft铸造状态 完成状态
	NftMintStatusFail   = 4 // nft铸造状态 失败状态

	// 用户注册来源常量
	NftFromChannelOnline  = "dWAK9G8c" // 红牛落地页生产预发环境 ch码
	NftFromChannelOffLine = "AGdooZnb" // 红牛落地页测试环境 ch码

	// 业务类型
	BusinessTypeHelp = 1 // 助力领券
	// 助力领券活动邀请人状态
	HelpStatusNotJoined  = 1 // 1.未报名
	HelpStatusInProgress = 2 // 2.待助力
	HelpStatusSuccess    = 3 // 3.助力成功
	HelpStatusFailed     = 4 // 4.助力未完成
	HelpStatusClaimed    = 5 // 5.已领奖
	HelpStatusRisk       = 6 // 6.命中风控
	// 助力领券用户领奖状态
	HelpUserPrizeStatusInit    = 0 // 初始化
	HelpUserPrizeStatusUngot   = 1 // 未领取
	HelpUserPrizeStatusGetting = 2 // 发放中
	HelpUserPrizeStatusSuccess = 3 // 发放成功
	HelpUserPrizeStatusFailed  = 4 // 发放失败

	// 任务中心业务类型
	HelpTaskBusinessType = 19 // 助力领券
	// todo：任务中心任务id
	HelpInviterTaskIdTest        = 1755
	HelpInviteeDepositTaskIdTest = 1753
	HelpInviteeTradeTaskIdTest   = 1754
	HelpInviterTaskIdProd        = 2476
	HelpInviteeDepositTaskIdProd = 2474
	HelpInviteeTradeTaskIdProd   = 2475
	// 任务中心奖励类型
	PrizeTypePoints = 1 // 积分
	PrizeTypeDiy    = 2 // 业务自定义
	PrizeTypeCoupon = 3 // 卡券
	PrizeTypePool   = 4 // 奖池
	// HelpProcess
	HelpProcessing    = 1 // 1进行中
	HelpProcessFinish = 2 // 2已结束

	// redis key
	CouponInfoAll = "welfare_go:coupon:info:all:%d:%s" // 卡劵详情key

	// 是否邀请人任务 0被邀请人,1邀请人
	IsInviteeTask = 0
	IsInviterTask = 1

	// 被邀请人状态
	InviteeStatusInit     = 0 // 未创建
	InviteeStatusRegister = 1 // 注册
	InviteeStatusProcess  = 2 // 入金/交易
	InviteeStatusFinish   = 3 // 已完成
	InviteeStatusEnd      = 4 // 已结束
	InviteeStatusRisk     = 5 // 命中风控

	// 任务类型
	TaskTypeOnce = 1 // 单次任务
	TaskTypeCycl = 2 // 循环任务

	// 任务系统类型
	TaskTypeSignup  = "signup"
	TaskTypeDeposit = "deposit"
	TaskTypeTrade   = "trade"

	// 风控相关
	HelpGetCouponRiskKey    = "marketing_acticity:help_get_coupon:user:risk:%d:%s:%s" // 风控信息key
	RiskEventCodeInviteRisk = "invite_risk"

	// 渠道码-助力领券-测试
	UtmCmpHelpGetCouponTest = "KZUzP9AG"
	// 渠道码-助力领券-生产
	UtmCmpHelpGetCouponProd = "PEYEQdSb"
	// 渠道码-惊喜大门-测试
	UtmCmpOpenGateTest = "xJ9oBg3C"
	// 渠道码-钥匙开门-生产
	UtmCmpOpenGateProd = "IBNrS2Ce"

	// 站内信常量
	HeraldMessageApp           = "service_marketing_activity"
	HeraldTaskRemain3          = "task_remain_3"
	HeraldTaskRemain1          = "task_remain_1"
	HeraldTaskExpired          = "task_expired"
	HeraldRefereeRegister      = "referee_register"
	HeraldRefereeTaskComplete  = "referee_task_complete"
	HeraldTaskComplete         = "task_complete"
	HeraldRewardRemind         = "reward_remind"
	HeraldRewardRelease        = "reward_release"
	HeraldInviteJoinAgain      = "invite_join_again"
	HeraldRefereeRewardRelease = "referee_reward_release"
	HeraldTaskRemind           = "task_remind"
	HeraldMethodMobile         = "mobile"

	// 反佣活动配置状态
	InviteRebateOnline = 1
	InviteRebateOffine = 2

	// 反佣活动生效状态
	InviteRebateUnstart    = 1 // 未开始
	InviteRebateProcessing = 2 // 进行中
	InviteRebateEnd        = 3 // 已结束

	/**
	 * verified：2 kyc1初级认证 1 kyc2高级认证 4 kyc3 住址证明认证 3 企业认证
	 * KYC 0.5 等级，介于KYC0 和KYC1 之间的等级，需满足完成LN 扫描
	 */
	KycLv1       = 2
	KycLv2       = 1
	KycLv3       = 4
	KycLvCompany = 3
	KycLv0       = 0
	KycLv05      = 5

	// 活动图片域名-prod
	ACTIVITY_IMG_DOMAIN_PROD = "https://gavatar.gateimg.com/"
	// 活动图片域名-dev
	ACTIVITY_IMG_DOMAIN_DEV = "https://test-gateio-activity.s3.ap-northeast-1.amazonaws.com/"
)

/**
 * 获取用户注册来源常量
 * @return string
 */
func GetFromChannel(isDev bool) string {
	fromChannel := NftFromChannelOnline
	if isDev {
		fromChannel = NftFromChannelOffLine
	}
	return fromChannel
}

/**
 * 获取邀请人助力领券任务id
 * @return string
 */
func GetHelpInviterTaskId() int64 {
	if environment.IsProd() || environment.IsPre() {
		return HelpInviterTaskIdProd
	}
	return HelpInviterTaskIdTest
}

/**
 * 获取被邀请人助力领券任务id
 * @return string
 */

func GetHelpInviteeDepositTaskId() int64 {
	if environment.IsProd() || environment.IsPre() {
		return HelpInviteeDepositTaskIdProd
	}
	return HelpInviteeDepositTaskIdTest
}

func GetHelpInviteeTradeTaskId() int64 {
	if environment.IsProd() || environment.IsPre() {
		return HelpInviteeTradeTaskIdProd
	}
	return HelpInviteeTradeTaskIdTest
}

func IfInviteeTask(taskCenterId int64) bool {
	if environment.IsProd() || environment.IsPre() {
		if taskCenterId == HelpInviterTaskIdProd {
			return false
		}
	} else {
		if taskCenterId == HelpInviterTaskIdTest {
			return false
		}
	}
	return true
}

func GetHelpGetCouponUtmCmp() string {
	if environment.IsProd() || environment.IsPre() {
		return UtmCmpHelpGetCouponProd
	}
	return UtmCmpHelpGetCouponTest
}

func GetOpenGateUtmCmp() string {
	if environment.IsProd() || environment.IsPre() {
		return UtmCmpOpenGateProd
	}
	return UtmCmpOpenGateTest
}

func JointActivityImgUrl(uri string) string {
	if environment.IsProd() || environment.IsPre() {
		return ACTIVITY_IMG_DOMAIN_PROD + uri
	}
	return ACTIVITY_IMG_DOMAIN_DEV + uri
}

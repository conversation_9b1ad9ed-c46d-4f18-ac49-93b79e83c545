package consts

/**
 * verified：2 kyc1初级认证 1 kyc2高级认证 4 kyc3 住址证明认证 3 企业认证
 * KYC 0.5 等级，介于KYC0 和KYC1 之间的等级，需满足完成LN 扫描
 */
const (
	KYC_LV_1       = 2 // kyc1初级认证
	KYC_LV_2       = 1 // kyc2高级认证
	KYC_LV_3       = 4 // kyc3住址证明认证
	KYC_LV_COMPANY = 3 // 企业认证
	KYC_LV_0       = 0 // KYC0
	KYC_LV_0_5     = 5 // KYC0.5
)

/**
 * 验证用户是否kyc
 */
func IsKyc(verified int64) bool {
	if verified == KYC_LV_1 || verified == KYC_LV_2 || verified == KYC_LV_3 || verified == KYC_LV_COMPANY {
		return true
	}
	return false
}

package kafka

// 合约定向转换活动接受任务系统ack回调消息
const ContractTransformTaskAckGroup = "Gateio-Growth-ContractTransformTaskAck-Group"

// 下载专项 group
const DownLoadUserRegisterGroup = "Gateio-Growth-DownloadUserRegister-Group"

// vip空投group
const VipAirdropTaskGroup = "Gateio-Growth-VipAirdrop-Group"

// 助力领券 group
const (
	HelpTaskRegisterGroup = "Gateio-Growth-HelpTaskRegister-Group"
	HelpTaskFinishGroup   = "Gateio-Growth- HelpTaskFinish-Group"
	HelpUserRegisterGroup = "Gateio-Growth-HelpUserRegister-Group"
)

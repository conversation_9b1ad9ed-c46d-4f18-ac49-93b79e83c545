package kafka

// 定义任务变更状态常量
const (
	StatusNotStarted    = 1 // 未开始
	StatusInProgress    = 2 // 进行中
	StatusCompleted     = 3 // 已完成
	StatusInvalid       = 4 // 已失效
	StatusRewardIssued  = 5 // 奖励已发放
	StatusRewardIssuing = 6 // 奖励发放中
)

// TaskCallBackMsg 任务系统回调消息结构
type TaskCallBackMsg struct {
	BusinessType int    `json:"business_type"`
	CallType     string `json:"call_type"`
	List         []struct {
		BusinessType int    `json:"business_type"`
		BusinessId   string `json:"business_id"`
		UserId       int64  `json:"user_id"`
		TaskId       int64  `json:"task_id"`
		Status       int    `json:"status"`
		ReceivedType int    `json:"received_type"`
		OnlyId       string `json:"only_id"`
		DoneTime     int64  `json:"done_time"`
		Conditions   []struct {
			PrizeType int         `json:"prize_type"`
			PrizeNum  int         `json:"prize_num"`
			Status    int         `json:"status"`
			PrizeExt  interface{} `json:"prize_ext"`
		} `json:"conditions"`
	} `json:"list"`
}

// 用户邀请回调
type UserRegisterCallBackMsg struct {
	Uid    int64  `json:"uid,omitempty"`
	Timest string `json:"timest,omitempty"`
	RefUid int64  `json:"ref_uid,omitempty"`
	UtmCmp string `json:"utm_cmp"`
}

// TaskCallBackRegMsg 任务系统回调-邀请注册
type TaskCallBackRegMsg struct {
	BusinessType int64               `json:"business_type"`
	CallType     string              `json:"call_type"`
	List         TaskCallBackRegList `json:"list"`
}
type TaskCallBackRegList struct {
	BusinessType    int64        `json:"business_type"`
	BusinessID      string       `json:"business_id"`
	UserID          int64        `json:"user_id"`
	TaskID          int64        `json:"task_id"`
	BeInvitedUserID int64        `json:"be_invited_user_id"`
	StartTime       int64        `json:"start_time"`
	EndTime         int64        `json:"end_time"`
	InviteInfo      []InviteInfo `json:"invite_info"`
}
type InviteInfo struct {
	BusinessID string `json:"business_id"`
	TaskID     string `json:"task_id"`
}

// TaskCallBackMsg 任务系助力领券调消息结构
type TaskCallHelpBackMsg struct {
	BusinessType int                   `json:"business_type"`
	CallType     string                `json:"call_type"`
	List         []TaskCallBackMsgItem `json:"list"`
}

type TaskCallBackMsgItem struct {
	BusinessType int    `json:"business_type"`
	BusinessId   string `json:"business_id"`
	UserId       int64  `json:"user_id"`
	TaskId       int64  `json:"task_id"`
	Status       int    `json:"status"`
	ReceivedType int    `json:"received_type"`
	OnlyId       string `json:"only_id"`
	Conditions   []struct {
		PrizeType int         `json:"prize_type"`
		PrizeNum  int         `json:"prize_num"`
		Status    int         `json:"status"`
		PrizeExt  interface{} `json:"prize_ext"`
	} `json:"conditions"`
	InvitedUserIds []int64 `json:"invited_user_ids"`
}

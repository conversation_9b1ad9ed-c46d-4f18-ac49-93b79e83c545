package kafka

// 任务中心任务上报
const GrowthTaskRecordTopic = "Gateio-Growth-Task-Record-Topic"

// 任务中心回调信息
const GrowthTaskCallTopic = "Gateio-Growth-Task-Call-Topic"

// 任务中心奖励上报
const GrowthTaskReceiveTopic = "Gateio-Growth-Task-Receive-Topic"

// 限时任务上报任务
const LimitedTimeTaskTopic = "Gateio-Growth-Task-DistributeUser-Topic"

const UserCenterRegisterTopic = "Gateio-UserCenter-Register-Topic"

// 活动报名数据上报及活动信息同步 https://gtglobal.jp.larksuite.com/wiki/ZCnRwasEUiuho7kpA5xjnOdgprb
const AdjustUserRecordTopic = "Gateio-Growth-Adjust-UserRecord-Topic"

const TaskInviteJoinTopic = "Gateio-Growth-Task-InviteJoin-Topic"

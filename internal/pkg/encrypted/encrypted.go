package encrypted

import (
	"context"
	"encoding/base64"
	"fmt"
	"strings"
)

type Config struct {
	IDConvertKey string `json:"id_convert_key,optional,omitempty"`
}

type Encrypted interface {
	GetDecryptedId(ciphertextId string, key string) (string, error)
	GetEncryptedId(id string, key string) (string, error)
}

type encrypted struct {
	ctx context.Context
}

// NewEncrypted .
func NewEncrypted(ctx context.Context) Encrypted {
	c := &encrypted{
		ctx: ctx,
	}
	return c
}

// GetDecryptedId 解密加密的 ID
func (e *encrypted) GetDecryptedId(ciphertextId string, key string) (string, error) {
	// 替换特殊字符
	ciphertextDec := strings.ReplaceAll(ciphertextId, "O0O0O", "=")
	ciphertextDec = strings.ReplaceAll(ciphertextDec, "o000o", "+")
	ciphertextDec = strings.ReplaceAll(ciphertextDec, "oo00o", "/")

	// Base64 解码
	decoded, err := base64.StdEncoding.DecodeString(ciphertextDec)
	if err != nil {
		return "", fmt.Errorf("base64 解码失败: %v", err)
	}

	// 异或解密
	sLen := len(decoded)
	kLen := len(key)
	plain := make([]byte, sLen)

	for i := 0; i < sLen; i += kLen {
		for j := 0; j < kLen && i+j < sLen; j++ {
			plain[i+j] = decoded[i+j] ^ key[j]
		}
	}

	return string(plain), nil
}

// GetEncryptedId 对输入的字符串进行加密
func (e *encrypted) GetEncryptedId(id string, key string) (string, error) {
	// 异或加密
	sLen := len(id)
	kLen := len(key)
	var cipher strings.Builder

	for i := 0; i < sLen; i += kLen {
		for j := 0; j < kLen && i+j < sLen; j++ {
			cipher.WriteByte(id[i+j] ^ key[j])
		}
	}

	// Base64 编码
	encoded := base64.StdEncoding.EncodeToString([]byte(cipher.String()))

	// 替换特殊字符
	encoded = strings.ReplaceAll(encoded, "=", "O0O0O")
	encoded = strings.ReplaceAll(encoded, "+", "o000o")
	encoded = strings.ReplaceAll(encoded, "/", "oo00o")

	return encoded, nil
}

package contract_transform

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/datacenter"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/marketing"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/consts/activity"
	"gateio_service_marketing_activity/internal/consts/kafka"
	"gateio_service_marketing_activity/internal/models/contract_transform_model"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/utils"
	"sync"
)

func CheckUserInCrowd(ctx context.Context, svcCtx *svc.ServiceContext, wg *sync.WaitGroup, crowdId int, userId int, isInCrowd *bool) {
	defer wg.Done()

	// 先从缓存获取数据
	redisKey := fmt.Sprintf(activity.UserIsInCrowdData, userId, crowdId)
	cacheData, err := svcCtx.Redis.GetCtx(ctx, redisKey)
	if err == nil {
		resp := &marketing.CheckUserInCrowdResponse{}
		err = json.Unmarshal([]byte(cacheData), resp)
		if err == nil {
			*isInCrowd = resp.UserInCrowd
			return
		}
	}

	param := &marketing.CheckUserInCrowdRequest{
		CrowdID: int64(crowdId),
		UserID:  int64(userId),
	}
	checkUserInCrowdRes, err := marketing.NewClient().CheckUserInCrowd(ctx, param)
	if err != nil {
		logx.Infof("GetUserNeedShowTask failed, step: CheckUserInCrowd, param : %v, err: %v", param, err)
	}
	*isInCrowd = checkUserInCrowdRes.UserInCrowd

	checkUserInCrowdResByte, err := json.Marshal(*checkUserInCrowdRes)
	if err == nil {
		checkUserInCrowdResStr := string(checkUserInCrowdResByte)
		err = svcCtx.Redis.SetexCtx(ctx, redisKey, checkUserInCrowdResStr, 60)
		if err != nil {
			logx.Infof("GetUserNeedShowTask failed, step: Redis.SetexCtx, redisKey : %s, err: %v", redisKey, err)
		}
	}

	return
}

// GetUserTaskSchedule 获取用户当前任务进度信息
func GetUserTaskSchedule(ctx context.Context, svcCtx *svc.ServiceContext, userId int64, taskId int64, businessType int64, businessId string) (*task.RecordScheduleResponse, error) {
	// 先从缓存获取
	redisKey := fmt.Sprintf(activity.ContractTransformTRSData, userId, taskId)
	cacheData, err := svcCtx.Redis.GetCtx(ctx, redisKey)
	if err == nil {
		resp := &task.RecordScheduleResponse{}
		err = json.Unmarshal([]byte(cacheData), resp)
		if err == nil {
			return resp, nil
		}
	}

	param := &task.RecordScheduleRequest{
		TaskID:       taskId,
		UserID:       userId,
		BusinessType: businessType,
		BusinessID:   businessId,
	}
	recordScheduleRes, err := task.NewClient().RecordSchedule(ctx, param)
	if err != nil {
		logx.Infof("GetUserTaskSchedule failed, step: RecordSchedule, param : %v, err: %v", param, err)
		return &task.RecordScheduleResponse{}, err
	}

	// 把结果加入到redis缓存当中
	recordScheduleResByte, err := json.Marshal(*recordScheduleRes)
	if err == nil {
		recordScheduleResStr := string(recordScheduleResByte)
		err = svcCtx.Redis.SetexCtx(ctx, redisKey, recordScheduleResStr, 60)
		if err != nil {
			logx.Infof("GetUserTaskSchedule failed, step: Redis.SetexCtx, redisKey : %s, err: %v", redisKey, err)
		}
	}

	return recordScheduleRes, nil
}

// GetTaskSysTaskInfoList 批量获取任务系统任务详情数据
func GetTaskSysTaskInfoList(ctx context.Context, svcCtx *svc.ServiceContext, userId int64, taskIds string, businessType int64, businessId string) (*task.BatchTaskAllResponse, error) {
	// 先从缓存获取
	redisKey := fmt.Sprintf(activity.ContractTransformTaskSysTaskInfoList, taskIds, businessType)
	cacheData, err := svcCtx.Redis.GetCtx(ctx, redisKey)
	if err == nil {
		resp := &task.BatchTaskAllResponse{}
		err = json.Unmarshal([]byte(cacheData), resp)
		if err == nil {
			return resp, nil
		}
	}

	param := &task.BatchTaskAllRequest{
		TaskIDs:      taskIds,
		UserID:       userId,
		BusinessType: businessType,
		BusinessID:   businessId,
	}
	batchTaskAllRes, err := task.NewClient().BatchTaskAll(ctx, param)
	if err != nil {
		logx.Infof("GetUserTaskSchedule failed, step: RecordSchedule, param : %v, err: %v", param, err)
		return &task.BatchTaskAllResponse{}, err
	}
	// 把结果加入到redis缓存当中
	batchTaskAllResByte, err := json.Marshal(*batchTaskAllRes)
	if err == nil {
		batchTaskAllResStr := string(batchTaskAllResByte)
		err = svcCtx.Redis.SetexCtx(ctx, redisKey, batchTaskAllResStr, 60)
		if err != nil {
			logx.Infof("GetTaskSysTaskInfoList failed, step: Redis.SetexCtx, redisKey : %s, err: %v", redisKey, err)
		}
	}

	return batchTaskAllRes, nil
}

// CheckUserIsContractDeal 检测用户是否进行过合约交易
func CheckUserIsContractDeal(ctx context.Context, userId int) (int64, error) {
	var isDoContractDeal int64 = 0
	dataClient := datacenter.NewClient()
	queryResult, err := dataClient.QueryExec(ctx, &datacenter.QueryExecRequest{
		FilterParams: map[string]interface{}{
			"ads.ads_user_contract_circle_group.uid": userId,
		},
		ApiID: 1869930816470401024,
	})

	if err != nil {
		logx.Infof("CheckUserIsContractDeal failed, step: dataClient.QueryExec, param : %d, err: %v", userId, err)
		return isDoContractDeal, err
	}
	if val, ok := queryResult.List.([]interface{}); !ok {
		logx.Infof("CheckUserIsContractDeal failed, step: queryResult.List, param : %d, err: %v", userId, err)
		return isDoContractDeal, err
	} else if len(val) == 0 { // 兼容 TotalCount 大于 0，但是list没数据的情况...
		return isDoContractDeal, fmt.Errorf("没查询到数据")
	}
	if queryResult.TotalCount == 0 {
		return isDoContractDeal, fmt.Errorf("没查询到数据")
	}

	vals, ok := queryResult.List.([]interface{})
	if !ok {
		return isDoContractDeal, fmt.Errorf("没查询到数据")
	}

	for _, item := range vals {
		mapTemp, ok := item.(map[string]interface{})
		if !ok {
			return isDoContractDeal, fmt.Errorf("数据解析失败")
		}

		isDoContractDeal = utils.MustInt64(mapTemp["is_new_user"])
	}
	return isDoContractDeal, nil
}

// ContractTransformTaskCallBack 合约定向转化任务接受任务回调消息处理方法
func ContractTransformTaskCallBack(ctx context.Context, svcCtx *svc.ServiceContext, msgStr string) {
	taskCallBackMsg := kafka.TaskCallBackMsg{}
	err := json.Unmarshal([]byte(msgStr), &taskCallBackMsg)
	if err != nil {
		logx.Infof("ContractTransformTaskCallBack Unmarshal Msg err: %v", err)
		return
	}

	logx.Infof("ContractTransformTaskCallBack，消息解析成功，收到任务系统回调消息：%s", msgStr)

	if taskCallBackMsg.BusinessType != activity.ContractTransformTaskBusinessType {
		return
	}

	if len(taskCallBackMsg.List) == 0 {
		return
	}

	businessIdInt, _ := utils.TryInt64(taskCallBackMsg.List[0].BusinessId)
	if taskCallBackMsg.CallType == consts.SysTaskCallTypeAck {
		updateData := make(map[string]interface{}, 2)
		updateData["status"] = consts.TaskStatusInProgress
		contractTransformUserTaskModel := contract_transform_model.NewContractTransformUserTaskModel(svcCtx.DBMarketingActivities)
		err = contractTransformUserTaskModel.UpdateOneById(ctx, businessIdInt, updateData)
		if err != nil {
			logx.Infof("ContractTransformTaskCallBack UpdateOneById Msg err: %v", err)
		}
	}

	if taskCallBackMsg.CallType == consts.SysTaskCallTypeSubDone || taskCallBackMsg.CallType == consts.SysTaskCallTypeDone {
		contractTransformUserTaskModel := contract_transform_model.NewContractTransformUserTaskModel(svcCtx.DBMarketingActivities)
		contractTransformUserTaskInfo, err := contractTransformUserTaskModel.FindOne(ctx, businessIdInt)
		if err != nil {
			return
		}

		finishNum := contractTransformUserTaskInfo.FinishNum + 1
		updateData := make(map[string]interface{}, 2)
		updateData["finish_num"] = finishNum
		if finishNum == 2 {
			updateData["status"] = consts.TaskStatusFinishPrize
		}
		err = contractTransformUserTaskModel.UpdateOneById(ctx, businessIdInt, updateData)
		if err != nil {
			logx.Infof("ContractTransformTaskCallBack UpdateOneById Msg err: %v", err)
		}
	}

	return
}

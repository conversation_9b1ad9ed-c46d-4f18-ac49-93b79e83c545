package common

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"time"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/consts/activity"
	"gateio_service_marketing_activity/internal/models"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/utils"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/engine/auth"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/coupon"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/datacenter"
)

func GetUserInfo(ctx context.Context) (*auth.UserInfo, error) {
	request, ok := requestools.GetRequestFromCtx(ctx)
	if !ok {
		logx.Error("获取用户信息失败")
		return nil, errors.New(2002, "获取用户信息失败")
	}
	return requestools.GetUserInfo(request), nil
}

// CheckActivityValid 检查活动是否有效期内
func CheckActivityValid(activityID int) (int, error) {
	// 初始化活动状态 - 未开始
	activityStatus := consts.ActivityStatusNotStart

	// 获取所有活动与时间的map
	activityAndTimeMap := GetActivityAndTimeMap()
	// 判断当前活动是否存在
	if _, ok := activityAndTimeMap[activityID]; !ok {
		activityStatus = consts.ActivityStatusOverdue
		return activityStatus, nil
	}

	nowTime := time.Now().Unix()

	if nowTime >= activityAndTimeMap[activityID].StartTimeInt && nowTime <= activityAndTimeMap[activityID].EndTimeInt {
		activityStatus = consts.ActivityStatusInProgress
	} else if nowTime > activityAndTimeMap[activityID].EndTimeInt {
		activityStatus = consts.ActivityStatusOverdue
	}
	return activityStatus, nil
}

func GetActivityAndTimeMap() map[int]activity.ActivityTimeRange {
	activityMap := make(map[int]activity.ActivityTimeRange, 100)
	// 添加-合约定向转化任务到map
	activityMap[activity.ContractTransformActivityId] = activity.ActivityTimeRange{
		StartTimeInt: activity.ContractTransformActivityStartTimeInt,
		EndTimeInt:   activity.ContractTransformActivityEndTimeInt,
	}
	return activityMap
}

// GetNetFeeAmount 获取爆仓额度
func GetNetFeeAmount(ctx context.Context, r *http.Request, uid int64) (int64, error) {
	dataClient := datacenter.NewClient()
	queryResult, err := dataClient.QueryExec(ctx, &datacenter.QueryExecRequest{
		FilterParams: map[string]interface{}{
			"ads.ads_user_contract_circle_group.uid": uid,
		},
		ApiID: 1868467711500500992,
	})
	if err != nil {
		return 0, consts.GetErrorMsg(r, consts.ErrInvalidActivity)
	}
	if val, ok := queryResult.List.([]interface{}); !ok {
		return 0, consts.GetErrorMsg(r, consts.ErrInvalidActivity)
	} else if len(val) == 0 { // 兼容 TotalCount 大于 0，但是list没数据的情况...
		return 0, consts.GetErrorMsg(r, consts.ErrInvalidActivity)
	}
	if queryResult.TotalCount == 0 {
		return 0, consts.GetErrorMsg(r, consts.ErrInvalidActivity)
	}
	// fmt.Println(queryResult.List)

	vals, ok := queryResult.List.([]interface{})
	if !ok {
		return 0, consts.GetErrorMsg(r, consts.ErrInvalidActivity)
	}
	var NetFee int64

	for _, item := range vals {
		// [map[net_fee:120.1110000000]]
		_v, _ok := item.(map[string]interface{})
		if !_ok {
			return 0, consts.GetErrorMsg(r, consts.ErrInvalidActivity)
		}
		//fmt.Println(reflect.TypeOf(item))
		//fmt.Println(reflect.TypeOf(_v))
		//value, _ok := _v["net_fee"].(string)
		//if !_ok {
		//	return 0, errors.New(consts.ErrInvalidActivity, language.GetLangDescByKey(consts.ErrorCodeKeyMap[consts.ErrInvalidActivity], requestools.GetUserLanguage(r)))
		//}
		//floatNum, err := strconv.ParseFloat(value, 64)
		//if err != nil {
		//	fmt.Printf("键 %s 的值转换错误: %v\n", value, err)
		//} else {
		//	fmt.Printf("键 %s 的值转换结果: %f\n", value, floatNum)
		//}

		float64Fee := utils.ToNumber(_v["net_fee"])
		// 取整数部分
		NetFee = utils.MustInt64(math.Floor(float64Fee))
	}

	return NetFee, nil
}

func GetNetFeeCouponAmount(netFeeAmount int64) (int64, error) {
	var amount int64
	var ratio float64
	// todo 规则待确认
	if netFeeAmount >= 300 && netFeeAmount < 2000 {
		ratio = 0.05
	} else if netFeeAmount >= 2000 {
		netFeeAmount = 2000
		ratio = 0.05
	} else {
		ratio = 0.1
	}
	amount = utils.MustInt64(math.Floor(float64(netFeeAmount) * ratio * 3))

	return amount, nil
}

type CouponCall struct {
	logx.Logger
	ctx context.Context
}

func NewCouponCall(ctx context.Context) *CouponCall {
	return &CouponCall{Logger: logx.WithContext(ctx), ctx: ctx}
}

// SendCouponById 发放卡券
func (cc *CouponCall) SendCouponById(uid int64, couponID int64, source string, amount string, requestID string) (*coupon.SendCouponByIdResponse, error) {
	date := utils.TimeUtil{}
	milliTime := date.GetNowMilliTime(date.Now())
	req := coupon.SendCouponByIdRequest{
		UserID:    uid,
		CouponID:  couponID,
		Source:    source,
		OnlyID:    fmt.Sprintf("%d_%d", uid, milliTime),
		RequestID: fmt.Sprintf("%d_%s", uid, requestID),
		Amount:    amount,
	}
	result, err := coupon.NewClient().SendCouponById(cc.ctx, &req)
	if err != nil {
		cc.Logger.Warnf("卡券发放失败, value: %t, err: %v", req, err)
		return nil, err
	}

	return result, nil
}

type ActivityCheckInCtx struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request
}

func NewActivityCheckInData(ctx context.Context, svcCtx *svc.ServiceContext, r *http.Request) *ActivityCheckInCtx {
	return &ActivityCheckInCtx{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		r:      r,
	}
}

type ActivityCheckInData struct {
	Info struct {
		Number       int64 `json:"number"`
		CouponAmount int64 `json:"coupon_amount"`
	} `json:"info"`
	List []activity.ContractTransformCheckInTask `json:"list"`
}

// GetActivityCheckInData 在活动时间范围&已登录&为圈群用户
func (ac *ActivityCheckInCtx) GetActivityCheckInData(actId int64, uid int64) (ActivityCheckInData, error) {
	activityConfig := activity.GetContractTransformTask()
	checkInTaskList := activityConfig.ContractTransformCheckInTaskList

	req := ActivityCheckInData{}
	if uid == 0 || actId == 0 {
		return req, nil
	}
	cacheList, _ := ac.GetCheckInListCache(actId, uid)
	if len(cacheList.List) != 0 {
		return cacheList, nil
	}

	checkInModel := models.NewActivityCheckInRecordsModel(ac.svcCtx.DBMarketingActivities)

	// 查询所有签到记录
	checkInList, err := checkInModel.GetAllCheckInByUid(ac.ctx, actId, uid)
	if err != nil {
		return req, err
	}
	req.List = checkInTaskList

	todayCheckInCount := len(checkInList)
	//if todayCheckInCount >= len(checkInTaskList) {
	//	return req, nil
	//}

	// 获取是否有爆仓额度
	NetFee, err := GetNetFeeAmount(ac.ctx, ac.r, uid)
	if err != nil {
		return req, nil
	}
	// 获取卡券总额度
	couponSumAmount, err := GetNetFeeCouponAmount(NetFee)
	// fmt.Println(couponSumAmount)
	if err != nil || couponSumAmount == 0 {
		return req, nil
	}

	var CouponSumAmountNew int64
	for key, item := range checkInTaskList {
		if key+1 <= todayCheckInCount {
			checkInTaskList[key].IsCheckIn = 1
		}
		amount := utils.MustInt64(math.Floor(float64(couponSumAmount) * item.PrizeTypeNumRatio))

		checkInTaskList[key].PrizeTypeNum = amount
		CouponSumAmountNew += amount
	}
	number := int64(todayCheckInCount)
	date := utils.TimeUtil{}
	day := date.FormatYmd(date.Now())
	todayCheckInRow, _ := checkInModel.FindTodayCheckInByUid(ac.ctx, actId, uid, day)
	// 今日未签到
	if todayCheckInRow == nil {
		number = number + 1
	}

	req.Info.Number = number
	req.Info.CouponAmount = CouponSumAmountNew
	req.List = checkInTaskList

	ac.SetCheckInListCache(actId, uid, req)
	return req, nil
}

// SetCheckInListCache 设置签到记录缓存
func (ac *ActivityCheckInCtx) SetCheckInListCache(actId int64, uid int64, req ActivityCheckInData) {
	redisKey := consts.ActivityCheckInList(actId, uid)

	// 序列化计划数据
	reqJson, err := json.Marshal(req)
	if err != nil {
		ac.Logger.Errorf(fmt.Sprintf("SetCheckInListCache key:%s |error: %v", redisKey, err))
		return
	}

	_ = ac.svcCtx.Redis.SetexCtx(ac.ctx, redisKey, string(reqJson), 60*5)
	return
}

// GetCheckInListCache 获取签到记录缓存
func (ac *ActivityCheckInCtx) GetCheckInListCache(actId int64, uid int64) (ActivityCheckInData, error) {
	redisKey := consts.ActivityCheckInList(actId, uid)

	req := ActivityCheckInData{}
	result, err := ac.svcCtx.Redis.GetCtx(ac.ctx, redisKey)
	if err != nil {
		return req, err
	}
	err = json.Unmarshal([]byte(result), &req)
	return req, err
}

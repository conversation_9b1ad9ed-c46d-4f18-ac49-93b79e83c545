package apiengine

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/svc"
	"io"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/rest/httpc"
	"bitbucket.org/gateio/gateio-lib-base-go/commonconfig"
	"bitbucket.org/gateio/gateio-lib-base-go/engine"
)

const baseURLField = "BALANCE_ENGINE_URL"

func NewClient() *BalanceClient {
	commonconfig.MustInit()
	engine := engine.NewBaseEngine(
		engine.WithBaseUrlConfFieldName(baseURLField),
	)
	return &BalanceClient{engine: engine}
}

type BalanceClient struct {
	engine *engine.BaseEngine
}

type GetBalanceByUidBody struct {
	JsonRpc string        `json:"jsonrpc"`
	Method  string        `json:"method"`
	ID      int           `json:"id"`
	Params  []interface{} `json:"params"`
}

// 查询用户余额
// https://gtglobal.jp.larksuite.com/wiki/MtC8wgF5hifI8mksm9WjbxGkp0d
// 没有使用
func (c *BalanceClient) GetBalanceByUid(ctx context.Context, svcCtx *svc.ServiceContext, req *BalanceQueryRequest) (*BalanceQueryResponse, error) {
	requestBody := &GetBalanceByUidBody{
		JsonRpc: "2.0",
		Method:  "balance.query",
		ID:      1,
		Params:  []interface{}{req.Uid, req.Asset},
	}
	baseURL := c.engine.GetBaseURL()

	// engine.SendRequest 和 httpc.Do 都不支持data为数组, 所以需要使用httpc.DoRequest
	reqBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("error from gateio-balance engine: failed to marshal request: %w", err)
	}

	r, err := http.NewRequestWithContext(ctx, http.MethodPost, baseURL, bytes.NewReader(reqBody))
	if err != nil {
		return nil, fmt.Errorf("error from gateio-balance engine: %w", err)
	}
	r.Header.Set("Content-Type", "application/json")

	// 设置签名
	key, secret, err := GetKeySecret(ctx, svcCtx)
	if err != nil {
		return nil, fmt.Errorf("error from GetKeySecret: %w", err)
	}
	if len(secret) > 0 {
		sign, err := CalcSign(reqBody, secret)
		if err != nil {
			return nil, fmt.Errorf("error from CalcSign: %w", err)
		}

		r.Header.Set("KEY", key)
		r.Header.Set("SIGN", sign)
	}

	resp, err := httpc.DoRequest(r)
	logx.Info("GetBalanceByUid Req: ", string(reqBody), " Err: ", err, " Url: ", baseURL, " Key: ", r.Header.Get("KEY"), " Sign: ", r.Header.Get("SIGN"))
	if err != nil {
		return nil, fmt.Errorf("error from gateio-balance engine: %w", err)
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error from gateio-balance engine: status code %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error from gateio-balance engine: failed to read response body: %w", err)
	}
	logx.Info("GetBalanceByUid Rsp: ", string(body))

	var result BalanceQueryResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("error from gateio-market engine: failed to decode response body: %w", err)
	}

	return &result, nil
}

/**
 * 获取key、secret
 */
func GetKeySecret(ctx context.Context, svcCtx *svc.ServiceContext) (string, string, error) {
	return svcCtx.SpotEngine.Key, svcCtx.SpotEngine.Secret, nil
}

/**
 * 计算签名
 */
func CalcSign(dataBytes []byte, secret string) (string, error) {
	h := hmac.New(sha512.New, []byte(secret))
	h.Write(dataBytes)
	signature := hex.EncodeToString(h.Sum(nil))
	return signature, nil
}

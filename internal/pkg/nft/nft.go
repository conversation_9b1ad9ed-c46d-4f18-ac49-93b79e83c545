package nft

import (
	"context"
	"crypto/md5"
	"database/sql"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	nftModel "gateio_service_marketing_activity/internal/models/db_nft"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"
	"math/rand"
	"time"

	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"gorm.io/gorm"

	"gorm.io/gorm/clause"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

type Goods struct {
	ID int64 `json:"id"`
	//Token        string `json:"token"`
	//CollectionId int64  `json:"collection_id"`
	NftId string `json:"nft_id"`
}
type Nft interface {
	Mint(userId int64, nftId int, fee float64, currency string) (resp *types.NftMintResponse, err error)
	GetUserGoods(userId int64, collectionId int64, token string) (resp []Goods, err error)
	GetNftCountByUser(userId int64, collectionId int64, token string) (map[int64]int64, error)
	TestInsert(svcCtx *svc.ServiceContext, userId int64, mid int64, nftId int64)
	QueryUserGoodsForActivity(conditions []clause.Expression) (resp []Goods, err error)
	GetMintStatusByMids(mids []int64) (respMap map[int64]string, err error)
	Airdrop(userId int64, nftId string) (int64, error)
}

type nftService struct {
	ctx        context.Context
	svcCtx     *svc.ServiceContext
	mintModel  nftModel.NftMintModel
	goodModel  nftModel.NftAuctionGoodsModel
	stockModel nftModel.NftInStockModel
}

func NewNft(ctx context.Context, svcCtx *svc.ServiceContext) Nft {
	return &nftService{
		ctx:        ctx,
		svcCtx:     svcCtx,
		mintModel:  nftModel.NewNftMintModel(svcCtx.DBNft),
		goodModel:  nftModel.NewNftAuctionGoodsModel(svcCtx.DBNft),
		stockModel: nftModel.NewNftInStockModel(svcCtx.DBNft),
	}
}

func (n *nftService) Airdrop(userId int64, nftId string) (int64, error) {
	officialUid := n.svcCtx.Config.NftMintConf.OfficialUid
	collectionId := n.svcCtx.Config.NftMintConf.CollectionId
	tokenStr := n.svcCtx.Config.NftMintConf.Token
	logc.Info(n.ctx, "[nft_air_drop]获取空投good start:", userId, nftId, officialUid, collectionId, tokenStr)

	// 查找系统用户的nft是否有库存，有获得goods id
	nftGoodItems, err := n.goodModel.GetAirdropGoods(n.ctx, "select * from nft_auction_goods where own_uid=? and collection_id=? and token=? and JSON_EXTRACT(detail, '$.nft_id')=? limit 100 ", officialUid, collectionId, tokenStr, nftId)
	if err != nil {
		logc.Error(n.ctx, "[nft_air_drop]获取空投good 失败:", err, userId, nftId, officialUid, collectionId, tokenStr)
		return 0, errors.New(consts.NFT_AIR_DROP_QUERY_STOCK_ERR, "获取空投good 失败")
	}
	if len(nftGoodItems) == 0 {
		logc.Warn(n.ctx, "[nft_air_drop]获取空投good 失败:", err, userId, nftId, officialUid, collectionId, tokenStr, nftGoodItems)
		return 0, errors.New(consts.NFT_AIR_DROP_NO_STOCK, "空投库存不足")
	}
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomIndex := r.Intn(len(nftGoodItems))
	nftGood := nftGoodItems[randomIndex]
	logc.Infof(n.ctx, "[nft_air_drop]获取空投good成功%v", nftGood)

	// 锁定该good id 30s
	lockKey := fmt.Sprintf("nft_activity_air_drop_%d", nftGood.Id)
	ok, err := n.svcCtx.Redis.SetnxExCtx(n.ctx, lockKey, "1", 30)
	if err != nil || !ok {
		logc.Error(n.ctx, "[nft_air_drop]锁定goods_id 失败:", err, userId, nftId, officialUid, collectionId, tokenStr, nftGood)
		return 0, errors.New(consts.NFT_AIR_DROP_LOCK_FAIL, "锁定goods_id 失败")
	}

	err = n.svcCtx.DBMarketingActivities.Transaction(func(tx *gorm.DB) error {
		// 更新拥有者uid
		changeConditions := []clause.Expression{
			gorm.Expr("id = ?", nftGood.Id),
			gorm.Expr("own_uid = ?", officialUid),
		}
		err = n.goodModel.UpdateGoods(n.ctx, changeConditions, map[string]interface{}{
			"own_uid": userId,
		})
		if err != nil {
			logc.Error(n.ctx, "[nft_air_drop]空投good表失败:", err, userId, nftId, officialUid, collectionId, tokenStr)
			return errors.New(consts.NFT_AIR_DROP_FAIL, "空投good 失败")
		}
		// 更新stock
		stockId := nftGood.InStockId
		stockConditions := []clause.Expression{
			gorm.Expr("id = ?", stockId),
		}
		err = n.stockModel.UpdateStock(n.ctx, stockConditions, map[string]interface{}{
			"uid": userId,
		})
		if err != nil {
			logc.Error(n.ctx, "[nft_air_drop]空投stock 失败:", err, userId, nftId, officialUid, collectionId, tokenStr)
			return errors.New(consts.NFT_AIR_DROP_FAIL, "空投good 失败")
		}
		return nil
	})
	if err != nil {
		logc.Errorf(n.ctx, "[nft_air_drop]空投事务error: %v", err)
		return 0, errors.New(consts.NFT_AIR_DROP_TRANSACTION_FAIL, err.Error())
	}

	// 清理nft good cache : [nft auction goods detail:]
	goodCacheStr := fmt.Sprintf("%s%d", "nft auction goods detail:", nftGood.Id)
	goodCacheKey := fmt.Sprintf("%x", md5.Sum([]byte(goodCacheStr)))[:12]
	_, err = n.svcCtx.NftRedis.Del(goodCacheKey)
	if err != nil {
		logc.Error(n.ctx, "[nft_air_drop]空投删除缓存失败:", err, userId, nftId, nftGood, goodCacheKey)
	}

	return nftGood.Id, nil
}
func (n *nftService) Mint(userId int64, nftId int, fee float64, currency string) (resp *types.NftMintResponse, err error) {
	mintParams := make(map[string]int)
	mintParams["tokenType"] = nftId

	jsonData, err := json.Marshal(mintParams)
	if err != nil {
		logc.Error(n.ctx, "JSON 序列化mint params失败:", err)
		return
	}

	requestData := &nftModel.NftMint{
		Uid:          userId,
		Token:        n.svcCtx.Config.NftMintConf.Token,
		CollectionId: n.svcCtx.Config.NftMintConf.CollectionId,
		Status:       "MINT_REQUEST",
		FeeAmount:    fee,
		FeeCurrType:  currency,
		MintParams:   string(jsonData),
		CreateTimest: time.Now(),
		UpdateTimest: time.Now(),
		InternalMemo: "",
	}
	err = n.mintModel.Insert(n.ctx, requestData)
	if err != nil {
		logc.Error(n.ctx, "submit mint error", err)
		return
	}

	resp = &types.NftMintResponse{
		Mid: requestData.Mid,
	}

	return resp, nil
}

/**
 * 查询铸造状态
 */
func (n *nftService) GetMintStatusByMids(mids []int64) (respMap map[int64]string, err error) {
	// 查询数据库获取多条记录
	records, err := n.mintModel.FindByIds(n.ctx, mids)
	if err != nil {
		return nil, err
	}
	respMap = make(map[int64]string, len(records))
	for _, record := range records {
		respMap[record.Mid] = record.Status
	}
	return respMap, nil
}

func (n *nftService) GetUserGoods(userId int64, collectionId int64, token string) (resp []Goods, err error) {
	if collectionId == 0 {
		collectionId = n.svcCtx.Config.NftMintConf.CollectionId
	}

	if token == "" {
		token = n.svcCtx.Config.NftMintConf.Token
	}

	lists, err := n.goodModel.GetUserGoodsNoPagination(n.ctx, userId, collectionId, token)

	if err != nil {
		logc.Error(n.ctx, "GetUserGoods error:", err)
		return
	}
	resp = make([]Goods, 0, len(lists))
	var goodDetail struct {
		NftId string `json:"nft_id"`
	}
	for _, goodItem := range lists {
		if goodItem.Detail.Valid && goodItem.Detail.String != "" {
			err = json.Unmarshal([]byte(goodItem.Detail.String), &goodDetail)
			if err != nil {
				continue
			}
			resp = append(resp, Goods{
				ID:    goodItem.Id,
				NftId: goodDetail.NftId,
			})
		}

	}
	return
}

func (n *nftService) QueryUserGoodsForActivity(conditions []clause.Expression) (resp []Goods, err error) {
	lists, err := n.goodModel.GetUserGoodsByConditionNoPagination(n.ctx, conditions)
	if err != nil {
		logc.Error(n.ctx, "QueryUserGoodsForActivity error:", err)
		return
	}
	resp = make([]Goods, 0, len(lists))
	var goodDetail struct {
		NftId string `json:"nft_id"`
	}
	for _, goodItem := range lists {
		if goodItem.Detail.Valid && goodItem.Detail.String != "" {
			err = json.Unmarshal([]byte(goodItem.Detail.String), &goodDetail)
			if err != nil {
				continue
			}
			resp = append(resp, Goods{
				ID:    goodItem.Id,
				NftId: goodDetail.NftId,
			})
		}

	}
	return
}

/**
 * 获取用户每个nft的数量
 */
func (n *nftService) GetNftCountByUser(userId int64, collectionId int64, token string) (map[int64]int64, error) {
	resp, err := n.GetUserGoods(userId, collectionId, token)
	if err != nil {
		return nil, err
	}

	nftCount := make(map[int64]int64)
	for _, good := range resp {
		nftCount[cast.ToInt64(good.NftId)]++
	}
	return nftCount, nil
}

/**
 * 测试方法-生成nft铸造数据
 */
func (n *nftService) TestInsert(svcCtx *svc.ServiceContext, userId int64, mid int64, nftId int64) {
	detailMap := map[string]interface{}{
		"name":   "Red Bull NFT",
		"nft_id": cast.ToString(nftId),
	}
	detailJson, _ := json.Marshal(detailMap)
	orderId, _ := utils.GetOrderId(n.ctx, n.svcCtx)

	if userId != 2124327704 {
		n.goodModel.Insert(n.ctx, &nftModel.NftAuctionGoods{
			OwnUid:          userId,
			CollectionId:    n.svcCtx.Config.NftMintConf.CollectionId,
			Token:           n.svcCtx.Config.NftMintConf.Token,
			TokenId:         cast.ToString(orderId),
			Type:            "NFT",
			WorkType:        1,
			SealStatus:      1,
			Auctioneers:     "",
			CategoryId:      0,
			TopicId:         0,
			PaymentCurrency: "USDT",
			BuyoutPrice:     decimal.NewFromFloat(0.0),
			StartingPrice:   decimal.NewFromFloat(0.0),
			ReservePrice:    decimal.NewFromFloat(0.0),
			Margin:          decimal.NewFromFloat(0.0),
			BaseBidPrice:    decimal.NewFromFloat(0.0),
			LastBidPrice:    decimal.NewFromFloat(0.0),
			LastBidUid:      0,
			BidTimes:        0,
			WinBidId:        0,
			CreateUid:       userId,
			Detail: sql.NullString{
				String: string(detailJson),
				Valid:  true,
			},
			Describes:       sql.NullString{},
			DescribesCn:     sql.NullString{},
			Copyright:       sql.NullString{},
			CopyrightCn:     sql.NullString{},
			RoyaltyRate:     decimal.NewFromFloat(0.0),
			Status:          "INIT",
			CreateTimest:    time.Now(),
			UpdateTimest:    time.Now(),
			OperationTimest: time.Now(),
			Mid:             mid,
		})
	}

	// 更新状态为已完成
	setStatus := nftModel.StatusMintSuccess
	if userId == 2124327704 {
		setStatus = nftModel.StatusMintFail
	}
	n.mintModel.UpdateOne(n.ctx, mid, []string{"status"}, &nftModel.NftMint{
		Status: setStatus,
	})
}

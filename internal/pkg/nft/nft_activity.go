package nft

import (
	"bitbucket.org/gateio/gateio-lib-base-go/environment"
	"context"
	"encoding/json"
	"gateio_service_marketing_activity/internal/models/nft_mint_log"

	"bitbucket.org/gatebackend/go-zero/rest/errors"

	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/models/nft_detail"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"
	"time"

	"github.com/spf13/cast"

	"bitbucket.org/gatebackend/go-zero/core/logx"
)

// 数据库活动信息转化成返回值中的活动信息
func NftActivityToResp(list []*nft_activity.NftActivity) []*types.ActivityInfo {
	respList := make([]*types.ActivityInfo, len(list))
	for i, item := range list {
		activityStatus := 1
		if item.StartTime.After(time.Now()) {
			activityStatus = 3
		}
		if item.EndTime.Before(time.Now()) {
			activityStatus = 2
		}
		taskStep := GetTaskStep(item)
		respList[i] = &types.ActivityInfo{
			ActivityID:                 item.ActivityId,
			ActivityName:               item.ActivityName,
			StartTime:                  item.StartTime.Format(consts.GoFormatTimeStr),
			EndTime:                    item.EndTime.Format(consts.GoFormatTimeStr),
			ReceiveStartTime:           item.ReceiveStartTime.Format(consts.GoFormatTimeStr),
			ReceiveEndTime:             item.ReceiveEndTime.Format(consts.GoFormatTimeStr),
			TaskStartTime:              item.TaskStartTime.Format(consts.GoFormatTimeStr),
			TaskEndTime:                item.TaskEndTime.Format(consts.GoFormatTimeStr),
			ActivityStatus:             activityStatus,
			SurpriseExtensionStartTime: item.SurpriseExtensionStartTime.Format(consts.GoFormatTimeStr),
			SurpriseExtensionEndTime:   item.SurpriseExtensionEndTime.Format(consts.GoFormatTimeStr),
			TaskStep:                   taskStep,
			SettlementStartTime:        item.SettlementStartTime.Unix(),
			SettlementEndTime:          item.SettlementEndTime.Unix(),
			DrawStartTime:              item.DrawStartTime.Unix(),
			DrawEndTime:                item.DrawEndTime.Unix(),
			AnnouncementStartTime:      item.AnnouncementStartTime.Unix(),
			AnnouncementEndTime:        item.AnnouncementEndTime.Unix(),
		}
	}
	return respList
}

const (
	ACTIVITY_STEP_NOT_START = 0 // 未开始
	ACTIVITY_STEP_RECEIVE   = 1 // 领取期
	ACTIVITY_STEP_TASK      = 2 // 任务期
	ACTIVITY_STEP_SURPRISE  = 3 // 惊喜任务期
	ACTIVITY_STEP_DRAW      = 4 // 表示整个抽奖期：从结算开始到结果展示结束
)

/**
 * 获取任务所属阶段
 */
func GetTaskStep(activity *nft_activity.NftActivity) int {
	currentTime := time.Now()
	// 检查是否在领取时间范围内
	if activity.ReceiveStartTime.Before(currentTime) && activity.ReceiveEndTime.After(currentTime) {
		return ACTIVITY_STEP_RECEIVE
	}
	// 检查是否在任务时间范围内
	if activity.TaskStartTime.Before(currentTime) && activity.TaskEndTime.After(currentTime) {
		return ACTIVITY_STEP_TASK
	}
	// 检查是否在惊喜扩展时间范围内
	if activity.SurpriseExtensionStartTime.Before(currentTime) && activity.SurpriseExtensionEndTime.After(currentTime) {
		return ACTIVITY_STEP_SURPRISE
	}

	// 抽奖期的定义：如果有惊喜活动时间，则从惊喜活动时间开始算，如果没有惊喜活动时间就是任务结束开始算
	drawDefinitionStartTime := activity.TaskEndTime
	// 惊喜活动开始时间和结束时间相等则认为没有惊喜活动时间
	if !activity.SurpriseExtensionStartTime.Equal(activity.SurpriseExtensionEndTime) {
		drawDefinitionStartTime = activity.SurpriseExtensionEndTime
	}
	if drawDefinitionStartTime.Before(currentTime) && activity.AnnouncementEndTime.After(currentTime) {
		return ACTIVITY_STEP_DRAW
	}

	// 未开始
	return ACTIVITY_STEP_NOT_START
}

// 查询指定活动
func GetNftActivity(svcCtx *svc.ServiceContext, ctx context.Context, activityID int64, isCentralized bool) (*nft_activity.NftActivity, error) {
	redisKey := fmt.Sprintf(consts.NftActivityDetailKey, activityID, cast.ToInt(isCentralized))
	if environment.IsPre() {
		redisKey = redisKey + "_:pre"
	}
	redisValue, _ := svcCtx.Redis.Get(redisKey)
	activity := &nft_activity.NftActivity{}
	var err error
	if redisValue == "" {
		activity, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetNftActivity(ctx, activityID, isCentralized)
		if err != nil {
			return nil, err
		}
		activityByte, err := json.Marshal(activity)
		if err == nil {
			//缓存十分钟
			_ = svcCtx.Redis.Setex(redisKey, string(activityByte), 30)
		}
	} else {
		_ = json.Unmarshal([]byte(redisValue), &activity)
	}
	return activity, nil
}

// 查询活动下的所有NFT
func GetNftList(svcCtx *svc.ServiceContext, ctx context.Context, activityID int64) ([]*nft_detail.NftDetail, error) {
	redisKey := fmt.Sprintf(consts.NftActivityNftListKey, activityID)
	if environment.IsPre() {
		redisKey = redisKey + "_:pre"
	}
	redisValue, _ := svcCtx.Redis.Get(redisKey)
	nftList := []*nft_detail.NftDetail{}
	var err error
	if redisValue == "" {
		nftList, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetNftList(ctx, activityID, 0, 0, 0)
		if err != nil {
			return nil, err
		}
		nftListByte, err := json.Marshal(nftList)
		if err == nil {
			//缓存十分钟
			_ = svcCtx.Redis.Setex(redisKey, string(nftListByte), 60*10)
		}
	} else {
		_ = json.Unmarshal([]byte(redisValue), &nftList)
	}
	return nftList, nil
}

// 查询活动下的所有NFT
func GetNftListByNftType(svcCtx *svc.ServiceContext, ctx context.Context, nftType int) ([]*nft_detail.NftDetail, error) {
	redisKey := fmt.Sprintf(consts.NftTypeNftListKey, nftType)
	if environment.IsPre() {
		redisKey = redisKey + "_:pre"
	}
	redisValue, _ := svcCtx.Redis.Get(redisKey)
	nftList := []*nft_detail.NftDetail{}
	var err error
	if redisValue == "" {
		nftList, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetNftList(ctx, 0, 0, nftType, 0)
		if err != nil {
			return nil, err
		}
		nftListByte, err := json.Marshal(nftList)
		if err == nil {
			//缓存十分钟
			_ = svcCtx.Redis.Setex(redisKey, string(nftListByte), 60*10)
		}
	} else {
		_ = json.Unmarshal([]byte(redisValue), &nftList)
	}
	return nftList, nil
}

// 查询活动下的所有NFT领取记录
func GetNftMintLogList(svcCtx *svc.ServiceContext, ctx context.Context, uid int64, walletAddress string, activityID int64) ([]*NftMintLog, error) {
	dbMintNftLogs := []*nft_mint_log.NftMintLog{}
	var err error
	if uid > 0 || walletAddress != "" {
		dbMintNftLogs, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetNftMintLogList(ctx, uid, walletAddress, 0, 0, activityID, 0)
		if err != nil {
			return nil, err
		}
	}
	mintNftLogs := make([]*NftMintLog, len(dbMintNftLogs))
	for i, dbMintNftLog := range dbMintNftLogs {
		mintNftLogs[i] = &NftMintLog{
			NftId:            dbMintNftLog.NftId,
			ParentNftId:      dbMintNftLog.ParentNftId,
			NftUserSubTaskId: dbMintNftLog.NftUserSubTaskId,
			WalletAddress:    dbMintNftLog.WalletAddress,
			Uid:              dbMintNftLog.Uid,
			MintStatus:       dbMintNftLog.MintStatus,
			Expenditure:      dbMintNftLog.Expenditure,
			OperationId:      dbMintNftLog.OperationId,
		}
	}
	return mintNftLogs, nil
}

// 定义返回的结构体
type MintNftResponse struct {
	ParentNftId             int64  // 可铸造的parentNftId 如果为0，代表不可铸造
	Message                 string // 不可铸造的描述
	FreeReceiveTimeWindow   bool   // 免费领取 - 时间窗口
	ExtraActivityTimeWindow bool   // 额外活动 - 时间窗口
	Error                   error  // 错误信息
	TaskStep                int    // 任务所属阶段
}

var (
	HAS_NFT_PROGRESS              error = errors.New(consts.NFT_ACTIVITY_HAS_NFT_PROGRESS, "Please do not claim until you have completed the last collection minting")
	NFT_ACTIVITY_ALREADY_CASTING  error = errors.New(consts.NFT_ACTIVITY_ALREADY_CASTING, "所有的普通nft都已产生领取记录")
	NFT_ACTIVITY_REPEAT_RECEIVE   error = errors.New(consts.NFT_ACTIVITY_REPEAT_RECEIVE, "parentNftId已有领取记录")
	NFT_ACTIVITY_HAS_NFT_PROGRESS error = errors.New(consts.NFT_ACTIVITY_HAS_NFT_PROGRESS, "存在铸造中的nft")
	NFT_ACTIVITY_SUB_TASK_CASTED  error = errors.New(consts.NFT_ACTIVITY_SUB_TASK_CASTED, "任务已铸造过NFT，不能重复铸造")
)

/**
 * 验证NFT当前时间是否可铸造
 * @param activityId 		// 活动ID	（必须）
 * @param parentNftId 		// 父NFTID	（领取期parentNftId必传）
 * @param walletAddress		// 钱包地址	  (非必须)
 * @param userId			// 用户ID	 (非必须)
 * @param nftUserSubTaskID	// 子任务ID	 (额外期必传)
 * @param isHighGrade       //是否是高等级nft 1:是
 * @return MintNftResponse
 */
func VerifyMintNft(svcCtx *svc.ServiceContext, ctx context.Context, activityId int64, parentNftId int64, userId int64, walletAddress string, nftUserSubTaskID int64, isHighGrade int, nftId int64, isCentralized bool) MintNftResponse {
	// 请求参数（日志）
	req := map[string]interface{}{}
	req["activity_id"] = activityId
	req["parent_nft_id"] = parentNftId
	req["user_id"] = userId
	req["wallet_address"] = walletAddress
	req["nft_user_sub_task_id"] = nftUserSubTaskID
	reqByte, _ := json.Marshal(req)

	// 数据库验证
	activity, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetNftActivity(ctx, activityId, isCentralized)
	if err != nil {
		logx.Infof("pkg VerifyMintNft  is err, params is:%s,err is:%v", string(reqByte), err)
		return MintNftResponse{
			ParentNftId: 0,
			Message:     "",
			Error:       err,
		}
	}
	if activity.ActivityId <= 0 {
		logx.Infof("pkg VerifyMintNft activity is null, params is:%s", string(reqByte))
		return MintNftResponse{
			ParentNftId: 0,
			Message:     "活动不存在",
			Error:       nil,
		}
	}

	// 判断活动是否结束
	if activity.EndTime.Before(time.Now()) {
		logx.Infof("pkg VerifyMintNft activity end, activity.EndTime is:%s", activity.EndTime.Format(consts.GoFormatTimeStr))
		return MintNftResponse{
			ParentNftId: 0,
			Message:     "活动已结束",
			Error:       nil,
		}
	}

	// 判断活动是否开始
	if activity.StartTime.After(time.Now()) {
		logx.Infof("pkg VerifyMintNft activity not start, activity.StartTime is:%s", activity.StartTime.Format(consts.GoFormatTimeStr))
		return MintNftResponse{
			ParentNftId: 0,
			Message:     "活动未开始",
			Error:       nil,
		}
	}

	// 获取任务所属阶段（惊喜阶段，逻辑不在这里处理）
	taskStep := GetTaskStep(activity)
	if taskStep == 3 {
		return MintNftResponse{
			ParentNftId: 0,
			Message:     "活动未开始",
			Error:       nil,
			TaskStep:    ACTIVITY_STEP_SURPRISE,
		}
	}

	// 判断是否在领取期
	receiveNow := false
	if activity.ReceiveStartTime.Before(time.Now()) && activity.ReceiveEndTime.After(time.Now()) {
		receiveNow = true
	}

	// 判断是否在额外活动期
	taskNow := false
	if activity.TaskStartTime.Before(time.Now()) && activity.TaskEndTime.After(time.Now()) {
		taskNow = true
	}
	// 通过任务获取的nft
	if taskNow && nftUserSubTaskID > 0 {
		logx.Infof("current is task step & nftUserSubTaskID:%d is not zero", nftUserSubTaskID)
		return MintNftResponse{
			ParentNftId: 0,
			Message:     "活动领取nft",
			Error:       nil,
			TaskStep:    ACTIVITY_STEP_TASK,
		}
	}

	// 领取期parentNftId必传
	if receiveNow && parentNftId <= 0 {
		logx.Infof("pkg VerifyMintNft receiveNow:%s,%s,%d", activity.ReceiveStartTime.Format(consts.GoFormatTimeStr), activity.ReceiveEndTime.Format(consts.GoFormatTimeStr), parentNftId)
		return MintNftResponse{
			ParentNftId: 0,
			Message:     "参数错误",
			Error:       nil,
		}
	}
	nftType := 1
	//如果是高等及就查金银nft
	if isHighGrade == 1 {
		nftType = 0
	}
	// 获取nft列表
	nftList := make([]*nft_detail.NftDetail, 0)
	// 获取nft列表 领取和购买场景
	if parentNftId > 0 && nftUserSubTaskID <= 0 {
		nftList, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetNftList(ctx, activityId, parentNftId, nftType, isHighGrade)
		if err != nil {
			logx.Infof("pkg VerifyMintNft GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
			return MintNftResponse{
				ParentNftId: 0,
				Message:     "",
				Error:       err,
			}
		}
	}

	if len(nftList) == 0 {
		logx.Infof("pkg VerifyMintNft nftList  is null, req is:%s", string(reqByte))
		return MintNftResponse{
			ParentNftId: 0,
			Message:     "未找到对应NFT",
			Error:       nil,
		}
	}
	for _, nftInfo := range nftList {
		if nftId > 0 && nftInfo.NftId != nftId {
			logx.Infof("pkg VerifyMintNft req.NftID not is null nftList  is null, req is:%s", string(reqByte))
			return MintNftResponse{
				ParentNftId: 0,
				Message:     "nftId错误",
				Error:       nil,
			}
		}
	}
	// 免费领取期
	if receiveNow {
		// 判断是否可领取
		nftType := int64(0)
		for _, nft := range nftList {
			nftType = nft.NftType
			// 判断当前parentNftID是否在领取期
			if nft.CampaignStartTime.After(time.Now()) || nft.CampaignEndTime.Before(time.Now()) {
				//当前parentNftID不在领取期
				paramByte, _ := json.Marshal(nft)
				logx.Infof("pkg VerifyMintNft nft not in recevie time, params is:%s", string(paramByte))
				return MintNftResponse{
					ParentNftId: 0,
					Message:     "parentNftId 不在领取期" + cast.ToString(nft.Id),
					Error:       nil,
				}
			}
		}
		mintNftLogs, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetNftMintLogListByNftId(ctx, userId, walletAddress, parentNftId, 0, activityId, 0, nftId)
		if err != nil {
			logx.Infof("pkg VerifyMintNft GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
			return MintNftResponse{
				ParentNftId: 0,
				Message:     "",
				Error:       err,
			}
		}
		// 同一个普通nft 在可领取期只能领取一次
		if nftType == 1 {
			if len(mintNftLogs) > 0 {
				logx.Infof("pkg VerifyMintNft mintNftLogs len > 0, params is:%d", len(mintNftLogs))
				return MintNftResponse{
					ParentNftId: 0,
					Message:     "parentNftId已有领取记录",
					Error:       NFT_ACTIVITY_REPEAT_RECEIVE,
				}
			}
		} else {
			for _, mintNftLog := range mintNftLogs {
				//同一个用的同一个nft如果有铸造中的状态也要拦截
				if mintNftLog.MintStatus < 3 && mintNftLog.NftId <= 0 {
					logx.Infof("pkg VerifyMintNft same nft minting data is:%d,%d", mintNftLog.NftId, mintNftLog.MintStatus)
					return MintNftResponse{
						ParentNftId: 0,
						Message:     "same nft in minting",
						Error:       NFT_ACTIVITY_HAS_NFT_PROGRESS,
					}
				}
			}
		}
	}

	//额外活动期, //正常购买
	if taskNow && nftUserSubTaskID <= 0 {
		// 如果购买的，不是普通的NFT
		for _, nftInfo := range nftList {
			if nftInfo.NftType != nft_detail.NftTypeNormal {
				return MintNftResponse{
					ParentNftId: 0,
					Message:     "额外活动期只能购买普通nft",
					Error:       nil,
				}
			}
		}
		//查询用户所有铸造记录的nft
		mintNftLogs, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetNftMintLogList(ctx, userId, walletAddress, 0, 0, activityId, 0)
		if err != nil {
			logx.Infof("pkg VerifyMintNft GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
			return MintNftResponse{
				ParentNftId: 0,
				Message:     "",
				Error:       err,
			}
		}
		for _, mintNftLog := range mintNftLogs {
			//检查同一个人的同一个nft是否在铸造中
			if mintNftLog.NftUserSubTaskId <= 0 && mintNftLog.MintStatus < 3 && mintNftLog.ParentNftId == parentNftId && mintNftLog.NftId == nftId {
				logx.Infof("pkg VerifyMintNft same nft minting data is:%d,%d", mintNftLog.NftId, mintNftLog.MintStatus)
				return MintNftResponse{
					ParentNftId: 0,
					Message:     "same nft in minting",
					Error:       nil,
				}
			}
		}

	}
	return MintNftResponse{
		FreeReceiveTimeWindow:   receiveNow,
		ExtraActivityTimeWindow: taskNow,
		ParentNftId:             parentNftId,
		Message:                 "",
		Error:                   nil,
	}
}

// 查询用户最后一个操作的铸造记录
func GetLastNftMintLogList(svcCtx *svc.ServiceContext, ctx context.Context, activityID, uid int64, walletAddress string, operationId int64) (*types.RollNftMintResultResp, error) {
	redisKey := fmt.Sprintf(consts.NftRollMintLogKey, activityID, uid, walletAddress, operationId)
	logx.Info("GetLastNftMintLogList rediskey is:", redisKey)
	/*redisValue, err := svcCtx.Redis.Get(redisKey)
	if err != nil {
		logx.Info("GetLastNftMintLogList Redis.Get is err:", redisKey, err.Error())
	}

	if redisValue == "" {*/
	resp := &types.RollNftMintResultResp{}
	dbMintNftLogs, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetNftMintLogListByOperation(ctx, activityID, uid, walletAddress, operationId)
	if err != nil {
		return nil, err
	}
	nftCountMap := map[int64]int{}
	//获取当前状态
	mintStatus := 2
	for _, dbMintNftLog := range dbMintNftLogs {
		if dbMintNftLog.NftId > 0 {
			nftCountMap[dbMintNftLog.NftId] += 1
		}
		if dbMintNftLog.MintStatus < 3 {
			//进行中状态
			mintStatus = 1
			break
		}
		if dbMintNftLog.MintStatus == 4 {
			//失败状态
			mintStatus = 3
			break
		}
	}
	if len(dbMintNftLogs) < 1 {
		mintStatus = 1
	}
	resp = &types.RollNftMintResultResp{
		MintStatus: mintStatus, // 铸造状态 1:进行中 2:成功 3:失败 当状态是 2 的时候会展示铸造成功的 nft 信息
	}
	//当状态是 2 的时候会展示铸造成功的 nft 信息
	if mintStatus == 2 {
		nftIds := make([]int64, 0, len(nftCountMap))
		for nftId := range nftCountMap {
			nftIds = append(nftIds, nftId)
		}
		nftList, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetNftListByNftIds(ctx, activityID, nftIds)
		if err != nil {
			return nil, err
		}
		nftProfs := make([]*types.NftProf, len(nftList))
		for i, nft := range nftList {
			nftProfs[i] = &types.NftProf{
				NftID:      nft.NftId,
				NftName:    nft.NftName,
				NftIconURL: nft.NftIconUrl,
				NftAmount:  nftCountMap[nft.NftId],
			}
		}
		resp.NftList = nftProfs
	}
	/*respByte, err := json.Marshal(resp)
		if err == nil {
			//缓存十分钟
			err = svcCtx.Redis.Setex(redisKey, string(respByte), 60*10)
			if err != nil {
				logx.Info("GetLastNftMintLogList Redis.Setex is err:", redisKey, err.Error())
			}
		}

	} else {
		_ = json.Unmarshal([]byte(redisValue), resp)
	}*/
	return resp, nil
}

// 格式化数据
func NftTaskListFormat(taskList []*nft.NftTask, subTaskList []*nft.NftUserSubTask, nftSubTaskOperationMap map[int64]int64) (rspList []*types.NftTaskListItem) {
	for _, task := range taskList {
		// 组装子任务数据
		doneLimit := 0
		rspSubTaskList := make([]types.SubTaskItem, 0)
		for _, taskRecord := range subTaskList {
			if taskRecord.TaskId == task.TaskId {
				// 帮助完成任务的用户信息
				helpUserId := taskRecord.HelpUserId
				helpUserId = utils.AbbreviateString(taskRecord.HelpUserId)
				// 组装数据
				rspSubTaskList = append(rspSubTaskList, types.SubTaskItem{
					SubTaskId:        taskRecord.Id,
					HelpUserId:       helpUserId,
					HelpUserAvatar:   taskRecord.HelpUserAvatar,
					ReceiveNftStatus: taskRecord.Status,
					OperationID:      nftSubTaskOperationMap[taskRecord.Id],
				})
				// 完成次数
				if taskRecord.Status > 0 {
					doneLimit++
				}
			}

		}

		// 主任务数据
		rspList = append(rspList, &types.NftTaskListItem{
			TaskId:          task.TaskId,             // 任务ID
			TaskName:        task.TaskName,           // 任务名称
			TaskType:        task.TaskType,           // 任务类型
			DoneLimit:       cast.ToInt64(doneLimit), // 已完成次数
			CompletionLimit: task.CompletionLimit,    // 可完成次数
			MediaId:         task.MediaId,
			SubTaskList:     rspSubTaskList,
		})
	}
	return
}

// 查询用户子任务信息
func GetSubTaskList(svcCtx *svc.ServiceContext, ctx context.Context, activityId, userId int64, walletAddress string) ([]*nft.NftUserSubTask, error) {
	redisKey := fmt.Sprintf(consts.NftUserSubTaskKey, activityId, userId, walletAddress)
	redisValue, _ := svcCtx.Redis.Get(redisKey)
	subTaskList := []*nft.NftUserSubTask{}
	var err error
	if redisValue == "" {
		subTaskList, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetNftSubTaskList(ctx, activityId, userId, walletAddress)
		if err != nil {
			return nil, err
		}
		if len(subTaskList) > 0 {
			nftListByte, err := json.Marshal(subTaskList)
			if err == nil {
				//缓存十分钟
				_ = svcCtx.Redis.Setex(redisKey, string(nftListByte), 60*10)
			}
		}
	} else {
		_ = json.Unmarshal([]byte(redisValue), &subTaskList)
	}
	return subTaskList, nil
}

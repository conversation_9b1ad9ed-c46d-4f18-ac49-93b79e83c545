package nft

import (
	"context"
	"database/sql"
	"fmt"
	nftModels "gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/svc"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
)

// GenerateAndSaveUserSubTask 创建任务的用户记录，会加锁，保证无并发问题。只会任务下面缺少的任务记录
func GenerateAndSaveUserSubTask(ctx context.Context, svcCtx *svc.ServiceContext, activityID, uid int64, taskList []*nftModels.NftTask) ([]*nftModels.NftUserSubTask, error) {
	lockKey := fmt.Sprintf("nft_create_user_sub_task_%d", uid)
	ok, err := svcCtx.Redis.SetnxExCtx(ctx, lockKey, "1", 5)
	if err != nil || !ok {
		logc.Warnf(ctx, "[GenerateAndSaveUserSubTask]redis设置值失败, ok: %t, err: %v", ok, err)
		return nil, err
	}

	userSubTaskModel := nftModels.NewNftUserSubTaskModel(svcCtx.DBMarketingActivities)

	taskTypeList := make([]int64, 0)
	for _, task := range taskList {
		taskTypeList = append(taskTypeList, task.TaskType)
	}
	// 先查询出某个任务已经有几个用户子任务了
	existedRecords, err := userSubTaskModel.ListTaskRecord(ctx, activityID, uid, taskTypeList)
	if err != nil {
		logc.Warnf(ctx, "[GenerateAndSaveUserSubTask]ListTaskRecord, err: %v", err)
		return nil, err
	}
	existedTaskType2RecordMap := make(map[int64][]*nftModels.NftUserSubTask)
	for _, record := range existedRecords {
		existedTaskType2RecordMap[record.TaskType] = append(existedTaskType2RecordMap[record.TaskType], record)
	}

	taskSubRecordList := make([]*nftModels.NftUserSubTask, 0)
	for _, task := range taskList {
		var i int64 = int64(len(existedTaskType2RecordMap[task.TaskType]))
		for i < task.CompletionLimit {
			taskSubRecordList = append(taskSubRecordList, &nftModels.NftUserSubTask{
				ActivityId:     activityID,
				UserId:         uid,
				WalletAddress:  "",
				TaskId:         task.Id,
				TaskType:       task.TaskType,
				Status:         nftModels.TaskStatusInitial,
				HelpUserId:     "",
				HelpUserAvatar: "",
				MediaId:        "",
				ExtraData:      sql.NullString{},
				SceneType:      task.SceneType,
			})
			i++
		}
	}

	// 保存用户子任务记录的数据

	err = userSubTaskModel.BatchCreateTaskRecord(ctx, taskSubRecordList)
	if err != nil {
		logx.WithContext(ctx).Errorf("[RegTaskCallBack]BatchCreateTaskRecord err: %v", err)
		return nil, err
	}
	// 最后要释放锁
	_, _ = svcCtx.Redis.Del(lockKey)

	for _, record := range existedRecords {
		if record.Status == nftModels.TaskStatusInitial {
			taskSubRecordList = append(taskSubRecordList, record)
		}
	}

	return taskSubRecordList, nil
}

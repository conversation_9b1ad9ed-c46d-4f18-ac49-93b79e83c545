package nft

import "bitbucket.org/gatebackend/go-zero/core/decimal"

type NftMintLog struct {
	NftId            int64           `json:"nft_id"`
	ParentNftId      int64           `json:"parent_nft_id"`        // 上级ID 用于分层 一期6个
	NftUserSubTaskId int64           `json:"nft_user_sub_task_id"` // 任务记录ID 用于判断
	WalletAddress    string          `json:"wallet_address"`       // 钱包标识 去中心化领取或购买的钱包ID 用于判断用户重复领取或购买
	Uid              int64           `json:"uid"`                  // uid 中心化领取或购买的用户ID
	MintStatus       int64           `json:"mint_status"`          // 铸造状态 1初始化 2进行中 3成功 4失败
	Expenditure      decimal.Decimal `json:"expenditure"`
	OperationId      int64           `json:"operation_id"`
}

const (
	BUY_NFT_CURRENCY = "USDT"
)

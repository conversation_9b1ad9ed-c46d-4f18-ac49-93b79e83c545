package logic

import (
	"context"
	"net/http"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/engine/auth"
)

type GetUserIdentityLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// web-获取用户身份
func NewGetUserIdentityLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserIdentityLogic {
	return &GetUserIdentityLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserIdentityLogic) GetUserIdentity(r *http.Request, userInfo *auth.UserInfo) (resp *types.GetUserIdentityResp, err error) {
	resp = &types.GetUserIdentityResp{}

	mmUidList, err := dao.NewFeeSettingMysqlDBUtil(l.svcCtx.FeeSettingDB).GetMmUidAry(l.svcCtx, l.ctx)
	if err != nil {
		l.Logger.Infof("GetUserIdentity GetMmUidAry is err: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	// 是否做市商用户 1:是 (切换为：mm_white_list_new表)
	if utils.ContainsArray(mmUidList, int64(userInfo.UID)) {
		resp.IsMarketplace = 1
	}
	// 是否企业用户 1:是
	if userInfo.Verified == consts.KycLv3 {
		resp.IsEnterprise = 1
	}
	// 是否子账号 1:是
	if userInfo.IsSub == 1 {
		resp.IsSub = 1
	}
	//  agency_type 代理商类型 0: 普通用户 1: 代理商 2: 代理商推荐注册用户 3:合伙人身份；4:合伙人邀请的用户
	resp.AgencyType = userInfo.AgencyType

	return
}

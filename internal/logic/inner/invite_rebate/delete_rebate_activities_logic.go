package invite_rebate

import (
	"context"
	"errors"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type DeleteRebateActivitiesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除邀请反佣页活动
func NewDeleteRebateActivitiesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteRebateActivitiesLogic {
	return &DeleteRebateActivitiesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteRebateActivitiesLogic) DeleteRebateActivities(req *types.DeleteRebateActivitiesReq) (resp *types.DeleteRebateActivitiesResp, err error) {
	resp = &types.DeleteRebateActivitiesResp{}

	oldAct, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindRebateActivitiesById(l.ctx, req.Id)
	if err != nil {
		return nil, errors.New(err.Error())
	}
	if oldAct.ConfigStatus == consts.InviteRebateOnline {
		return nil, errors.New("禁用时配置才可以删除")
	}

	err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).DeleteRebateActivity(l.ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

package invite_rebate

import (
	"context"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/invite_rebate_activity"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type OfflineRebateActivitiesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 禁用邀请反佣页活动
func NewOfflineRebateActivitiesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *OfflineRebateActivitiesLogic {
	return &OfflineRebateActivitiesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *OfflineRebateActivitiesLogic) OfflineRebateActivities(req *types.OfflineRebateActivitiesReq) (resp *types.OfflineRebateActivitiesResp, err error) {
	resp = &types.OfflineRebateActivitiesResp{}

	data := &invite_rebate_activity.InviteRebateActivity{
		Id:           req.Id,
		ConfigStatus: consts.InviteRebateOffine,
	}
	_, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).UpdateRebateActivities(l.ctx, data)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

package invite_rebate

import (
	"context"
	"sort"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/cc_competitions"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type GetRebateActivitiesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取邀请反佣页活动列表
func NewGetRebateActivitiesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRebateActivitiesLogic {
	return &GetRebateActivitiesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetRebateActivitiesLogic) GetRebateActivities(req *types.GetRebateActivitiesReq) (resp *types.GetRebateActivitiesResp, err error) {
	resp = &types.GetRebateActivitiesResp{}
	list, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).QueryRebateActivitiesByParam(l.ctx, req)
	if err != nil {
		l.Logger.Errorf("QueryRebateActivitiesByParam is fail ，err is:%v", err)
		return nil, err
	}
	var ids []int64
	for _, v := range list {
		ids = append(ids, v.ActivityId)
	}

	comps, err := dao.NewCompetitionsMysqlDBUtil(l.svcCtx.DBCompetitions).QueryCompetitionByParam(l.ctx, ids, req.CompetitionName, req.Status)
	if err != nil {
		l.Logger.Errorf("QueryCompetitionByParam is fail ，err is:%v", err)
		return nil, err
	}
	compsMap := make(map[int64]*cc_competitions.CcCompetitions)

	for _, comp := range comps {
		compsMap[comp.Id] = comp
	}

	resp.List = []*types.RebateActivity{}
	current := time.Now().Unix()
	for _, v := range list {
		currentComp, exists := compsMap[v.ActivityId]
		if !exists {
			continue
		}

		startAt := currentComp.StartAt
		endAt := currentComp.EndAt
		status := consts.InviteRebateUnstart
		if startAt <= current && endAt >= current {
			status = consts.InviteRebateProcessing
		} else if endAt < current {
			status = consts.InviteRebateEnd
		}

		resp.List = append(resp.List, &types.RebateActivity{
			Id:              v.Id,
			ActivityId:      v.ActivityId,
			CompetitionName: currentComp.CompetitionName,
			Url:             currentComp.Url,
			Img:             consts.JointActivityImgUrl(currentComp.Img),
			StartAt:         startAt,
			EndAt:           endAt,
			Status:          status,
			ConfigStatus:    int(v.ConfigStatus),
			Weight:          int(v.Weight),
		})
	}

	sort.Slice(resp.List, func(i, j int) bool {
		// 优先按启用禁用状态排序，启用在前
		if resp.List[i].ConfigStatus != resp.List[j].ConfigStatus {
			return resp.List[i].ConfigStatus < resp.List[j].ConfigStatus
		}
		// 如果状态相同，则按 weight 排序
		return resp.List[i].Weight > resp.List[j].Weight
	})

	return resp, nil
}

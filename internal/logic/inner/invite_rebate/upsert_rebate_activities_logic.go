package invite_rebate

import (
	"context"
	"errors"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/models/invite_rebate_activity"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type UpsertRebateActivitiesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 新增邀请反佣页活动
func NewUpsertRebateActivitiesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpsertRebateActivitiesLogic {
	return &UpsertRebateActivitiesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpsertRebateActivitiesLogic) UpsertRebateActivities(req *types.UpsertRebateActivitiesReq) (resp *types.UpsertRebateActivitiesResp, err error) {
	resp = &types.UpsertRebateActivitiesResp{}
	act := &invite_rebate_activity.InviteRebateActivity{
		Weight: int64(req.Weight),
	}
	if req.Id == 0 {
		// 判断是否上架状态
		comp, err := dao.NewCompetitionsMysqlDBUtil(l.svcCtx.DBCompetitions).GetCompetitionById(l.ctx, req.ActivityId)
		if err != nil {
			return nil, errors.New("无效活动id，该活动id不存在")
		}
		if comp.Status != 1 {
			return nil, errors.New("无效活动id，该活动未上架")
		}

		// 判断权重占用
		find, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindRebateActivitiesByWeight(l.ctx, req.Weight)
		if err != nil {
			return nil, errors.New(err.Error())
		}
		if find {
			return nil, errors.New("该权重已被占用")
		}

		// 判断活动id占用
		find, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindRebateActivitiesByActivityId(l.ctx, req.ActivityId)
		if err != nil {
			return nil, errors.New(err.Error())
		}
		if find {
			return nil, errors.New("该活动id已创建")
		}

		// 创建
		act.ActivityId = req.ActivityId
		act.ConfigStatus = consts.InviteRebateOffine
		_, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).InsertRebateActivities(l.ctx, act)
		if err != nil {
			return nil, errors.New(err.Error())
		}
	} else {
		oldAct, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindRebateActivitiesById(l.ctx, req.Id)
		if err != nil {
			return nil, errors.New(err.Error())
		}
		if oldAct.ConfigStatus == consts.InviteRebateOnline {
			return nil, errors.New("禁用时配置才可以编辑")
		}

		// 更新
		act.Id = req.Id
		_, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).UpdateRebateActivities(l.ctx, act)
		if err != nil {
			return nil, errors.New(err.Error())
		}
	}

	return resp, nil
}

package invite_rebate

import (
	"context"
	"errors"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/invite_rebate_activity"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type OnlineRebateActivitiesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 启用邀请反佣页活动
func NewOnlineRebateActivitiesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *OnlineRebateActivitiesLogic {
	return &OnlineRebateActivitiesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *OnlineRebateActivitiesLogic) OnlineRebateActivities(req *types.OnlineRebateActivitiesReq) (resp *types.OnlineRebateActivitiesResp, err error) {
	resp = &types.OnlineRebateActivitiesResp{}

	act, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindRebateActivitiesById(l.ctx, req.Id)
	if err != nil {
		return nil, errors.New(err.Error())
	}

	// 判断权重占用
	find, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindRebateActivitiesByWeight(l.ctx, int(act.Weight))
	if err != nil {
		return nil, errors.New(err.Error())
	}
	if find {
		return nil, errors.New("该权重已被占用")
	}
	data := &invite_rebate_activity.InviteRebateActivity{
		Id:           req.Id,
		ConfigStatus: consts.InviteRebateOnline,
	}
	_, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).UpdateRebateActivities(l.ctx, data)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

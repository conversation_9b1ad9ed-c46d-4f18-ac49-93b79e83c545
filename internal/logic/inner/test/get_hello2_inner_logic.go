package test

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type GetHello2InnerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 测试接口
func NewGetHello2InnerLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetHello2InnerLogic {
	return &GetHello2InnerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetHello2InnerLogic) GetHello2Inner() (resp *types.TestApiInnerResponse, err error) {
	// todo: add your logic here and delete this line
	resp = &types.TestApiInnerResponse{
		Message: "内部接口-GetHello2",
	}
	return
}

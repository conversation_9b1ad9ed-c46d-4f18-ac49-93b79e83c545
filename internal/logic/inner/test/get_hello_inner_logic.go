package test

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type GetHelloInnerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 测试接口
func NewGetHelloInnerLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetHelloInnerLogic {
	return &GetHelloInnerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetHelloInnerLogic) GetHelloInner() (resp *types.TestApiInnerResponse, err error) {
	// todo: add your logic here and delete this line
	resp = &types.TestApiInnerResponse{
		Message: "内部接口-GetHello",
	}
	return
}

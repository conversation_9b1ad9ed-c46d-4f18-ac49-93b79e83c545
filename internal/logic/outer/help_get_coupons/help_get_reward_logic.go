package help_get_coupons

import (
	"context"
	"encoding/json"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/mqs/kafka"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type HelpGetRewardLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取助力领券奖励额
func NewHelpGetRewardLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HelpGetRewardLogic {
	return &HelpGetRewardLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *HelpGetRewardLogic) HelpGetReward() (resp *types.HelpGetRewardResp, err error) {
	resp = &types.HelpGetRewardResp{}
	defer func() {
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("HelpGetReward resp: ", string(respJson), " err: ", err)
	}()

	resp.InviterRewardValue = int(service.GetTaskRewardDetail(l.svcCtx, l.ctx, consts.GetHelpInviterTaskId()).RewardNum)
	tradeRewardValue := int(service.GetTaskRewardDetail(l.svcCtx, l.ctx, consts.GetHelpInviteeTradeTaskId()).RewardNum)
	resp.InviteeRewardValue = tradeRewardValue

	_, spot, _, couponId, source := service.GetTaskContionMinNum(l.svcCtx, l.ctx, consts.GetHelpInviteeTradeTaskId())
	rewardDetail := service.GetTaskRewardDetail(l.svcCtx, l.ctx, consts.GetHelpInviteeTradeTaskId())

	l.Logger.Infof("%d %d %s %d %s", spot, couponId, source, rewardDetail.RewardCouponId, rewardDetail.RewardSource)
	// test
	msgStr := "{\"uid\":2124436956,\"timest\":\"2025-06-30 19:37:22\",\"ref_uid\":2124437819,\"utm_cmp\":\"PEYEQdSb\"}"
	// msgStr := "{\"business_type\":19,\"call_type\":\"done\",\"list\":[{\"business_type\":19,\"business_id\":\"798229278218035201\",\"user_id\":2124437941,\"task_id\":1753,\"only_id\":\"0197bffc-9681-7061-b9d3-71780fb2e26a\",\"status\":3,\"done_time\":1751278118,\"conditions\":[{\"prize_type\":2,\"prize_num\":0,\"status\":3,\"prize_ext\":{\"coupon_source\":\"jumiqfqziw\",\"coupon_id\":1604,\"prize_pool_id\":0,\"draw_num\":\"\",\"prize_id\":0},\"send_prize_total_full_n\":0}],\"received_type\":4,\"rule_info\":[{\"id\":1383858,\"mark\":\"recharge\",\"status\":3,\"is_multiple_condition\":1,\"conditions\":[{\"only_id\":\"0197bffc-9684-75e6-b64d-b093e79ef3a1\",\"prize_type\":2,\"prize_num\":0,\"status\":3,\"prize_ext\":{\"coupon_source\":\"jumiqfqziw\",\"coupon_id\":1604,\"prize_pool_id\":0,\"draw_num\":\"\",\"prize_id\":0},\"send_prize_total_full_n\":0}]}],\"is_multiple_rule\":2,\"invited_user_ids\":[]}],\"__gateio_inner_msg_id__\":798252046422724609,\"__gateio_msg_create_time__\":1751278118}"
	// HelpTaskRegCallBack(l.ctx, l.svcCtx, msgStr)
	// kafka.HelpInviteeTaskCallBack(l.ctx, l.svcCtx, msgStr)

	// 注册
	// msgStr := "{\"business_type\":19,\"call_type\":\"invite_join\",\"list\":{\"business_type\":19,\"business_id\":\"17\",\"user_id\":2124437819,\"task_id\":1755,\"be_invited_user_id\":2124438037,\"start_time\":1751281999,\"end_time\":1751704559,\"invite_info\":[{\"business_id\":798268322633355265,\"task_id\":\"1753\"},{\"business_id\":798268322746601472,\"task_id\":\"1754\"}]}}"
	// msgStr := "{\"business_type\":19,\"call_type\":\"done\",\"list\":[{\"business_type\":19,\"business_id\":\"17\",\"user_id\":2124437819,\"task_id\":1755,\"only_id\":\"0197bffc-9681-7061-b9d3-71780fb2e26a\",\"status\":3,\"done_time\":1751278118,\"conditions\":[{\"prize_type\":2,\"prize_num\":0,\"status\":3,\"prize_ext\":{\"coupon_source\":\"jumiqfqziw\",\"coupon_id\":1604,\"prize_pool_id\":0,\"draw_num\":\"\",\"prize_id\":0},\"send_prize_total_full_n\":0}],\"received_type\":4,\"rule_info\":[{\"id\":1383858,\"mark\":\"recharge\",\"status\":3,\"is_multiple_condition\":1,\"conditions\":[{\"only_id\":\"0197bffc-9684-75e6-b64d-b093e79ef3a1\",\"prize_type\":2,\"prize_num\":0,\"status\":3,\"prize_ext\":{\"coupon_source\":\"jumiqfqziw\",\"coupon_id\":1604,\"prize_pool_id\":0,\"draw_num\":\"\",\"prize_id\":0},\"send_prize_total_full_n\":0}]}],\"is_multiple_rule\":2,\"invited_user_ids\":[2124436956,2124438037]}],\"__gateio_inner_msg_id__\":798252046422724609,\"__gateio_msg_create_time__\":1751278118}"
	kafka.HelpUserRegCallBack(l.ctx, l.svcCtx, msgStr)

	return resp, nil
}

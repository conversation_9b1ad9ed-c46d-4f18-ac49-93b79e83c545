package help_get_coupons

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"strconv"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"gorm.io/gorm"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/logic"
	"gateio_service_marketing_activity/internal/models/help_invite_task"
	"gateio_service_marketing_activity/internal/mqs/kafka"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"

	"gateio_service_marketing_activity/internal/models/help_user_join"

	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
)

type HelpSignupLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 用户报名
func NewHelpSignupLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HelpSignupLogic {
	return &HelpSignupLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *HelpSignupLogic) HelpSignup(req *types.HelpSignupReq, r *http.Request) (resp *types.HelpSignupResp, err error) {
	resp = &types.HelpSignupResp{}

	userInfo := requestools.GetUserInfo(r)
	if userInfo == nil || userInfo.UID <= 0 {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	identity, err := logic.NewGetUserIdentityLogic(r.Context(), l.svcCtx).GetUserIdentity(r, userInfo)
	if err != nil {
		l.Logger.Errorf("[HelpSignup] GetUserIdentity fail,err: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	uid := int64(userInfo.UID)
	l.Logger.Infof("[HelpSignup] GetUserIdentity userinfo: uid:%d, IsMarketplace:%d, IsEnterprise:%d, IsSub:%d",
		uid, identity.IsMarketplace, identity.IsEnterprise, identity.IsSub)
	// 市商用户|企业用户
	if identity.IsMarketplace == 1 || userInfo.Verified == consts.KycLvCompany {
		return resp, consts.GetErrorMsg(r, consts.ErrHelpGetCouponNotAllow)
	}
	// 子账户
	if identity.IsSub == 1 {
		return resp, consts.GetErrorMsg(r, consts.ErrHelpGetCouponSubUser)
	}

	defer func() {
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("[HelpSignup] resp: ", string(respJson), " err: ", err, "  uid is:", uid)
	}()

	dataMap := make(map[string]interface{})
	dataMap["user_id"] = uid
	dataMap["ip"] = requestools.GetClientIP(r)
	dataMap["const_id"] = req.ConstID
	dataMap["action_code"] = "help_signup"
	dataMap["inviter_uid"] = "user_id"
	dataMap["invitee_uid"] = ""
	isRisk, err := service.GetUserRiskData(l.svcCtx, l.ctx, int(uid), req.ConstID, consts.RiskEventCodeInviteRisk, dataMap, r)
	paramByte, _ := json.Marshal(dataMap)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("[HelpSignup] CheckRisk is err,params is:%d, %s ，err is：%v", uid, string(paramByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrApiError)
	}
	// 命中风控
	if isRisk {
		logx.Infof("[HelpSignup] user is risk,params is:%d, %s", uid, req.ConstID)
		return nil, consts.GetErrorMsg(r, consts.ErrHelpGetCouponRisk)
	}

	l.Logger.Infof("[HelpSignup] CheckRisk success: uid=%d, isRisk=%t, param=%s", uid, isRisk, string(paramByte))

	now := time.Now()
	// 一个月内只能报名5次
	startTime := now.AddDate(0, -1, 0)
	tasks, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindInviterTaskByUidAndCreateTime(l.ctx, uid, startTime)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		l.Logger.Errorf("查询助力领券任务失败: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	if len(tasks) >= 5 {
		nextTime := tasks[0].CreatedAt.AddDate(0, 1, 0).Unix()
		resp.NextTime = nextTime
		return resp, nil
	}

	if len(tasks) > 0 {
		task := tasks[len(tasks)-1]
		if task.Status == consts.HelpStatusSuccess {
			return nil, consts.GetErrorMsg(r, consts.ErrHelpGetCouponUnGot)
		}
		if task.Status == consts.HelpStatusInProgress {
			if time.Unix(task.EndTime, 0).After(now) {
				return nil, consts.GetErrorMsg(r, consts.ErrTaskNotFinished)
			}
		}
	}

	// 写入 help_invite_task
	rewardDetail := service.GetTaskRewardDetail(l.svcCtx, l.ctx, consts.GetHelpInviterTaskId())
	endTime := now.Add(5 * 24 * time.Hour)
	l.Logger.Infof("[HelpSignup] 任务开始时间: %s", now.Format("2006-01-02 15:04:05"))
	l.Logger.Infof("[HelpSignup] 任务结束时间: %s", endTime.Format("2006-01-02 15:04:05"))

	task := &help_invite_task.HelpInviteTask{
		TaskId:         consts.GetHelpInviterTaskId(),
		Uid:            uid,
		TaskType:       consts.TaskTypeCycl,
		IsInviterTask:  consts.IsInviterTask,
		ReportStatus:   consts.TaskStatusInitial,
		Status:         consts.HelpStatusInProgress,
		Valid:          0,
		PrizeId:        rewardDetail.RewardCouponId,
		PrizeExtraInfo: sql.NullString{String: rewardDetail.RewardSource, Valid: rewardDetail.RewardSource != ""},
		PrizeType:      consts.PrizeTypeCoupon,
		BusinessType:   int64(consts.BusinessTypeHelp),
		EndTime:        endTime.Unix(), // 当前时间加5天
	}
	helpInviteTaskId, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).InsertHelpInviteTask(l.ctx, task)
	if err != nil {
		l.Logger.Errorf("报名写入 help_invite_task 失败: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}

	// 上报任务系统
	err = kafka.SubmitHelpTaskRecord(l.ctx, l.svcCtx, uid, helpInviteTaskId, consts.GetHelpInviterTaskId())
	if err != nil {
		logx.WithContext(l.ctx).Errorf("[Help-Signup]submitTaskRecord err: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}
	// 把用户任务状态更新成已上报
	update := &help_invite_task.HelpInviteTask{
		Id:             helpInviteTaskId,
		TaskBusinessId: strconv.FormatInt(helpInviteTaskId, 10),
		ReportStatus:   consts.TaskStatusReported,
	}

	_, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).UpdateHelpInviteTask(l.ctx, update)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("[Help-Signup]UpdateHelpInviteTask err: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}

	// 先查询是否已存在记录（根据uid查询）
	existingRecord, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindHelpUserJoinByUid(l.ctx, uid)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		l.Logger.Errorf("查询 help_user_join 失败: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	if existingRecord != nil {
		updateData := &help_user_join.HelpUserJoin{
			Id:        existingRecord.Id,
			Uid:       uid,
			UpdatedAt: now,
		}
		_, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).UpdateHelpUserJoin(l.ctx, updateData)
		if err != nil {
			l.Logger.Errorf("更新 help_user_join 失败: %v", err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		l.Logger.Infof("更新 help_user_join 成功, uid: %d", uid)
	} else {
		// 如果记录不存在，插入新记录
		newRecord := &help_user_join.HelpUserJoin{
			Uid:       uid,
			CreatedAt: now,
			UpdatedAt: now,
		}
		_, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).InsertHelpUserJoin(l.ctx, newRecord)
		if err != nil {
			l.Logger.Errorf("插入 help_user_join 失败: %v", err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		l.Logger.Infof("插入 help_user_join 成功, uid: %d", uid)
	}

	return resp, nil
}

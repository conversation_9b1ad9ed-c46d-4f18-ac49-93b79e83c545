package help_get_coupons

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"
	"time"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/help_invite_task"
	"gateio_service_marketing_activity/internal/models/help_user_prize"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/service_client"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
)

type HelpGetCouponLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 邀请人领奖
func NewHelpGetCouponLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HelpGetCouponLogic {
	return &HelpGetCouponLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *HelpGetCouponLogic) HelpGetCoupon(r *http.Request, req *types.HelpCouponReq) (resp *types.HelpCouponResp, err error) {
	userInfo := requestools.GetUserInfo(r)
	if userInfo == nil || userInfo.UID <= 0 {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	uid := int64(userInfo.UID)

	// 查找该用户最近一条任务
	task, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindLatestTaskByUid(l.ctx, uid)
	if err != nil {
		l.Logger.Errorf("HelpGetCoupon FindLatestTaskByUid failed, uid: %d, err: %v", uid, err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	if task == nil || task.PrizeId == 0 || task.Status != consts.HelpStatusSuccess || task.ProcessNum < 2 || task.Valid != 1 {
		l.Logger.Warnf("HelpGetCoupon FindLatestTaskByUid failed, uid: %d, err: %v", uid, err)
		return nil, consts.GetErrorMsg(r, consts.ErrHelpGetCouponInvalid)
	}
	l.Logger.Infof("[HelpGetCoupon] uid: %d, PrizeId: %d, Status: %d, ProcessNum: %d, Valid: %d",
		uid, task.PrizeId, task.Status, task.ProcessNum, task.Valid)

	extraData := task.ExtraData
	var inviteeList []int64
	if extraData.Valid && extraData.String != "" {
		err := json.Unmarshal([]byte(extraData.String), &inviteeList)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("[HelpGetCoupon] Unmarshal extraData fail ,uid is:%d，err is：%v", uid, err)
			return nil, consts.GetErrorMsg(r, consts.ErrTaskNotFinished)
		}
	} else {
		logx.WithContext(l.ctx).Errorf("[HelpGetCoupon] extraData is null,params is:%d, %s ，err is：%v", uid, req.ConstID, err)
		return nil, consts.GetErrorMsg(r, consts.ErrTaskNotFinished)
	}

	isRisk := false
	if !isRisk {
		dataMap := make(map[string]interface{})
		dataMap["user_id"] = uid
		dataMap["ip"] = requestools.GetClientIP(r)
		dataMap["const_id"] = req.ConstID
		dataMap["action_code"] = "help_get_coupon"
		dataMap["inviter_uid"] = "user_id"

		inviteeStrList := make([]string, len(inviteeList))
		for i, invitee := range inviteeList {
			inviteeStrList[i] = strconv.FormatInt(invitee, 10)
		}
		dataMap["invitee_uid"] = strings.Join(inviteeStrList, ",")

		isRisk, err = service.GetUserRiskData(l.svcCtx, l.ctx, int(uid), req.ConstID, consts.RiskEventCodeInviteRisk, dataMap, r)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("[HelpGetCoupon] GetUserRiskData is err,params is:%d, %s ，err is：%v", uid, req.ConstID, err)
			return nil, consts.GetErrorMsg(r, consts.ErrApiError)
		}
	}

	// 命中风控
	if isRisk {
		task := &help_invite_task.HelpInviteTask{
			Id:     task.Id,
			Status: consts.HelpStatusRisk,
		}
		_, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).UpdateHelpInviteTask(l.ctx, task)
		if err != nil {
			l.Logger.Errorf("命中风控写入 help_invite_task 失败: %v", err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		return nil, consts.GetErrorMsg(r, consts.ErrHelpGetCouponRisk)
	}

	couponInfo, _ := service.GetCouponInfo(l.svcCtx, l.ctx, task.PrizeId, task.PrizeExtraInfo.String)
	// 插入领奖记录
	newPrize := &help_user_prize.HelpUserPrize{
		Uid:             uid,
		TaskId:          task.Id,
		IsInviterReward: consts.IsInviterTask,
		PrizeId:         task.PrizeId,
		PrizeType:       task.PrizeType,
		PrizeValue:      couponInfo.Amount,
		PrizeStatus:     consts.HelpUserPrizeStatusGetting,
		BusinessType:    consts.BusinessTypeHelp,
	}
	var userPrizeId int64
	userPrizeId, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).InsertHelpUserPrize(l.ctx, newPrize)
	if err != nil {
		l.Logger.Errorf("HelpGetCoupon InsertHelpUserPrize failed, prize: %+v, err: %v", newPrize, err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}

	// 发放卡券
	var status int64
	taskCenterCall := service_client.NewTaskCenterCall(l.ctx)
	_, err = taskCenterCall.ManualSendPrize(uid, task.TaskId, consts.HelpTaskBusinessType, task.TaskBusinessId)
	if err != nil {
		status = consts.HelpUserPrizeStatusFailed
		l.Logger.Errorf("[HelpGetCoupon]助力领券邀请人调用任务接口发卡券失败. taskId=%d, userId=%d, taskCenterId=%d, businessType=%d, taskBusinessId=%s, err=%v",
			task.Id, uid, task.TaskId, consts.HelpTaskBusinessType, task.TaskBusinessId, err)
	} else {
		status = consts.HelpUserPrizeStatusSuccess
	}

	// 修改状态
	task.Status = consts.HelpStatusClaimed
	_, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).UpdateHelpInviteTask(l.ctx, task)
	if err != nil {
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}

	helpUserPrize := &help_user_prize.HelpUserPrize{
		Id:              userPrizeId,
		PrizeStatus:     status,
		PrizeIssuedTime: time.Now().Unix(),
	}
	_, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).UpdateHelpUserPrize(l.ctx, helpUserPrize)
	if err != nil {
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}

	// 发站内信
	_, err = service.SendHeraldMessages(l.svcCtx, l.ctx, consts.HeraldRewardRelease, []string{"mobile"}, []int64{uid}, "", []string{})
	if err != nil {
		logx.WithContext(l.ctx).Errorf("%s SendHeraldMessages HeraldRewardRelease failed,  taskId:%d, uid:%d, err:%v", task.Id, uid, err)
	} else {
		logx.WithContext(l.ctx).Infof("%s HeraldRewardRelease notification sent, taskId:%d, uid:%d", task.Id, uid)
	}

	amount, _ := strconv.Atoi(couponInfo.Amount)
	return &types.HelpCouponResp{
		InviterRewardValue: amount,
	}, nil
}

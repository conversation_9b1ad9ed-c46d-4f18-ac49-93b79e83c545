package help_get_coupons

import (
	"context"
	"errors"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"gorm.io/gorm"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type HelpInviteeRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 查询被邀请人记录
func NewHelpInviteeRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HelpInviteeRecordLogic {
	return &HelpInviteeRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *HelpInviteeRecordLogic) HelpInviteeRecord(r *http.Request, req *types.HelpInviteeRecordReq) (resp *types.HelpInviteeRecordResp, err error) {
	userInfo := requestools.GetUserInfo(r)
	if userInfo == nil || userInfo.UID <= 0 {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	uid := int64(userInfo.UID)

	if req.PerPage <= 0 {
		req.PerPage = 10
	}
	if req.Page <= 0 {
		req.Page = 1
	}

	list, total, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindInviteeRecordsByRefUid(l.ctx, uid, req.Page, req.PerPage)
	if err != nil {
		l.Logger.Errorf("FindInviteeRecords failed: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}

	inviteeList := make([]*types.InviteeRecord, 0, len(list))
	for _, v := range list {
		inviteeTask, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindInviteeTaskByUid(l.ctx, v.Uid)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			l.Logger.Errorf("FindInviteeTaskByUid failed: %v", err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}

		valid := 0
		finishTime := int64(0)
		if inviteeTask != nil {
			valid = int(inviteeTask.Valid)
			finishTime = inviteeTask.FinishTime
		}
		record := &types.InviteeRecord{
			Uid:          v.Uid,
			RegisterTime: v.RegisterTime,
			Valid:        valid,
			FinishTime:   finishTime,
		}
		inviteeList = append(inviteeList, record)
	}

	resp = &types.HelpInviteeRecordResp{
		InviteeList: inviteeList,
		Total:       int(total),
	}
	return resp, nil
}

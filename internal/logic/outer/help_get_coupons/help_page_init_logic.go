package help_get_coupons

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"sort"
	"time"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/logic"
	"gateio_service_marketing_activity/internal/models/help_invite_task"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"gorm.io/gorm"
)

type HelpPageInitLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 页面初始化接口
func NewHelpPageInitLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HelpPageInitLogic {
	return &HelpPageInitLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *HelpPageInitLogic) HelpPageInit(r *http.Request) (resp *types.HelpPageInitResp, err error) {
	resp = &types.HelpPageInitResp{}
	accumulated, _, _, _, _ := service.GetTaskContionMinNum(l.svcCtx, l.ctx, consts.GetHelpInviteeDepositTaskId())
	_, spot, _, _, _ := service.GetTaskContionMinNum(l.svcCtx, l.ctx, consts.GetHelpInviteeTradeTaskId())
	defalutHelpUsers := []*types.HelpUser{
		getDeafultUser(accumulated, spot),
		getDeafultUser(accumulated, spot),
	}
	resp.HelpUsers = defalutHelpUsers

	userInfo := requestools.GetUserInfo(r)
	if userInfo == nil || userInfo.UID <= 0 {
		resp.Login = -1
		return resp, nil
	}

	identity, err := logic.NewGetUserIdentityLogic(r.Context(), l.svcCtx).GetUserIdentity(r, userInfo)
	if err != nil {
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	// 市商用户|机构用户|企业用户
	if identity.IsMarketplace == 1 || identity.IsEnterprise == 1 {
		return resp, consts.GetErrorMsg(r, consts.ErrHelpGetCouponNotAllow)
	}
	// 子账户
	if identity.IsSub == 1 {
		return resp, consts.GetErrorMsg(r, consts.ErrHelpGetCouponSubUser)
	}

	uid := int64(userInfo.UID)
	defer func() {
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("HelpPageInit resp: ", string(respJson), " err: ", err, "  uid is:", uid)
	}()

	// 查询邀请人信息
	inviterTask, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindLatestTaskByUid(l.ctx, uid)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		l.Logger.Errorf("查询助力领券任务失败: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}

	var helpUsers []*types.HelpUser
	var tasks []*help_invite_task.HelpInviteTask
	// 如果找不到邀请任务，表示用户未参与
	if errors.Is(err, gorm.ErrRecordNotFound) || inviterTask == nil {
		resp.Status = consts.HelpStatusNotJoined
	} else {
		resp.Status = int(inviterTask.Status)
		// 计算剩余时间
		now := time.Now()
		if time.Unix(inviterTask.EndTime, 0).After(now) {
			resp.TimeLeft = int64(time.Unix(inviterTask.EndTime, 0).Sub(now).Seconds())
		} else {
			resp.TimeLeft = 0
			if inviterTask.Status == consts.HelpStatusInProgress {
				resp.Status = consts.HelpStatusFailed
			}
		}

		// 接入缓存
		redisKey := consts.HelpPageInitUsersKey(uid)
		redisVal, _ := l.svcCtx.Redis.Get(redisKey)
		if redisVal != "" {
			_ = json.Unmarshal([]byte(redisVal), &helpUsers)
		} else {
			// 查询助力用户列表
			tasks, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindTaskByRefTaskId(l.ctx, inviterTask.Id)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				l.Logger.Errorf("查询助力领券任务失败: %v", err)
				return nil, consts.GetErrorMsg(r, consts.ErrDbError)
			}

			if !errors.Is(err, gorm.ErrRecordNotFound) && tasks != nil {
				for _, task := range tasks {
					relation, _ := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetUserInfo(l.ctx, task.Uid)

					var steps []*types.Step
					if !task.ExtraData.Valid || task.ExtraData.String == "" {
						steps = nil
					} else {
						err := json.Unmarshal([]byte(task.ExtraData.String), &steps)
						if err != nil {
							return nil, consts.GetErrorMsg(r, consts.ErrUnmarshalFailed)
						}
					}
					status := task.Status
					sort := 0
					for _, step := range steps {
						if !step.Completed {
							status = consts.InviteeStatusProcess
						} else {
							sort += 1
						}
					}
					if status == consts.InviteeStatusProcess && time.Unix(task.EndTime, 0).Unix() <= time.Now().Unix() {
						status = consts.InviteeStatusEnd
					}

					helpUsers = append(helpUsers, &types.HelpUser{
						UserId: task.Uid,
						Avatar: relation.Avatar,
						Name:   relation.Name,
						Steps:  steps,
						Sort:   sort,
						Status: int(status),
					})
				}
				sort.Slice(helpUsers, func(i, j int) bool {
					return helpUsers[i].Sort > helpUsers[j].Sort
				})
				if len(tasks) > 2 {
					helpUsers = helpUsers[:2]
				}

				// 缓存十分钟
				byteVal, err := json.Marshal(helpUsers)
				if err == nil {
					_ = l.svcCtx.Redis.Setex(consts.HelpPageInitUsersKey(uid), string(byteVal), 60*10)
				}
			}
		}
	}

	if len(helpUsers) < 2 {
		for i := 0; i < 2-len(helpUsers); i++ {
			helpUsers = append(helpUsers, getDeafultUser(accumulated, spot))
		}
	} else if len(helpUsers) > 2 {
		helpUsers = helpUsers[:2]
	}

	resp.HelpUsers = helpUsers
	return resp, nil
}

func getDeafultUser(accumulated int, spot int) *types.HelpUser {
	steps := []*types.Step{
		{
			Label:              consts.TaskTypeSignup,
			Completed:          false,
			TaskConditionValue: 0,
		},
		{
			Label:              consts.TaskTypeDeposit,
			Completed:          false,
			TaskConditionValue: accumulated,
		},
		{
			Label:              consts.TaskTypeTrade,
			Completed:          false,
			TaskConditionValue: spot,
		},
	}
	return &types.HelpUser{
		UserId: 0,
		Avatar: "",
		Name:   "",
		Steps:  steps,
		Status: consts.InviteeStatusInit,
	}
}

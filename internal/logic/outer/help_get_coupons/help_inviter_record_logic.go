package help_get_coupons

import (
	"context"
	"net/http"
	"strconv"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"gorm.io/gorm"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type HelpInviterRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 查询邀请人记录
func NewHelpInviterRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HelpInviterRecordLogic {
	return &HelpInviterRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *HelpInviterRecordLogic) HelpInviterRecord(r *http.Request, req *types.HelpInviterRecordReq) (resp *types.HelpInviterRecordResp, err error) {
	userInfo := requestools.GetUserInfo(r)
	if userInfo == nil || userInfo.UID <= 0 {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	uid := int64(userInfo.UID)

	if req.PerPage <= 0 {
		req.PerPage = 10
	}
	if req.Page <= 0 {
		req.Page = 1
	}

	tasks, total, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindInviterTasksByPage(l.ctx, uid, req.ProgressStatus, req.Page, req.PerPage)
	if err != nil {
		l.Logger.Errorf("FindInviterTasks failed: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}

	inviterList := make([]*types.InviterRecord, 0, len(tasks))
	inviterRewardValue := int(service.GetTaskRewardDetail(l.svcCtx, l.ctx, consts.GetHelpInviterTaskId()).RewardNum)
	for _, t := range tasks {
		progressStatus := consts.HelpProcessing
		if t.Status != consts.HelpStatusInProgress {
			progressStatus = consts.HelpProcessFinish
		}

		prize, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).FindUserPrizeByTaskId(l.ctx, t.Id, t.Uid)
		if err != nil && err != gorm.ErrRecordNotFound {
			continue
		}
		receiveStatus := consts.HelpUserPrizeStatusInit
		if prize != nil {
			receiveStatus = int(prize.PrizeStatus)
			inviterRewardValue, _ = strconv.Atoi(prize.PrizeValue)
		}
		record := &types.InviterRecord{
			StartTime:          t.CreatedAt.Unix(),
			ProgressStatus:     progressStatus,
			ProgressNumber:     int(t.ProcessNum),
			InviterRewardValue: inviterRewardValue,
			ReceiveStatus:      receiveStatus,
		}
		inviterList = append(inviterList, record)
	}

	resp = &types.HelpInviterRecordResp{
		InviterList: inviterList,
		Total:       int(total),
	}
	return resp, nil
}

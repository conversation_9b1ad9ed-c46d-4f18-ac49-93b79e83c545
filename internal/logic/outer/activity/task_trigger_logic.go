package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type TaskTriggerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTaskTriggerLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TaskTriggerLogic {
	return &TaskTriggerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TaskTriggerLogic) TaskTrigger(req *types.TaskTriggerReq, r *http.Request) (resp *types.TaskTriggerResp, err error) {
	defer func() {
		reqJson, _ := json.Marshal(req)
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("TaskTrigger Req: ", string(reqJson), " resp: ", string(respJson), " err: ", err)
	}()

	// 验证用户
	if req.UserId > 0 {
		userInfo := requestools.GetUserInfo(r)
		if userInfo == nil || int64(userInfo.UID) != req.UserId {
			return nil, errors.New(consts.ErrUserLogin, "请先登录")
		}
	}

	// 查询子任务数据
	subTaskDataModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)
	subTask, err := subTaskDataModel.GetSubTaskByID(l.ctx, req.SubTaskId)
	if err != nil {
		return nil, fmt.Errorf("GetSubTaskByID error: %v", err)
	}
	if subTask == nil {
		return nil, errors.New(consts.ErrInvalidParam, "sub_task_id参数错误")
	}
	if subTask.UserId != req.UserId {
		return nil, errors.New(consts.ErrInvalidParam, "用户ID与任务用户不匹配")
	}
	if subTask.TaskType != nft.TaskTypeForwardXTweet {
		return nil, errors.New(consts.ErrInvalidParam, "该任务类型，无法完成")
	}

	// 修改任务状态为已完成
	err = subTaskDataModel.UpdateOne(l.ctx, req.SubTaskId, []string{
		"status",
		"updated_at",
	}, &nft.NftUserSubTask{
		Status:    nft.TaskStatusCompleted,
		UpdatedAt: time.Now(),
	})
	if err != nil {
		logx.Infof("TaskTrigger UpdateOne error: %v, businessId: %d", err, req.SubTaskId)
		return
	}

	return
}

/**
 * 请求参数验证
 */
func (l *TaskTriggerLogic) TaskTriggerValidate(req *types.TaskTriggerReq) (err error) {
	if req.SubTaskId <= 0 {
		return errors.New(consts.ErrInvalidParam, "sub_task_id参数不能为空")
	}
	if req.UserId <= 0 {
		return errors.New(consts.ErrInvalidParam, "user_id参数不能为空")
	}
	return nil
}

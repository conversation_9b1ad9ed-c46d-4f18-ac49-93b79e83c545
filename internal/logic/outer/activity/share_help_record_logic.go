package activity

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"github.com/spf13/cast"
	"gorm.io/gorm"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"
)

type ShareHelpRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewShareHelpRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ShareHelpRecordLogic {
	return &ShareHelpRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

var (
	ErrNftActivityRepeatHelp = errors.New(consts.NFT_ACTIVITY_REPEAT_HELP, "您已经助力过该用户")
)

func (l *ShareHelpRecordLogic) ShareHelpRecord(req *types.ShareHelpRecordReq, r *http.Request) (resp *types.ShareHelpRecordResp, err error) {
	defer func() {
		reqJson, _ := json.Marshal(req)
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("ShareHelpRecord Req: ", string(reqJson), " resp: ", string(respJson), " err: ", err)
	}()

	// 验证用户
	if req.Uid > 0 {
		userInfo := requestools.GetUserInfo(r)
		if userInfo == nil || int64(userInfo.UID) != req.Uid {
			return nil, errors.New(consts.ErrUserLogin, "请先登录")
		}
		//获取账户信息，用于验证是否是子账户
		userMap, _ := service.NewUserCenterCall(l.ctx).GetUserMap([]int64{req.Uid})
		if user, exists := userMap[req.Uid]; exists && user != nil && user.IsSub == 1 {
			logx.Infof("ShareHelpRecord sub user use :%d, is_sub val is:%d", req.Uid, userMap[req.Uid].IsSub)
			return nil, errors.New(consts.NFT_SUB_ACCOUNT_NO_ALLOW, "子账号不能参与活动")
		}
	}

	// 请求参数验证
	if err := l.ShareHelpRecordValidate(req, ""); err != nil {
		return nil, err
	}
	if (req.Uid > 0 && req.Uid == req.InviteUid) || (req.WalletAddress != "" && req.WalletAddress == req.InviteWalletAddress) {
		return nil, errors.New(consts.NFT_ACTIVITY_HELP_YOURSELF, "自己为自己助力")
	}
	// 查询活动数据
	activityInfo, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftActivity(l.ctx, req.ActivityId, req.Uid > 0)
	if err != nil {
		logx.Infof("ShareHelpRecord GetNftActivity err:%v", err)
		return resp, errors.New(consts.ErrDbError, "操作失败，请稍后重试")
	}
	if activityInfo.TaskEndTime.Before(time.Now()) {
		return nil, errors.New(consts.NFT_ACTIVITY_END, "该轮活动已结束")
	}

	// 查询邀请人的子任务数据
	subTaskList, err := l.GetSubTaskList(req)
	if err != nil {
		return nil, err
	}

	// 验证是否已经助力过
	isHelped, err := l.HasUserHelped(req, subTaskList)
	if err != nil {
		return nil, err
	}
	if isHelped {
		return nil, errors.New(consts.NFT_ACTIVITY_HELPD, "该助力已被完成")
	}

	// 助力操作
	for _, subTask := range subTaskList {
		// 如果已经助力过，则跳过
		if subTask.Status == nft.TaskStatusCompleted {
			continue
		}

		// 查询助力用户的头像
		helpUserId, helpUserAvatar, err := l.GetUserAvatarInfo(int64(req.Uid), req.WalletAddress)
		if err != nil {
			logx.Info("ShareHelpRecord GetHelpUserInfo Err: ", err, " 请求参数: ", req)
			return resp, errors.New(consts.ErrDbError, "操作失败，请稍后重试")
		}

		// 更新助力状态
		dataModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)
		err = dataModel.UpdateOne(l.ctx, subTask.Id, []string{
			"status",
			"updated_at",
			"help_user_id",
			"help_user_avatar",
		}, &nft.NftUserSubTask{
			Status:         nft.TaskStatusCompleted,
			UpdatedAt:      time.Now(),
			HelpUserId:     helpUserId,
			HelpUserAvatar: helpUserAvatar,
		})
		if err != nil {
			logx.Info("ShareHelpRecord UpdateOne Err: ", err, " 请求参数: ", req)
			return resp, errors.New(consts.ErrDbError, "操作失败，请稍后重试")
		}
		break
	}

	return
}

// 获取子任务列表
func (l *ShareHelpRecordLogic) GetSubTaskList(req *types.ShareHelpRecordReq) (subTaskList []*nft.NftUserSubTask, err error) {
	dataModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)
	if req.InviteUid > 0 {
		subTaskList, err = dataModel.GetTaskRecordListByUid(l.ctx, req.ActivityId, req.InviteUid, map[string]interface{}{
			"task_type": nft.TaskTypeHelp,
		})
		if err != nil {
			logx.Info("getSubTaskList GetTaskRecordListByUid Err: ", err, " 请求参数: ", req)
			return nil, errors.New(consts.ErrDbError, "操作失败，请稍后重试")
		}
	} else {
		subTaskList, err = dataModel.GetTaskRecordListByWalletAddress(l.ctx, req.ActivityId, req.InviteWalletAddress, map[string]interface{}{
			"task_type": nft.TaskTypeHelp101,
		})
		if err != nil {
			logx.Info("getSubTaskList GetTaskRecordListByWalletAddress Err: ", err, " 请求参数: ", req)
			return nil, errors.New(consts.ErrDbError, "操作失败，请稍后重试")
		}
	}
	return subTaskList, nil
}

/**
 * 获取用户的头像
 */
func (l *ShareHelpRecordLogic) GetUserAvatarInfo(userId int64, walletAddress string) (helpUserId string, helpUserAvatar string, err error) {
	// 助力人用户ID
	if userId > 0 {
		helpUserId = cast.ToString(userId)

		// 助力人头像
		userResp, err := service.NewUserCenterCall(l.ctx).GetUserInfo(userId)
		if err != nil {
			helpUserAvatar = utils.GetImageUrlByWallet(helpUserId)
			if !utils.CheckDev() {
				logx.Info("GetHelpUserInfo GetUserInfo Err: ", err, " userId: ", userId, " walletAddress: ", walletAddress, " helpUserAvatar: ", helpUserAvatar)
			}
			return helpUserId, helpUserAvatar, nil
		}
		if userResp != nil && len(userResp.UserInfo.Avatar) > 0 {
			return helpUserId, cast.ToString(userResp.UserInfo.Avatar), nil
		}
		return helpUserId, utils.GetImageUrlByWallet(helpUserId), nil
	}

	// 使用钱包地址，作为用户ID
	helpUserId = cast.ToString(walletAddress)

	// 如果头像仍为空，则随机一个
	if len(helpUserAvatar) == 0 {
		helpUserAvatar = utils.GetImageUrlByWallet(walletAddress)
	}
	return helpUserId, helpUserAvatar, nil
}

func (l *ShareHelpRecordLogic) ShareHelpRecordValidate(req *types.ShareHelpRecordReq, fromType string) (err error) {
	// 子任务ID
	if len(req.EncryptedActivityId) == 0 {
		return errors.New(consts.ErrInvalidParam, "encrypted_activity_id参数不能为空")
	}

	// 邀请人ID
	if len(req.EncryptedUid) == 0 && len(req.EncryptedWalletAddress) == 0 {
		return errors.New(consts.ErrInvalidParam, "encrypted_uid或encrypted_wallet_address必须填写一个")
	}

	// 解密参数
	activityId, err := l.GetOriginalId(req.EncryptedActivityId)
	if err != nil {
		return
	}
	inviteUid, err := l.GetOriginalId(req.EncryptedUid)
	if err != nil {
		return
	}
	inviteWalletAddress, err := l.GetOriginalId(req.EncryptedWalletAddress)
	if err != nil {
		return
	}
	req.ActivityId = cast.ToInt64(activityId)
	req.InviteUid = cast.ToInt64(inviteUid)
	req.InviteWalletAddress = inviteWalletAddress

	// 助力人
	if fromType != "detail" {
		if req.Uid == 0 && len(req.WalletAddress) == 0 {
			return errors.New(consts.ErrInvalidParam, "uid或wallet_address必须填写一个")
		}
	}
	return nil
}

/**
 * 获取原始ID参数
 */
func (l *ShareHelpRecordLogic) GetOriginalId(encryptedId string) (string, error) {
	dataId, err := utils.Decode(encryptedId)
	if err != nil {
		return "", err
	}
	if dataId == 0 {
		return "", nil
	}
	dataModel := nft.NewNftEncryptedMappingModel(l.svcCtx.DBMarketingActivities)
	data, err := dataModel.FindOne(l.ctx, int64(dataId))
	if err != nil && err != gorm.ErrRecordNotFound {
		return "", err
	}
	if data != nil && data.OriginalId != "" {
		return data.OriginalId, nil
	}
	return "", nil
}

// 新增方法：验证用户是否已经助力过
func (l *ShareHelpRecordLogic) HasUserHelped(req *types.ShareHelpRecordReq, subTaskList []*nft.NftUserSubTask) (bool, error) {
	var isHelped bool = true // 是否助力已完成
	for _, subTask := range subTaskList {
		if req.Uid > 0 && subTask.HelpUserId == cast.ToString(req.Uid) {
			return false, ErrNftActivityRepeatHelp
		}
		if len(req.WalletAddress) > 0 && subTask.HelpUserId == req.WalletAddress {
			return false, ErrNftActivityRepeatHelp
		}
		if subTask.Status == nft.TaskStatusInitial {
			isHelped = false
		}
	}
	return isHelped, nil
}

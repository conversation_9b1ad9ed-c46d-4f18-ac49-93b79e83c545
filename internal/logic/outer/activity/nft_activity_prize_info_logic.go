package activity

import (
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"context"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"github.com/spf13/cast"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type NftActivityPrizeInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 所有中奖记录查询
func NewNftActivityPrizeInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NftActivityPrizeInfoLogic {
	return &NftActivityPrizeInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NftActivityPrizeInfoLogic) NftActivityPrizeInfo(req *types.NftActivityPrizeInfoReq) (resp *types.NftActivityPrizeInfoResp, err error) {
	// 直接从数据库中查询出对应那期的所有中奖信息就好了
	lotteryModel := nft_activity.NewNftLotteryModel(l.svcCtx.DBMarketingActivities)
	prizes, err := lotteryModel.ListPrizes(l.ctx, req.ActivityID)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("NftActivityPrizeInfoLogic.ListPrizes error: %v", err)
		return nil, errors.New(consts.ErrSystemError, "please try again later")
	}
	resp = &types.NftActivityPrizeInfoResp{}
	resp.ActivityPrizeInfo = make([]types.NFTActivityPrizeInfo, 0)
	for _, prize := range prizes {
		resp.ActivityPrizeInfo = append(resp.ActivityPrizeInfo, types.NFTActivityPrizeInfo{
			PrizeDrawNumber: cast.ToString(prize.LotteryNumber),
			RaffleHash:      prize.LotteryHash,
			PrizeType:       prize.PrizeId,
		})
	}

	return
}

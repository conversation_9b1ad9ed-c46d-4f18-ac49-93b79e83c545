package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/activities"
	modelNft "gateio_service_marketing_activity/internal/models/db_nft"
	"gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/models/nft_mint_log"
	pkgnft "gateio_service_marketing_activity/internal/pkg/nft"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/utils"
	"net/http"
	"sort"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/usercenter"
	"github.com/spf13/cast"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type PageInitLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPageInitLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PageInitLogic {
	return &PageInitLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PageInitLogic) PageInit(req *types.PageInitReq, r *http.Request) (resp *types.PageInitResp, err error) {
	// 参数验证
	reqByte, _ := json.Marshal(req)
	if req.ActivityID <= 0 || (req.WalletAddress == "" && req.UserId <= 0) || (req.WalletAddress != "" && req.UserId > 0) {
		logx.Infof("PageInit req invalid parameter: %s", string(reqByte))
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
	}

	// 验证用户
	if req.UserId > 0 {
		userInfo := requestools.GetUserInfo(r)
		if userInfo == nil || int64(userInfo.UID) != req.UserId {
			return
		}
	}

	//数据库验证
	activityInfo, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftActivity(l.ctx, req.ActivityID, req.UserId > 0)
	if err != nil {
		logx.Infof("PageInit GetNftActivity  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	if activityInfo.ActivityId <= 0 {
		logx.Infof("PageInit activity is null, req is:%s", string(reqByte))
		return nil, consts.GetErrorMsg(r, consts.NFT_ACTIVITY_END)
	}
	//判断活动是否结束
	if activityInfo.EndTime.Before(time.Now()) {
		logx.Infof("PageInit activity end, activity.EndTime is:%s,%s", string(reqByte), activityInfo.EndTime.Format(consts.GoFormatTimeStr))
		return nil, consts.GetErrorMsg(r, consts.NFT_ACTIVITY_END)
	}
	//判断活动是否开始
	if activityInfo.StartTime.After(time.Now()) {
		logx.Infof("PageInit activity not start, activity.StartTime is:%s,%s", string(reqByte), activityInfo.StartTime.Format(consts.GoFormatTimeStr))
		return nil, consts.GetErrorMsg(r, consts.NFT_ACTIVITY_NOT_START)
	}
	// 如果额外活动已开始
	if activityInfo.TaskStartTime.Before(time.Now()) {
		// 获取子任务数据
		subTaskList, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftSubTaskList(l.ctx, req.ActivityID, req.UserId, req.WalletAddress)
		if err != nil {
			logx.Infof("PageInit GetNftSubTaskList  is err, req is:%s,err is:%v", string(reqByte), err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}

		// 初始化子任务
		/*
			err = l.InitTaskRecord(req, activityInfo, subTaskList)
			if err != nil {
				logx.Infof("PageInit InitTaskRecord  is err, req is:%s,err is:%v", string(reqByte), err)
				return nil, consts.GetErrorMsg(r, consts.ErrDbError)
			}*/

		// 检查任务是否已完成
		l.checkKycTask(l.ctx, l.svcCtx, req, subTaskList, activityInfo)
	}

	// 测试造数据，模拟NFT铸造完成数据
	if utils.CheckDev() {
		l.testBuildNftCastData(req)
	}

	// 检查nft铸造进度
	if err := l.CheckNftCastProgress(req); err != nil {
		logx.Infof("PageInit checkNftCastProgress  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	return
}

/**
 * 检查NFT铸造进度
 */
func (l *PageInitLogic) CheckNftCastProgress(req *types.PageInitReq) (err error) {
	// 去中心化，不进行判断
	if req.UserId <= 0 {
		return
	}
	defer func() {
		reqJson, _ := json.Marshal(req)
		logx.Info("PageInitLogic checkNftCastProgress Req: ", string(reqJson), " err: ", err)
	}()

	// 查询用户铸造记录
	mintLogModel := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities)
	mintNftLogs, err := mintLogModel.GetNftMintLogList(l.ctx, req.UserId, "", 0, 0, req.ActivityID, 0)
	if err != nil {
		logx.Info("PageInitLogic checkNftCastProgress GetNftMintLogList err: ", err)
		return err
	}
	if len(mintNftLogs) == 0 {
		return nil
	}
	midList := make([]int64, 0)
	for _, mintNftLog := range mintNftLogs {
		midList = append(midList, mintNftLog.Mid)
	}

	// 查询已经铸造成功的NFT
	nftService := pkgnft.NewNft(l.ctx, l.svcCtx)
	statusMap, err := nftService.GetMintStatusByMids(midList)
	if err != nil {
		logx.Info("PageInitLogic checkNftCastProgress GetMintStatusByMids err: ", err)
		return err
	}
	statusMapJson, _ := json.Marshal(statusMap)
	logx.Info("PageInitLogic checkNftCastProgress GetMintStatusByMids statusMap: ", string(statusMapJson), " req.UserId: ", req.UserId)

	// 更新状态
	for _, mintNftLog := range mintNftLogs {
		if mintNftLog.SceneType == nft_mint_log.SCENE_TYPE_SURPRISE {
			continue
		}
		if newStatus, ok := statusMap[mintNftLog.Mid]; ok {
			// 更新铸造状态
			setStatus := nft_mint_log.MINT_STATUS_SUCCESS
			if newStatus == modelNft.StatusMintFail || newStatus == modelNft.StatusDepoDone || newStatus == modelNft.StatusMintCancel {
				setStatus = nft_mint_log.MINT_STATUS_FAILED
			}
			err = mintLogModel.UpdateOne(l.ctx,
				mintNftLog.Id,
				[]string{
					"mint_status",
					"updated_at",
				},
				&nft_mint_log.NftMintLog{
					MintStatus: int64(setStatus),
					UpdatedAt:  time.Now(),
				})
			if err != nil {
				logx.Info("PageInitLogic checkNftCastProgress UpdateOne err: ", err, " logId: ", mintNftLog.Id)
			}

			//删除中心化铸造记录的缓存
			mintLogKey := fmt.Sprintf(consts.NftUserMintLogKey, req.UserId, req.ActivityID)
			// 删除轮询nft铸造结果的缓存
			rollMintNfrKey := fmt.Sprintf(consts.NftRollMintLogKey, req.ActivityID, req.UserId, "", mintNftLog.OperationId)
			_, _ = l.svcCtx.Redis.Del(mintLogKey, rollMintNfrKey)
		}
	}

	return nil
}

/**
 * 初始化任务
 */
func (l *PageInitLogic) InitTaskRecord(req *types.PageInitReq, activityInfo *nft_activity.NftActivity, subTaskList []*nft.NftUserSubTask) (err error) {
	defer func() {
		reqJson, _ := json.Marshal(req)
		logx.Info("PageInitLogic InitTaskRecord Req: ", string(reqJson), " err: ", err)
	}()

	// 如果已有子任务，则不再进行初始化
	if len(subTaskList) > 0 {
		return nil
	}
	taskModel := nft.NewNftTaskModel(l.svcCtx.DBMarketingActivities)
	isCentralization := 0
	if req.UserId > 0 {
		isCentralization = 1
	}
	taskList, err := taskModel.GetTaskList(l.ctx, isCentralization, nft.SceneTypeAddTime)
	if err != nil {
		return err
	}

	// 如果没有任务，则进行初始化
	_, err = l.InitTaskRecordHandle(req, activityInfo, taskList)
	if err != nil {
		return err
	}
	return nil
}

/**
 * 初始化任务
 */
func (l *PageInitLogic) InitTaskRecordHandle(req *types.PageInitReq, activityInfo *nft_activity.NftActivity, taskList []*nft.NftTask) ([]*nft.NftUserSubTask, error) {
	subTaskDataModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)
	// 插入计划
	rspList := make([]*nft.NftUserSubTask, 0)
	for _, task := range taskList {
		// 插入任务记录
		for range int(task.CompletionLimit) {
			// 生成本地任务
			taskRecord := &nft.NftUserSubTask{
				ActivityId:    activityInfo.ActivityId,
				UserId:        cast.ToInt64(req.UserId),
				WalletAddress: req.WalletAddress,
				TaskId:        task.TaskId,
				TaskType:      task.TaskType,
				MediaId:       task.MediaId,
				Status:        nft.TaskStatusInitial,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			}
			_, err := subTaskDataModel.CreateTaskRecord(l.ctx, taskRecord)
			if err != nil {
				return nil, err
			}
			// 组合列表数据
			rspList = append(rspList, taskRecord)
		}
	}
	return rspList, nil
}

/**
 * 测试造数据，模拟NFT铸造完成数据
 */
func (l *PageInitLogic) testBuildNftCastData(req *types.PageInitReq) error {
	// 查询用户铸造中的NFT
	mintLogModel := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities)
	mintNftLogs, err := mintLogModel.GetNftMintLogList(l.ctx, req.UserId, "", 0, 0, req.ActivityID, 0)
	if err != nil {
		return err
	}

	// 生成已完成的数据
	nftService := pkgnft.NewNft(l.ctx, l.svcCtx)
	for _, mintNftLog := range mintNftLogs {
		if mintNftLog.MintStatus != nft_mint_log.MINT_STATUS_IN_PROGRESS {
			continue
		}
		nftService.TestInsert(l.svcCtx, req.UserId, mintNftLog.Mid, mintNftLog.NftId)
	}
	return nil
}

/**
 * 检查kyc任务是否已完成
 */
func (l *PageInitLogic) checkKycTask(ctx context.Context, srvCtx *svc.ServiceContext, req *types.PageInitReq, userSubTaskList []*nft.NftUserSubTask, activityInfo *nft_activity.NftActivity) {
	reqJson, _ := json.Marshal(req)
	logx.Info("PageInitLogic checkKycTask running req: ", string(reqJson))

	// 去中心化，不进行判断
	if req.UserId <= 0 {
		return
	}

	// 过滤出kyc任务
	runningTaskCount := 0
	kycUserSubTaskList := make([]*nft.NftUserSubTask, 0)
	for _, task := range userSubTaskList {
		if task.TaskType == nft.TaskTypeKyc {
			kycUserSubTaskList = append(kycUserSubTaskList, task)
			if task.Status == nft.TaskStatusInitial {
				runningTaskCount++
			}
		}
	}
	if len(kycUserSubTaskList) == 0 || runningTaskCount == 0 {
		userSubTaskListJson, _ := json.Marshal(kycUserSubTaskList)
		logx.Info("PageInitLogic checkKycTask req: ", string(reqJson), " 没有kyc任务，json: ", string(userSubTaskListJson))
		return
	}

	// 查询邀请的用户
	userIdMap, err := l.GetKycUserList(ctx, req, activityInfo)
	if err != nil {
		return
	}

	// 标记已完成任务
	sort.Slice(kycUserSubTaskList, func(i, j int) bool {
		return kycUserSubTaskList[i].Status == nft.TaskStatusCompleted && kycUserSubTaskList[j].Status != nft.TaskStatusCompleted
	})
	subTaskDataModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)
	for _, subTask := range kycUserSubTaskList {
		// 如果任务已完成，表示以前消耗过满足kyc的数量
		dbHelpUserId := cast.ToInt64(subTask.HelpUserId)
		if _, exists := userIdMap[dbHelpUserId]; exists {
			delete(userIdMap, dbHelpUserId)
			continue
		}

		// 更新任务状态为已完成
		// - 消耗一个用户ID，并标记任务已经完成
		for userId, user := range userIdMap {
			helpUserAvatar := user.UserInfo.Avatar
			if len(helpUserAvatar) == 0 {
				helpUserAvatar = utils.GetImageUrlByWallet(cast.ToString(userId))
			}
			err = subTaskDataModel.UpdateOne(l.ctx, subTask.Id, []string{
				"status",
				"updated_at",
				"help_user_id",
				"help_user_avatar",
			}, &nft.NftUserSubTask{
				Status:         nft.TaskStatusCompleted,
				UpdatedAt:      time.Now(),
				HelpUserId:     cast.ToString(userId),
				HelpUserAvatar: helpUserAvatar,
			})
			if err != nil {
				logx.Info("PageInitLogic checkKycTask UpdateOne Err: ", err, " 请求参数: ", string(reqJson))
				return
			}
			delete(userIdMap, userId)
			break
		}
	}
}

/**
 * 获取KYC用户列表
 */
func (l *PageInitLogic) GetKycUserList(ctx context.Context, req *types.PageInitReq, activityInfo *nft_activity.NftActivity) (map[int64]*usercenter.UserDetail, error) {
	// 获取邀请的用户
	reqJson, _ := json.Marshal(req)
	dataModel := activities.NewActivityInviteRebateModel(l.svcCtx.DBActivities)
	uidList, err := dataModel.GetListByRefUid(ctx, req.UserId, activityInfo.TaskStartTime.Unix(), activityInfo.TaskEndTime.Unix())
	if err != nil {
		logx.Info("PageInitLogic checkKycTask GetListByRefUid Err: ", err, " 请求参数: ", string(reqJson))
		return nil, err
	}
	if len(uidList) == 0 {
		logx.Info("PageInitLogic checkKycTask req: ", string(reqJson), " 没有邀请的用户")
		return nil, nil
	}

	// 查询邀请用户的kyc情况
	userMap, _ := service.NewUserCenterCall(l.ctx).GetUserMap(uidList)
	//if err != nil {
	//	logx.Info("PageInitLogic checkKycTask GetUserList Err: ", err, " 请求参数: ", string(reqJson))
	//	return nil, err
	//}
	fromChannel := consts.GetFromChannel(utils.CheckDev())
	userIdMap := make(map[int64]*usercenter.UserDetail)
	for _, user := range userMap {
		logx.Infof("PageInitLogic userMap user FromChannel is:%s", user.UserInfo.FromChannel)
		if consts.IsKyc(user.Verified) && user.UserInfo.FromChannel == fromChannel {
			userIdMap[user.UID] = user
		}
	}
	return userIdMap, nil
}

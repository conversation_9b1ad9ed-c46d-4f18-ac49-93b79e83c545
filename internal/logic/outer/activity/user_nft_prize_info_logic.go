package activity

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"context"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/models/reclaim_prize_record"
	"gateio_service_marketing_activity/internal/pkg/nft"
	"gateio_service_marketing_activity/internal/service"
	"github.com/spf13/cast"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type UserNFTPrizeInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

const (
	ClaimStatusNot      = 1
	ClaimStatusReceived = 2
)

// 用户抽奖信息
func NewUserNFTPrizeInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UserNFTPrizeInfoLogic {
	return &UserNFTPrizeInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UserNFTPrizeInfoLogic) CalSuiteNum(activityID int64, isCentralized bool, uid int64, walletAddress string) (int64, error) {
	// 如果处于未结算完成阶段，对于中心化就从数据库中获取用户的nft资产，如果是去中心化的就从接口中获取数据
	collNftCountMap := make(map[int64]int64)
	var err error
	// 中心化
	if isCentralized {
		nftService := nft.NewNft(l.ctx, l.svcCtx)
		collNftCountMap, err = nftService.GetNftCountByUser(uid, 0, "")
		if err != nil {
			logx.Infof("[UserNFTPrizeInfoLogic] GetNftCountByUser err:%v", err)
			return 0, err
		}
	} else {
		//去中心化
		//从web3接口获取
		tokenUrls, err := service.GetRedBullList(l.svcCtx, walletAddress)
		if err != nil {
			logx.Infof("CollectionNftProgress service.GetRedBullList  is err, req is:%s,err is:%v", walletAddress, err)
			//添加重试机制 重试3次
			for i := 0; i < 3; i++ {
				//睡眠50毫秒
				time.Sleep(50 * time.Millisecond)
				tokenUrls, err = service.GetRedBullList(l.svcCtx, walletAddress)
				if err != nil {
					continue
				} else {
					break
				}
			}
		}
		for _, tokenUrl := range tokenUrls {
			if tokenUrl != "" {
				urlStrs := strings.Split(tokenUrl, "/")
				nftIdStr := urlStrs[len(urlStrs)-1]
				nftId, _ := strconv.ParseInt(nftIdStr, 10, 64)
				if nftId > 0 {
					collNftCountMap[nftId] += 1
				}
			}
		}
	}

	// 先查询当前活动下的所有nft id 和他对应的 art piece id
	nftConfigList, err := nft.GetNftList(l.svcCtx, l.ctx, activityID)
	if err != nil {
		logc.Infof(l.ctx, "[UserNFTPrizeInfoLogic]GetNftList err:%v", err)
		return 0, err
	}

	nftID2ArtPieceIDMap := make(map[int64]int64)
	artPieceIDMap := make(map[int64]struct{})
	for _, nftConfig := range nftConfigList {
		nftID2ArtPieceIDMap[nftConfig.NftId] = nftConfig.ParentNftId
		artPieceIDMap[nftConfig.ParentNftId] = struct{}{}
	}

	// 计算art piece id 数量
	artPieceMap := make(map[int64]int64)
	for nftId, count := range collNftCountMap {
		artPieceID := nftID2ArtPieceIDMap[nftId]
		artPieceMap[artPieceID] += count
	}

	if len(artPieceMap) == 0 {
		return 0, nil
	}

	// 哪个art piece id 最小，那么最多就只能有几套
	minNum := artPieceMap[nftConfigList[0].ParentNftId]
	for artPieceID := range artPieceIDMap {
		if artPieceMap[artPieceID] < minNum {
			minNum = artPieceMap[artPieceID]
		}
	}
	return minNum, nil
}

func (l *UserNFTPrizeInfoLogic) UserNFTPrizeInfo(req *types.UserNFTPrizeInfoReq, uid int) (resp *types.UserNFTPrizeInfoResp, err error) {
	isCentralized := req.IsCentralization == 1
	resp = &types.UserNFTPrizeInfoResp{}
	resp.UserPrizeInfo.PrizeDetail = make([]types.NFTPrizeDetail, 0)
	resp.UserPrizeInfo.DrawNumber = make([]string, 0)
	if req.ActivityID <= 0 {
		logc.Infof(l.ctx, "UserNFTPrizeInfo req invalid activityID: %d", req.ActivityID)
		return nil, errors.New(consts.ErrInvalidParam, "activity id is invalid!")
	}
	// 如果是中心化的请求，但是没登录的话，就直接返回结果
	if isCentralized && uid == 0 {
		logc.Infof(l.ctx, "UserNFTPrizeInfo isCenteralized:%v, uid is zero: %d", isCentralized, uid)
		return resp, nil
	}
	// 如果是去中心化的请求，但是没登录的话（没传钱包地址），就直接返回结果
	if !isCentralized && len(strings.TrimSpace(req.WalletAddress)) == 0 {
		logc.Infof(l.ctx, "UserNFTPrizeInfo isCentralized:%v walletAddress is empty: %v", isCentralized, req.WalletAddress)
		return resp, nil
	}
	// 查询出对应的活动，判断活动处于什么阶段
	activity, err := nft.GetNftActivity(l.svcCtx, l.ctx, req.ActivityID, isCentralized)
	if err != nil {
		logc.Errorf(l.ctx, "[UserNFTPrizeInfoLogic] GetUserNftActivity parmas: ActivityID: %v isCentralized:%v err:%v", req.ActivityID, isCentralized, err)
		return nil, errors.New(consts.ErrSystemError, "please try again later")
	}

	if activity == nil {
		logc.Warnf(l.ctx, "[UserNFTPrizeInfoLogic] activity is nil: %v %v", req.ActivityID, isCentralized)
		return nil, errors.New(consts.NFT_ACTIVITY_NOT_START, "activity is invalid!")
	}

	if activity.SettlementEndTime.Before(time.Now()) {
		// 如果已经结算完成那么则从lottery表中获取数据
		var lotteryID string
		if isCentralized {
			lotteryID = cast.ToString(uid)
		} else {
			lotteryID = cast.ToString(req.WalletAddress)
		}
		//查出用户所有的奖券信息，奖品也是记录在这个表里面的，price_id表示的是奖品
		lotteryModel := nft_activity.NewNftLotteryModel(l.svcCtx.DBMarketingActivities)
		lotteries, err := lotteryModel.ListPrizesByLotteryID(l.ctx, activity.ActivityId, lotteryID)
		if err != nil {
			logc.Errorf(l.ctx, "[UserNFTPrizeInfoLogic] GetUserNftPrizeInfo err:%v", err)
			return nil, errors.New(consts.ErrSystemError, "please try again later")
		}
		// 获取用户抽奖次数&奖券的信息
		resp.UserPrizeInfo.DrawCnt = int64(len(lotteries))
		prizedLotteryID := make([]int64, 0)
		for _, lottery := range lotteries {
			resp.UserPrizeInfo.DrawNumber = append(resp.UserPrizeInfo.DrawNumber, cast.ToString(lottery.LotteryNumber))
			if lottery.PrizeId != 0 {
				prizedLotteryID = append(prizedLotteryID, lottery.Id)
			}
		}

		// 如果已经开始通知期了，则展示用户的具体中奖信息
		if activity.AnnouncementStartTime.Before(time.Now()) {
			// 中奖信息从表中直接拿出来，但是要展示用户是否已经领取
			claimModel := reclaim_prize_record.NewReclaimPrizeRecordModel(l.svcCtx.DBMarketingActivities)
			claimRecords, err := claimModel.ListRecordByPrizeInfoIDList(l.ctx, prizedLotteryID)
			if err != nil {
				logc.Infof(l.ctx, "[UserNFTPrizeInfoLogic] ListRecordByPrizeInfoIDList err:%v", err)
				return nil, errors.New(consts.ErrSystemError, "please try again later")
			}
			existedRecords := make(map[int64]struct{})
			for _, record := range claimRecords {
				existedRecords[record.PrizeInfoId] = struct{}{}
			}

			prizeList := make([]types.NFTPrizeDetail, 0)
			for _, lottery := range lotteries {
				// prize_id等于证明未中奖
				if lottery.PrizeId == 0 {
					continue
				}
				claimStatus := ClaimStatusNot
				if _, ok := existedRecords[lottery.Id]; ok {
					claimStatus = ClaimStatusReceived
				}
				prizeList = append(prizeList, types.NFTPrizeDetail{
					PrizeInfoID:   lottery.Id,
					PrizeType:     lottery.PrizeId,
					PrizeCategory: PrizeType2PrizeCategoryMap[lottery.PrizeId],
					Status:        claimStatus,
				})
			}
			resp.UserPrizeInfo.PrizeDetail = prizeList
		}
		return resp, nil
	}
	// 还没结算完成，则对于中心化请求，从数据库中获取，对于去中心化请求，则从接口中获取
	suiteNum, err := l.CalSuiteNum(activity.ActivityId, isCentralized, int64(uid), req.WalletAddress)
	if err != nil {
		logc.Infof(l.ctx, "[UserNFTPrizeInfoLogic]CalSuiteNum err:%v params: activityID:%v, isCentralized:%v, uid:%v, walletAddress:%v", err, activity.ActivityId, isCentralized, int64(uid), req.WalletAddress)
		return nil, errors.New(consts.ErrSystemError, "please try again later")
	}
	resp.UserPrizeInfo.DrawCnt = suiteNum

	return resp, nil
}

package activity

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/marketing"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/usercenter"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/vip"
	"context"
	"database/sql"
	"errors"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/consts/activity"
	"gateio_service_marketing_activity/internal/models/vip_airdrop"
	"gateio_service_marketing_activity/internal/mqs/kafka"
	"gateio_service_marketing_activity/internal/pkg/common"
	"gorm.io/gorm"
	"net/http"
	"time"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type VipAirdropApplyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewVipAirdropApplyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *VipAirdropApplyLogic {
	return &VipAirdropApplyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *VipAirdropApplyLogic) VipAirdropApply(req *types.VipAirdropApplyReq, r *http.Request) (resp *types.VipAirdropApplyResp, err error) {
	u, err := common.GetUserInfo(l.ctx)
	if err != nil || u == nil {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	lockKey := consts.VipAirdropApplyLock(req.Season, int64(u.UID))
	ok, err := l.svcCtx.Redis.SetnxExCtx(l.ctx, lockKey, "1", 5)
	if err != nil || !ok {
		return nil, consts.GetErrorMsg(r, consts.ManyAttempts)
	}
	userModel := vip_airdrop.NewCcVipAirdropUserModel(l.svcCtx.DBMarketingActivities)
	applyRecord, err := userModel.FindOneByUidSeason(l.ctx, int64(u.UID), req.Season)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logx.WithContext(l.ctx).Errorf("VipAirdropApplyLogic.VipAirdropApply err:%v", err)
		return nil, err
	} else if applyRecord != nil && applyRecord.Id > 0 {
		return nil, consts.GetErrorMsg(r, consts.ManyAttempts)
	}
	configModel := vip_airdrop.NewCcVipAirdropConfigModel(l.svcCtx.DBMarketingActivities)
	conf, err := configModel.FindOneBySeason(l.ctx, req.Season)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logx.WithContext(l.ctx).Errorf("VipAirdropApplyLogic.VipAirdropApply err:%v", err)
		return nil, err
	}
	if !conf.CheckInCompetitionTime() {
		return nil, consts.GetErrorMsg(r, consts.VIP_AIRDROP_NOT_START)
	}
	userInfo, err := usercenter.NewClient().GetUserInfo(l.ctx, &usercenter.GetUserInfoRequest{UserID: int64(u.UID)})
	if err != nil {
		return nil, err
	}
	//人群检测
	if conf.CrowdId > 0 {
		mc := marketing.NewClient()
		crowdRes, err := mc.CheckUserInCrowd(l.ctx, &marketing.CheckUserInCrowdRequest{CrowdID: conf.CrowdId, UserID: int64(u.UID)})
		if err != nil {
			logx.Errorf("VipAirdropApplyLogic UserInCrowdInfo err:%v", err)
			return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
		}
		if crowdRes.UserInCrowd {
			return nil, consts.GetErrorMsg(r, consts.VIP_AIRDROP_IN_CROWD)
		}
	}
	vipCli := vip.NewClient()
	vipResp, err := vipCli.GetTier(l.ctx, &vip.GetTierRequest{UserID: int64(u.UID)})
	if err != nil {
		logx.WithContext(l.ctx).Errorf("VipAirdropApplyLogic GetTier err:%v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	} else if vipResp.Final.Type != -1 || vipResp.Final.Tier < 5 {
		return nil, consts.GetErrorMsg(r, consts.VIP_AIRDROP_TIER_TOO_LOW)
	}
	vipAirdropUser := &vip_airdrop.CcVipAirdropUser{
		Season:     req.Season,
		Uid:        int64(u.UID),
		Nick:       userInfo.UserInfo.Nick,
		RefUid:     int64(u.RefUID),
		Country:    u.Country,
		ApplyAt:    time.Now(),
		RegisterAt: u.RegTimest,
		Verified:   int64(u.Verified),
		Avatar:     userInfo.UserInfo.Avatar,
		VipTier:    sql.NullInt64{Int64: int64(vipResp.Final.Tier), Valid: true},
	}
	if err := userModel.Insert(l.ctx, vipAirdropUser); err != nil {
		logx.WithContext(l.ctx).Errorf("VipAirdropApplyLogic Insert err:%v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}
	if _, err := l.svcCtx.Redis.Incr(consts.VipAirdropApplyTotal(req.Season)); err != nil {
		logx.WithContext(l.ctx).Errorf("VipAirdropApplyLogic Redis.Incr err:%v", err)
	}
	l.ClimeTask(int64(u.UID), vipAirdropUser.Id, conf)
	return
}

func (l *VipAirdropApplyLogic) ClimeTask(uid int64, businessId int64, conf *vip_airdrop.CcVipAirdropConfig) {
	now := time.Now().Unix()
	kafkaProducer, err := kafka.NewKafkaProducer(l.svcCtx.KafkaConf)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("VipAirdropApplyLogic.ClimeTask err:%v", err)
		return
	}
	defer kafkaProducer.Close()
	taskConfig, err := conf.GetTaskConfig()
	if err != nil {
		logx.WithContext(l.ctx).Errorf("VipAirdropApplyLogic.GetTaskConfig err:%v", err)
		return
	}
	for _, item := range taskConfig {
		logx.WithContext(l.ctx).Infof("VipAirdropApplyLogic.ClimeTask uid:%d taskId:%d", uid, item.TaskId)
		err = kafkaProducer.TaskRecordReceiveProducer(l.ctx, uid, item.TaskId, activity.VipAirdropBusinessType, businessId, now, 0)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("VipAirdropApplyLogic.ClimeTask err:%v", err)
		}
	}
}

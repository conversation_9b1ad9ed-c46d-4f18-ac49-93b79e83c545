package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/pkg/nft"
	"gateio_service_marketing_activity/internal/utils"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type RollNftMintResultLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// NFT铸造结果轮询接口
func NewRollNftMintResultLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RollNftMintResultLogic {
	return &RollNftMintResultLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RollNftMintResultLogic) RollNftMintResult(req *types.RollNftMintResultReq, r *http.Request) (resp *types.RollNftMintResultResp, err error) {
	reqByte, _ := json.Marshal(req)
	// 记录请求返回日志
	defer func() {
		respJson, _ := json.Marshal(resp)
		logx.Info("RollNftMintResult Req: ", string(reqByte), " resp: ", string(respJson), " err: ", err)
	}()
	if (req.UID <= 0 && req.WalletAddress == "") || req.ActivityID <= 0 || (req.ParentNftID <= 0 && req.NftUserSubTaskId <= 0) {
		logx.Infof("RollNftMintResult req invalid parameter: %s", string(reqByte))
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
	}

	// 验证用户
	if req.UID > 0 {
		userInfo := requestools.GetUserInfo(r)
		if userInfo == nil || int64(userInfo.UID) != req.UID {
			return nil, errors.New(consts.ErrUserLogin, "请先登录")
		}
	}

	resp, err = nft.GetLastNftMintLogList(l.svcCtx, l.ctx, req.ActivityID, req.UID, req.WalletAddress, req.OperationID)
	if err != nil {
		logx.Infof("RollNftMintResult GetLastNftMintLogList  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	//todo:生产环境删除
	if resp.MintStatus == 1 && req.UID > 0 {
		pageInitLogic := NewPageInitLogic(r.Context(), l.svcCtx)

		// 调用一下初始化接口
		if utils.CheckDev() {
			// 测试环境，模拟任务完成
			// 模拟百分之五的概率，完成任务
			randNum, _ := utils.SecureRandomInt(20)
			if randNum == 1 {
				_, err := pageInitLogic.PageInit(&types.PageInitReq{
					UserId:        req.UID,
					WalletAddress: req.WalletAddress,
					ActivityID:    req.ActivityID,
				}, r)
				if err != nil {
					logx.Infof("RollNftMintResult PageInit  is err, req is:%s,err is:%v", string(reqByte), err)
					return nil, consts.GetErrorMsg(r, consts.ErrDbError)
				}
			}

			// 删除轮询nft铸造结果的缓存
			rollMintNfrKey := fmt.Sprintf(consts.NftRollMintLogKey, req.ActivityID, req.UID, req.WalletAddress, req.OperationID)
			_, _ = l.svcCtx.Redis.Del(rollMintNfrKey)
		} else {
			// 生产环境，检测铸造是否完成
			err = pageInitLogic.CheckNftCastProgress(&types.PageInitReq{
				UserId:     req.UID,
				ActivityID: req.ActivityID,
			})
			if err != nil {
				logx.Infof("RollNftMintResult CheckNftCastProgress is err, req is:%s,err is:%v", string(reqByte), err)
				return nil, consts.GetErrorMsg(r, consts.ErrDbError)
			}
		}
	}
	return
}

package activity

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/marketing"
	"context"
	"encoding/json"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/consts/activity"
	"gateio_service_marketing_activity/internal/models"
	"gateio_service_marketing_activity/internal/pkg/common"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"
	"math"
	"net/http"
)

type ActivityCheckInLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

type DataCenterUserContractDataSlice []*DataCenterUserContractData

type DataCenterUserContractData struct {
	NetFee int64 `json:"net_fee"`
}

// 签到接口
func NewActivityCheckInLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ActivityCheckInLogic {
	return &ActivityCheckInLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ActivityCheckInLogic) ActivityCheckIn(req *types.ActivityCheckInRequest, r *http.Request) (resp *types.ActivityCheckInResponse, err error) {
	actId := req.ActId

	u, err := common.GetUserInfo(l.ctx)
	if err != nil {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}

	// 是否在活动范围内
	activityConfig := activity.GetContractTransformTask()
	activityStatus, err := common.CheckActivityValid(activityConfig.ActivityId)
	if err != nil || activityStatus != consts.ActivityStatusInProgress {
		return nil, consts.GetErrorMsg(r, consts.ErrActivitySuspended)
	}

	//fmt.Println(u)
	uid := utils.MustInt64(u.UID)
	lk := consts.ActivityCheckInLock(actId, uid)
	ok, err := l.svcCtx.Redis.SetnxExCtx(l.ctx, lk, "1", 5)
	if err != nil || !ok {
		return nil, consts.GetErrorMsg(r, consts.ManyAttempts)
	}

	// 目前只支持单个活动
	//if actId != activity.ContractTransformActivityId {
	//	return nil, errors.New(consts.ErrInvalidActivity, language.GetLangDescByKey(consts.ErrorCodeKeyMap[consts.ErrInvalidActivity], requestools.GetUserLanguage(r)))
	//}

	// 圈群中的爆仓用户
	var crowdId int
	crowdIds := activity.GetNeedCheckCrowdIds()
	crowdId = crowdIds[2]
	param := &marketing.CheckUserInCrowdRequest{
		CrowdID: int64(crowdId),
		UserID:  uid,
	}
	checkUserInCrowdRes, err := marketing.NewClient().CheckUserInCrowd(l.ctx, param)
	if err != nil || checkUserInCrowdRes.UserInCrowd == false {
		logx.Warnf("GetUserNeedShowTask failed, step: CheckUserInCrowd, param : %v, err: %v", param, err)
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidActivity)
	}

	// 今日是否已签到过
	date := utils.TimeUtil{}
	day := date.FormatYmd(date.Now())
	checkInModel := models.NewActivityCheckInRecordsModel(l.svcCtx.DBMarketingActivities)
	todayCheckInRow, err := checkInModel.FindTodayCheckInByUid(l.ctx, actId, uid, day)
	if err != nil {
		return nil, err
	}
	if todayCheckInRow != nil {
		return nil, consts.GetErrorMsg(r, consts.AttendedToday)
	}
	checkInTaskList := activityConfig.ContractTransformCheckInTaskList
	todayCheckInCount, err := checkInModel.CountCheckInNumByUid(l.ctx, actId, uid)
	if err != nil || todayCheckInCount >= utils.MustInt64(len(checkInTaskList)) {
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidActivity)
	}
	// 获取是否有爆仓额度
	NetFee, err := common.GetNetFeeAmount(l.ctx, r, uid)
	if err != nil {
		return nil, err
	}
	// 获取卡券总额度
	couponSumAmount, err := common.GetNetFeeCouponAmount(NetFee)
	//fmt.Println(couponSumAmount)
	if err != nil || couponSumAmount == 0 {
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidActivity)
	}
	checkInInfo := checkInTaskList[todayCheckInCount]
	amount := utils.MustInt64(math.Floor(float64(couponSumAmount) * checkInInfo.PrizeTypeNumRatio))

	if checkInInfo.PrizeType == activity.CheckInPrizeTypeNoPrize {
		var detail = &models.CheckInDetail{
			CouponID:     0,
			CouponSource: "",
			CouponAmount: amount,
		}

		// 将结构体实例序列化为 JSON 字符串
		detailJsonData, _ := json.Marshal(detail)
		var d = &models.ActivityCheckInRecords{
			ActivityId:   actId,
			Uid:          uid,
			Day:          day,
			PrizeId:      0,
			PrizeType:    checkInInfo.PrizeType,
			PrizeTypeNum: checkInInfo.PrizeTypeNum,
			Status:       models.CHECK_IN_STATUS_SUCCESS,
			Detail:       string(detailJsonData),
		}
		err = checkInModel.Insert(l.ctx, d)
		if err != nil {
			return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
		}
	} else if checkInInfo.PrizeType == activity.CheckInPrizeTypeCoupon {
		couponId := activityConfig.ContractTransformCouponInfo.CouponId
		source := activityConfig.ContractTransformCouponInfo.Source
		var detail = &models.CheckInDetail{
			CouponID:     couponId,
			CouponSource: source,
			CouponAmount: amount,
		}

		// 将结构体实例序列化为 JSON 字符串
		detailJsonData, err := json.Marshal(detail)
		if err != nil {
			return nil, consts.GetErrorMsg(r, consts.CheckInFailed)
		}

		var d = &models.ActivityCheckInRecords{
			ActivityId:   actId,
			Uid:          uid,
			Day:          day,
			PrizeId:      couponId,
			PrizeType:    checkInInfo.PrizeType,
			PrizeTypeNum: amount,
			Status:       models.CHECK_IN_STATUS_INIT,
			Detail:       string(detailJsonData),
		}
		createCheckIn, err := checkInModel.CreateCheckIn(l.ctx, d)
		if err != nil {
			return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
		}
		checkInId := createCheckIn.Id
		//fmt.Println(checkInId)
		// 发放卡券
		var status string
		_, err = common.NewCouponCall(l.ctx).SendCouponById(uid, couponId, source, utils.MustString(amount), utils.MustString(checkInId))
		if err != nil {
			status = models.CHECK_IN_STATUS_FAILED
		} else {
			status = models.CHECK_IN_STATUS_SUCCESS
		}

		// 修改状态
		err = checkInModel.UpdateStatusById(l.ctx, checkInId, status)
		if err != nil {
			return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
		}
	} else {
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidActivity)
	}

	l.DelCheckInListCache(actId, uid)

	return
}

// DelCheckInListCache 删除签到记录缓存
func (l *ActivityCheckInLogic) DelCheckInListCache(actId int64, uid int64) {
	redisKey := consts.ActivityCheckInList(actId, uid)
	_, _ = l.svcCtx.Redis.DelCtx(l.ctx, redisKey)
	return
}

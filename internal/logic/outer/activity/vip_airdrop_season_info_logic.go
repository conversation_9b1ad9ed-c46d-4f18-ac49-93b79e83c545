package activity

import (
	"bitbucket.org/gatebackend/go-zero/core/stores/redis"
	"context"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/vip_airdrop"
	"gorm.io/gorm"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"errors"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"net/http"
)

type VipAirdropSeasonInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewVipAirdropSeasonInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *VipAirdropSeasonInfoLogic {
	return &VipAirdropSeasonInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *VipAirdropSeasonInfoLogic) VipAirdropSeasonInfo(req *types.VipAirdropSeasonInfoReq, r *http.Request) (resp *types.VipAirdropSeasonInfoResp, err error) {
	configModel := vip_airdrop.NewCcVipAirdropConfigModel(l.svcCtx.DBMarketingActivities)
	conf, err := configModel.FindOneBySeason(l.ctx, req.Season)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logx.Errorf("VipAirdropApplyLogic.VipAirdropApply err:%v", err)
		return nil, err
	}
	if !conf.CheckInBookingTime() {
		return nil, consts.GetErrorMsg(r, consts.VIP_AIRDROP_NOT_START)
	}
	applyTotal, err := l.svcCtx.Redis.Get(consts.VipAirdropApplyTotal(req.Season))
	if err != nil && !errors.Is(err, redis.Nil) {
		logx.WithContext(l.ctx).Errorf("VipAirdropSeasonInfoLogic.VipAirdropSeasonInfo err:%v", err)
	}
	resp = &types.VipAirdropSeasonInfoResp{
		ApplyTotal:           applyTotal,
		AirdropCoin:          conf.AirdropCoin.String,
		AirdropCoinUrl:       conf.AirdropCoinUrl.String,
		AirdropCoinNum:       conf.AirdropCoinNum.String,
		CompetitionStartTime: conf.CompetitionStartTime,
		CompetitionEndTime:   conf.CompetitionEndTime,
	}

	return
}

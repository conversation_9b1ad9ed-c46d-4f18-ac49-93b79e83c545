package activity

import (
	"context"
	"errors"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/vip_airdrop"
	"gateio_service_marketing_activity/internal/pkg/common"
	"gorm.io/gorm"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type VipAirdropUserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewVipAirdropUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *VipAirdropUserInfoLogic {
	return &VipAirdropUserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *VipAirdropUserInfoLogic) VipAirdropUserInfo(req *types.VipAirdropUserInfoReq, r *http.Request) (resp *types.VipAirdropUserInfoResp, err error) {
	u, err := common.GetUserInfo(l.ctx)
	if err != nil || u == nil {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	userModel := vip_airdrop.NewCcVipAirdropUserModel(l.svcCtx.DBMarketingActivities)
	applyRecord, err := userModel.FindOneByUidSeason(l.ctx, int64(u.UID), req.Season)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logx.WithContext(l.ctx).Errorf("VipAirdropApplyLogic.VipAirdropTaskList err:%v", err)
		return nil, err
	}
	resp = &types.VipAirdropUserInfoResp{
		IsApply: applyRecord != nil && applyRecord.Id > 0,
	}
	return
}

package activity

import (
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"context"
	"encoding/json"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/service"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type CaptchaVerifyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// web3客户端防女巫验证
func NewCaptchaVerifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CaptchaVerifyLogic {
	return &CaptchaVerifyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CaptchaVerifyLogic) CaptchaVerify(req *types.CaptchaVerifyReq, r *http.Request) (resp *types.CaptchaVerifyResp, err error) {
	reqByte, _ := json.Marshal(req)
	if req.WalletAddress == "" {
		logx.Infof("CaptchaVerify req invalid parameter: %s", string(reqByte))
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
	}
	resp = &types.CaptchaVerifyResp{
		VerifyStatus: 1,
	}
	//验证是否是国内IP
	ip := requestools.GetClientIP(r)
	//调用其他服务接口做防女巫验证
	agent := r.UserAgent()
	captchaVerifyReq := &service.CaptchaVerifyReq{
		CaptchaType:      req.CaptchaType,
		DeviceType:       0,
		ClientIP:         ip,
		Scene:            req.Scene,
		CaptchaID:        req.CaptchaID,
		CaptchaOutput:    req.CaptchaOutput,
		GenTime:          req.GenTime,
		LotNumber:        req.LotNumber,
		PassToken:        req.PassToken,
		CaptchaAccountID: req.WalletAddress,
		UserAgent:        agent,
	}
	captchaVerifyCode, err := service.NewValidationCenterClient().CaptchaVerify(l.ctx, captchaVerifyReq)
	if err != nil {
		captchaVerifyReqByte, _ := json.Marshal(captchaVerifyReq)
		logx.Infof("ClientVerifyNftMint CaptchaVerify is err, err is %v,CaptchaVerifyReq is:%s", err, string(captchaVerifyReqByte))
		return nil, consts.GetErrorMsg(r, consts.ErrApiError)
	}
	//防女巫验证不通过
	if captchaVerifyCode != 0 {
		resp.VerifyStatus = 2
		return resp, nil
	}
	return
}

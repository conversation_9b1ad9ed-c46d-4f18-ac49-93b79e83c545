package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"github.com/spf13/cast"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"
)

type EncryptDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewEncryptDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *EncryptDataLogic {
	return &EncryptDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *EncryptDataLogic) EncryptData(req *types.EncryptDataReq, r *http.Request) (resp *types.EncryptDataResp, err error) {
	defer func() {
		reqJson, _ := json.Marshal(req)
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("EncryptData Req: ", string(reqJson), " resp: ", string(respJson), " err: ", err)
	}()

	// 验证用户
	if req.Uid > 0 {
		userInfo := requestools.GetUserInfo(r)
		if userInfo == nil || int64(userInfo.UID) != req.Uid {
			return nil, errors.New(consts.ErrUserLogin, "请先登录")
		}
	}

	var uidEncrypted string
	var walletAddressEncrypted string
	var ActivityIdEncrypted string

	// 加密请求参数
	if req.Uid > 0 {
		uidEncrypted, err = l.buildEncryptData(cast.ToString(req.Uid))
		if err != nil {
			return nil, err
		}
	}
	if len(req.WalletAddress) > 0 {
		walletAddressEncrypted, err = l.buildEncryptData(req.WalletAddress)
		if err != nil {
			return nil, err
		}
	}
	if req.ActivityId > 0 {
		ActivityIdEncrypted, err = l.buildEncryptData(cast.ToString(req.ActivityId))
		if err != nil {
			return nil, err
		}
	}

	// 返回加密后的数据
	return &types.EncryptDataResp{
		EncryptedUid:           uidEncrypted,
		EncryptedWalletAddress: walletAddressEncrypted,
		EncryptedActivityId:    ActivityIdEncrypted,
	}, nil
}

/**
 * 生成加密数据
 */
func (l *EncryptDataLogic) buildEncryptData(originalId string) (string, error) {
	dataModel := nft.NewNftEncryptedMappingModel(l.svcCtx.DBMarketingActivities)

	// 判断是否已存在
	data, err := dataModel.GetDataByOriginalId(l.ctx, originalId)
	if err != nil {
		return "", errors.New(consts.ErrDbError, fmt.Sprintf("buildEncryptData GetDataByOriginalId error: %v", err))
	}
	if data != nil && data.OriginalId != "" {
		return utils.Encode(cast.ToUint64(data.Id)), nil
	}

	// 生成加密映射
	insertData := &nft.NftEncryptedMapping{
		OriginalId: originalId,
		CreatedAt:  time.Now(),
	}
	if err := dataModel.Insert(l.ctx, insertData); err != nil {
		return "", err
	}
	return utils.Encode(cast.ToUint64(insertData.Id)), nil
}

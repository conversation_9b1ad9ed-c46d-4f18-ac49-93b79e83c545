package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"github.com/spf13/cast"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/models/nft_mint_log"
	"gateio_service_marketing_activity/internal/mqs/kafka"
	pkg_nft "gateio_service_marketing_activity/internal/pkg/nft"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"
)

type SurpriseExtensionTaskListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSurpriseExtensionTaskListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SurpriseExtensionTaskListLogic {
	return &SurpriseExtensionTaskListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SurpriseExtensionTaskListLogic) SurpriseExtensionTaskList(req *types.SurpriseExtensionTaskListReq, r *http.Request) (resp *types.SurpriseExtensionTaskListResp, err error) {
	// 记录请求返回日志
	defer func() {
		reqJson, _ := json.Marshal(req)
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("SurpriseExtensionTaskList Req: ", string(reqJson), " resp: ", string(respJson), " err: ", err)
	}()

	// 请求参数验证
	if err := l.NftTaskListValidate(req); err != nil {
		return nil, err
	}
	reqByte, _ := json.Marshal(req)

	// 获取进行中的惊喜加时活动
	activityInfo, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetRunningSurpriseExtensionTask(l.ctx, 1, 1)
	if err != nil {
		logx.Infof("NftTaskList GetRunningSurpriseExtensionTask  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}
	if activityInfo == nil {
		logx.Infof("NftTaskList activityInfo is null, req is:%s", string(reqByte))
		return nil, errors.New(consts.NFT_ACTIVITY_END, "惊喜加时-活动已结束")
	}
	if activityInfo.TaskStartTime.After(time.Now()) {
		logx.Infof("NftTaskList The current period is not an additional activity period, req is:%s", string(reqByte))
		return nil, errors.New(consts.NFT_ACTIVITY_NOT_START, "惊喜加时-活动未开始")
	}

	// 获取任务配置
	taskList, err := l.GetTaskListByUser(req, activityInfo)
	if err != nil {
		logx.Infof("NftTaskList GetTaskListByUser  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}

	// 获取子任务列表数据
	userSubTaskList, err := l.GetUserSubTask(req, activityInfo)
	if err != nil {
		logx.Infof("NftTaskList GetUserSubTask  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}

	// 如果没有任务，则进行初始化
	userSubTaskList, err = l.InitTaskRecord(req, activityInfo, taskList, userSubTaskList)
	if err != nil {
		logx.Infof("NftTaskList InitTaskRecord  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}

	// 共需收集数量
	totalToCollect, err := l.CountTotalToCollect(req, taskList)
	if err != nil {
		logx.Infof("NftTaskList CountTotalToCollect  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}

	// 获取NFT列表
	nftList, err := pkg_nft.GetNftList(l.svcCtx, l.ctx, activityInfo.ActivityId)
	if err != nil {
		logx.Infof("NftTaskList GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}

	// 获取NFT铸造记录
	mintNftLogs, err := pkg_nft.GetNftMintLogList(l.svcCtx, l.ctx, req.UserId, req.WalletAddress, activityInfo.ActivityId)
	if err != nil {
		logx.Infof("NftTaskList GetNftMintLogList is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}

	//获取全部普通nft ID
	basicParentIdNftIds := make([]int64, 0)
	for _, detail := range nftList {
		if detail.NftType == 1 {
			basicParentIdNftIds = append(basicParentIdNftIds, detail.ParentNftId)
		}
	}

	// 已收集数量（普通nft 且 铸造成功）
	collectedNumber := 0
	for _, nftLog := range mintNftLogs {
		if utils.Int64InSlice(nftLog.ParentNftId, basicParentIdNftIds) && nftLog.MintStatus == nft_mint_log.MINT_STATUS_SUCCESS {
			collectedNumber++
		}
	}

	// 获取任务状态
	taskStatus, err := l.GetTaskStatus(req, activityInfo, collectedNumber, int(totalToCollect))
	if err != nil {
		logx.Infof("NftTaskList GetTaskStatus is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}

	// 格式化数据
	rspList, err := l.NftTaskListFormat(req, taskList, userSubTaskList)
	if err != nil {
		logx.Infof("NftTaskList NftTaskListFormat is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}
	return &types.SurpriseExtensionTaskListResp{
		BaseInfo: types.SurpriseExtensionTaskBaseInfo{
			ActivityId:     cast.ToInt64(activityInfo.ActivityId),
			TotalToCollect: cast.ToInt64(totalToCollect),
			Collected:      cast.ToInt64(collectedNumber),
			IsFromNftChain: req.IsFromNftChain,
			TaskStatus:     taskStatus,
		},
		List: rspList,
	}, nil
}

/**
 * 获取任务状态
 */
func (l *SurpriseExtensionTaskListLogic) GetTaskStatus(req *types.SurpriseExtensionTaskListReq, activityInfo *nft_activity.NftActivity, collectedNumber int, totalToCollect int) (status int, err error) {
	taskStatus := 1
	if activityInfo.SurpriseExtensionStartTime.Before(time.Now()) && activityInfo.SurpriseExtensionEndTime.After(time.Now()) {
		taskStatus = 2 //进行中
	}
	if activityInfo.SurpriseExtensionEndTime.Before(time.Now()) {
		taskStatus = 4 //已结束
	}
	if collectedNumber >= totalToCollect {
		taskStatus = 3 //已经领取完
	}
	return taskStatus, nil
}

/**
 * 获取符合用户条件的任务配置
 */
func (l *SurpriseExtensionTaskListLogic) GetTaskListByUser(req *types.SurpriseExtensionTaskListReq, activityInfo *nft_activity.NftActivity) (rspTaskList []*nft.NftTask, err error) {
	taskModel := nft.NewNftTaskModel(l.svcCtx.DBMarketingActivities)
	taskList, err := taskModel.GetTaskList(l.ctx, 1, nft.SceneTypeSurpriseExtension)
	if err != nil {
		return nil, err
	}

	// 获取符合用户条件的任务配置
	rspTaskList = make([]*nft.NftTask, 0)
	for _, task := range taskList {
		if req.IsFromNftChain == 1 {
			// 通过ch码邀请注册
			if task.TaskType == nft.TaskTypeSurpriseExtensionInviteeCh || task.TaskType == nft.TaskTypeSurpriseExtensionInviter {
				rspTaskList = append(rspTaskList, task)
			}
		} else {
			// 没有通过ch码邀请注册
			if task.TaskType == nft.TaskTypeSurpriseExtensionInviteeNoCh {
				rspTaskList = append(rspTaskList, task)
			}
		}
	}
	return
}

/**
 * 共需收集数量
 */
func (l *SurpriseExtensionTaskListLogic) CountTotalToCollect(req *types.SurpriseExtensionTaskListReq, taskList []*nft.NftTask) (count int64, err error) {
	for _, task := range taskList {
		count += task.CompletionLimit
	}
	return
}

/**
 * 统计已收集数量
 */
func (l *SurpriseExtensionTaskListLogic) CountFreeNft(req *types.SurpriseExtensionTaskListReq, activityId int64) (count int64, err error) {
	// 去中心化过滤
	countFilter := map[string]interface{}{
		"activity_id": activityId,
		"mint_status": nft_mint_log.MINT_STATUS_SUCCESS,
		"pay_type":    nft_mint_log.PAY_TYPE_NO_CENTER,
	}
	// 用户请求
	countFilter["uid"] = req.UserId
	countFilter["pay_type"] = nft_mint_log.PAY_TYPE_CENTER

	// 获取免费NFT数量
	count, err = nft_mint_log.NewNftMintLogModel(l.svcCtx.DBMarketingActivities).CountByFilter(l.ctx, countFilter)
	if err != nil {
		return 0, err
	}
	return
}

/**
 * 格式化数据
 */
func (l *SurpriseExtensionTaskListLogic) NftTaskListFormat(req *types.SurpriseExtensionTaskListReq, taskList []*nft.NftTask, subTaskList []*nft.NftUserSubTask) (rspList []*types.SurpriseExtensionTaskItem, err error) {
	// 获取已领取NFT的任务数据
	subTaskIdList := make([]int64, 0)
	for _, subTask := range subTaskList {
		subTaskIdList = append(subTaskIdList, subTask.Id)
	}
	nftMintLogMap, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftMintLogsBySubTaskIDsMap(l.ctx, subTaskIdList)
	if err != nil {
		return
	}

	// 组装子任务数据
	inviteeList := make([]*types.SurpriseExtensionTaskItem, 0) // 被邀请人任务
	inviterList := make([]*types.SurpriseExtensionTaskItem, 0) // 邀请人任务
	for _, task := range taskList {
		doneLimit := 0
		rspSubTaskList := make([]*types.SurpriseExtensionTaskSubTaskItem, 0)
		for _, taskRecord := range subTaskList {
			if taskRecord.TaskId == task.Id {
				// 领取nft状态
				var receiveNftStatus int64
				if nftMintLog, ok := nftMintLogMap[taskRecord.Id]; ok {
					if ok && nftMintLog != nil {
						if nftMintLog.MintStatus == nft_mint_log.MINT_STATUS_IN_PROGRESS {
							// 已领取（铸造中）
							receiveNftStatus = 2
						} else if nftMintLog.MintStatus == nft_mint_log.MINT_STATUS_SUCCESS {
							// 已领取（铸造完成）
							receiveNftStatus = 3
						}
					}
				} else if taskRecord.Status == nft.TaskStatusInitial {
					// 未完成任务
					receiveNftStatus = 0
				} else if taskRecord.Status == nft.TaskStatusCompleted {
					// 任务已完成
					receiveNftStatus = 1
				}

				// 帮助完成任务的用户信息
				helpUserId := taskRecord.HelpUserId

				// 组装数据
				rspSubTaskList = append(rspSubTaskList, &types.SurpriseExtensionTaskSubTaskItem{
					SubTaskId:        taskRecord.Id,
					HelpUserId:       helpUserId,
					HelpUserAvatar:   taskRecord.HelpUserAvatar,
					ReceiveNftStatus: receiveNftStatus,
				})

				// 完成次数
				if taskRecord.Status == nft.TaskStatusCompleted {
					doneLimit++
				}
			}
		}

		// 主任务数据
		rspItem := &types.SurpriseExtensionTaskItem{
			TaskId:          task.TaskId,             // 任务ID
			TaskName:        task.TaskName,           // 任务名称
			TaskType:        task.TaskType,           // 任务类型
			DoneLimit:       cast.ToInt64(doneLimit), // 已完成次数
			CompletionLimit: task.CompletionLimit,    // 可完成次数
			SubTaskList:     rspSubTaskList,
		}

		if task.TaskType == nft.TaskTypeSurpriseExtensionInviter {
			inviteeList = append(inviteeList, rspItem)
		} else if task.TaskType == nft.TaskTypeSurpriseExtensionInviteeNoCh || task.TaskType == nft.TaskTypeSurpriseExtensionInviteeCh {
			inviterList = append(inviterList, rspItem)
		}
	}

	// 被邀请人任务在前，邀请人任务在后
	rspList = append(inviteeList, inviterList...)
	return
}

/**
 * 初始化任务
 */
func (l *SurpriseExtensionTaskListLogic) InitTaskRecord(req *types.SurpriseExtensionTaskListReq, activityInfo *nft_activity.NftActivity, taskList []*nft.NftTask, userSubTaskList []*nft.NftUserSubTask) ([]*nft.NftUserSubTask, error) {
	subTaskDataModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)
	rspList := make([]*nft.NftUserSubTask, 0)
	rspList = append(rspList, userSubTaskList...)

	// 如果已经有邀请人任务和被邀请人任务，则直接返回
	var inviteTaskCount int64  // 邀请人任务数量
	var inviteeTaskCount int64 // 被邀请人任务数量
	for _, task := range userSubTaskList {
		if task.TaskType == nft.TaskTypeSurpriseExtensionInviteeNoCh || task.TaskType == nft.TaskTypeSurpriseExtensionInviteeCh {
			inviteTaskCount++
			continue
		}
		if task.TaskType == nft.TaskTypeSurpriseExtensionInviter {
			inviteeTaskCount++
			continue
		}
	}

	// 生成被邀请人任务
	if inviteeTaskCount == 0 {
		// 如果没有被邀请人任务，并且用户是否是通过ch码邀请注册，则创建
		if req.IsFromNftChain == 1 {
			// 根据任务配置，生成被邀请人任务
			var inviteeTask *nft.NftTask
			for _, task := range taskList {
				if task.TaskType == nft.TaskTypeSurpriseExtensionInviter {
					inviteeTask = task
				}
			}
			if inviteeTask != nil {
				userSubTaskList, err := pkg_nft.GenerateAndSaveUserSubTask(l.ctx, l.svcCtx, activityInfo.ActivityId, req.UserId, []*nft.NftTask{inviteeTask})
				if err != nil {
					return nil, err
				}
				rspList = append(rspList, userSubTaskList...)

				// 上报任务系统
				userSubTaskModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)
				for _, userSubTask := range userSubTaskList {
					err := kafka.SubmitTaskRecord(l.ctx, l.svcCtx, req.UserId, userSubTask.Id, inviteeTask.TaskId)
					if err == nil {
						userSubTask.Status = nft.TaskStatusReported
						err = userSubTaskModel.UpdateOne(l.ctx, userSubTask.Id, []string{"status"}, userSubTask)
						if err != nil {
							return nil, err
						}
					}
				}
			}
		}
	}

	// 生成邀请人任务
	for _, task := range taskList {
		if task.TaskType != nft.TaskTypeSurpriseExtensionInviteeNoCh && task.TaskType != nft.TaskTypeSurpriseExtensionInviteeCh {
			continue
		}

		// 插入任务记录
		for range int(task.CompletionLimit - inviteTaskCount) {
			// 生成本地任务
			taskRecord := &nft.NftUserSubTask{
				ActivityId:    activityInfo.ActivityId,
				UserId:        cast.ToInt64(req.UserId),
				WalletAddress: req.WalletAddress,
				TaskId:        task.Id,
				TaskType:      task.TaskType,
				MediaId:       task.MediaId,
				Status:        nft.TaskStatusInitial,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
				SceneType:     nft.SceneTypeSurpriseExtension,
			}
			inserId, err := subTaskDataModel.CreateTaskRecord(l.ctx, taskRecord)
			if err != nil {
				return nil, err
			}
			fmt.Println("insertId: ", inserId)

			// 组合列表数据
			rspList = append(rspList, taskRecord)
		}
	}

	return rspList, nil
}

/**
 * 请求任务列表数据
 */
func (l *SurpriseExtensionTaskListLogic) GetUserSubTask(req *types.SurpriseExtensionTaskListReq, activityInfo *nft_activity.NftActivity) ([]*nft.NftUserSubTask, error) {
	dataModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)
	recordList, err := dataModel.GetTaskRecordListByUid(l.ctx, activityInfo.ActivityId, req.UserId, map[string]interface{}{
		"scene_type": nft.SceneTypeSurpriseExtension,
	})
	if err != nil {
		return nil, fmt.Errorf("GetTaskRecordListByUid error: %v", err)
	}
	return recordList, nil
}

/**
 * 请求参数验证
 */
func (l *SurpriseExtensionTaskListLogic) NftTaskListValidate(req *types.SurpriseExtensionTaskListReq) (err error) {
	// 验证登陆态
	userInfo, err := utils.GetUserInfo(l.ctx)
	if err != nil {
		logx.Infof("SurpriseExtensionTaskList GetUserInfo is err:%v ", err)
	}
	if userInfo == nil || userInfo.UID == 0 {
		return errors.New(consts.ErrUserLogin, "请先登录")
	}
	req.UserId = int64(userInfo.UID)

	// 验证用户是否是子账户
	userMap, _ := service.NewUserCenterCall(l.ctx).GetUserMap([]int64{req.UserId})
	user, exists := userMap[req.UserId]
	if !exists || user == nil {
		return errors.New(consts.ErrUserLogin, "请先登录")
	}
	if user.IsSub == 1 {
		return errors.New(consts.NFT_SUB_ACCOUNT_NO_ALLOW, "子账号不能参与活动")
	}

	// 是否通过nft落地页ch码注册
	fromChannel := consts.GetFromChannel(utils.CheckDev())
	if user.UserInfo.FromChannel == fromChannel {
		req.IsFromNftChain = 1
	}

	// TODO 临时测试，注意去掉
	if req.UserId == ********** {
		req.IsFromNftChain = 1
	}

	return nil
}

package activity

import (
	"context"
	"encoding/json"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/models/nft_detail"
	pkgnft "gateio_service_marketing_activity/internal/pkg/nft"
	"gateio_service_marketing_activity/internal/service"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"

	"bitbucket.org/gatebackend/go-zero/core/logx"
)

type CollectionNftProgressLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// nft收集进度查询
func NewCollectionNftProgressLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CollectionNftProgressLogic {
	return &CollectionNftProgressLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CollectionNftProgressLogic) CollectionNftProgress(req *types.CollectionNftProgressReq, r *http.Request) (resp *types.CollectionNftProgressResp, err error) {
	reqByte, _ := json.Marshal(req)
	defer func() {
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("CollectionNftProgress Req: ", string(reqByte), " resp: ", string(respJson), " err: ", err)
	}()
	if (req.WalletAddress == "" && req.UID <= 0) || req.NftType <= 0 {
		logx.Infof("CollectionNftProgress req invalid parameter: %s", string(reqByte))
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
	}

	// 验证用户
	if req.UID > 0 {
		userInfo := requestools.GetUserInfo(r)
		if userInfo == nil || int64(userInfo.UID) != req.UID {
			return nil, errors.New(consts.ErrUserLogin, "请先登录")
		}
		//获取账户信息，用于验证是否是子账户
		userMap, _ := service.NewUserCenterCall(l.ctx).GetUserMap([]int64{req.UID})
		if user, exists := userMap[req.UID]; exists && user != nil && user.IsSub == 1 {
			logx.Infof("CollectionNftProgress sub user use :%d, is_sub val is:%d", req.UID, userMap[req.UID].IsSub)
			return nil, errors.New(consts.NFT_SUB_ACCOUNT_NO_ALLOW, "子账号不能参与活动")
		}
	}

	//根据类型获取NFT信息
	nftList, err := pkgnft.GetNftListByNftType(l.svcCtx, l.ctx, req.NftType)
	if err != nil {
		logx.Infof("CollectionNftProgress GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	// 获取用户收集的NFT数据
	collNftCountMap, err := l.GetCollectionNftInfo(req, r)
	if err != nil {
		logx.Infof("CollectionNftProgress l.GetCollectionNftInfo  is err, req is:%v,err is:%v", req, err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	activityList, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftActivityList(l.ctx, 0, 1, req.UID > 0)
	if err != nil {
		logx.Infof("CollectionNftProgress GetNftActivityList  is err, req is:%v,err is:%v", req, err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	activityMap := map[int64]*nft_activity.NftActivity{}
	for _, activity := range activityList {
		activityMap[activity.ActivityId] = activity
	}
	//定义分期数据
	roundNftListMap := map[int64][]*nft_detail.NftDetail{}
	for _, nftInfo := range nftList {
		if roundNftListMap[nftInfo.ActivityId] == nil {
			roundNftListMap[nftInfo.ActivityId] = []*nft_detail.NftDetail{}
		}
		roundNftListMap[nftInfo.ActivityId] = append(roundNftListMap[nftInfo.ActivityId], nftInfo)
	}
	roundList := make([]*ActivityRound, len(roundNftListMap))
	roundIndex := 0
	for activityId, daoNftList := range roundNftListMap {
		//活动状态 1:未开始 2:进行中 3:已错过
		campaignStatus := 1

		if activityMap[activityId] != nil {
			if activityMap[activityId].StartTime.After(time.Now()) {
				campaignStatus = 3
			}
			if activityMap[activityId].EndTime.Before(time.Now()) {
				campaignStatus = 2
			}
		}
		respNftList := make([]*types.NftProgress, len(daoNftList))
		nftTypeCountMap := make(map[int64]struct{})
		for index, nftInfo := range daoNftList {
			//发行状态 1:已拥有 2:未拥有 3:未发行
			releaseStatus := 2
			if nftInfo.CampaignStartTime.After(time.Now()) {
				//未到开始时间 未发行
				releaseStatus = 3
			}
			// 已收集NFT数量
			var collNftCount int
			if tmpCount, exists := collNftCountMap[nftInfo.NftId]; exists && tmpCount > 0 {
				collNftCount = int(tmpCount)
				nftTypeCountMap[nftInfo.NftId] = struct{}{}
			}
			if collNftCount > 0 {
				//拥有数量大于0 已拥有
				releaseStatus = 1
			}

			// 组合数据
			respNftList[index] = &types.NftProgress{
				NftID:         nftInfo.NftId,
				NftName:       nftInfo.NftName,
				NftIconURL:    nftInfo.NftIconUrl,
				NftAmount:     collNftCount,
				ReleaseStatus: releaseStatus,
			}
		}
		collectNftCount := len(nftTypeCountMap)
		if campaignStatus == 3 {
			collectNftCount = 0
		}
		roundList[roundIndex] = &ActivityRound{
			ActivityId:      activityId,
			AllNftCount:     len(roundNftListMap[activityId]),
			CollectNftCount: collectNftCount,
			CampaignStatus:  campaignStatus,
			NftList:         respNftList,
		}
		roundIndex++
	}
	//正序排序
	sort.Slice(roundList, func(i, j int) bool {
		return roundList[i].ActivityId < roundList[j].ActivityId
	})
	resp = &types.CollectionNftProgressResp{
		List: CollectionNftProgressFormat(roundList),
	}
	return
}

/**
 * 中心化 - 获取收集NFT数量
 */
/*
 func (l *CollectionNftProgressLogic) GetCollectionNftInfoCentralization(req *types.CollectionNftProgressReq, r *http.Request) (collNftCountMap map[int64]map[int64]int64, err error) {
	isCentralization := false
	if req.UID > 0 {
		isCentralization = true
	}
	collNftCountMap = make(map[int64]map[int64]int64)
	if isCentralization {
		mintNftLogs, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftMintLogList(l.ctx, req.UID, "", 0, 0, 0, 0)
		if err != nil {
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		for _, mintNftLog := range mintNftLogs {
			if mintNftLog.MintStatus != nft_mint_log.MINT_STATUS_SUCCESS {
				continue
			}
			if _, ok := collNftCountMap[mintNftLog.ActivityId]; !ok {
				collNftCountMap[mintNftLog.ActivityId] = make(map[int64]int64)
			}
			collNftCountMap[mintNftLog.ActivityId][mintNftLog.NftId] += 1
		}
	}
	return collNftCountMap, nil
}
*/

/**
 * 获取收集NFT数量
 */
func (l *CollectionNftProgressLogic) GetCollectionNftInfo(req *types.CollectionNftProgressReq, r *http.Request) (collNftCountMap map[int64]int64, err error) {
	isCentralization := false
	if req.UID > 0 {
		isCentralization = true
	}
	collNftCountMap = make(map[int64]int64)

	// 中心化
	if isCentralization {
		nftService := pkgnft.NewNft(l.ctx, l.svcCtx)
		collNftCountMap, err = nftService.GetNftCountByUser(req.UID, 0, "")
	} else {
		//去中心化
		//从web3接口获取
		tokenUrls, err := service.GetRedBullList(l.svcCtx, req.WalletAddress)
		if err != nil {
			logx.Infof("CollectionNftProgress service.GetRedBullList  is err, req is:%s,err is:%v", req.WalletAddress, err)
			//添加重试机制 重试3次
			for i := 0; i < 3; i++ {
				//睡眠50毫秒
				time.Sleep(50 * time.Millisecond)
				tokenUrls, err = service.GetRedBullList(l.svcCtx, req.WalletAddress)
				if err != nil {
					continue
				} else {
					break
				}
			}
		}
		for _, tokenUrl := range tokenUrls {
			if tokenUrl != "" {
				urlStrs := strings.Split(tokenUrl, "/")
				nftIdStr := urlStrs[len(urlStrs)-1]
				nftId, _ := strconv.ParseInt(nftIdStr, 10, 64)
				if nftId > 0 {
					collNftCountMap[nftId] += 1
				}
			}
		}
	}
	return
}

// 活动期
type ActivityRound struct {
	ActivityId      int64                `json:"activity_id"`
	AllNftCount     int                  `json:"all_nft_count"`
	CollectNftCount int                  `json:"collect_nft_count"`
	CampaignStatus  int                  `json:"campaign_status"` //活动状态 1:未开始 2:进行中 3:已错过
	NftList         []*types.NftProgress `json:"nft_list"`
}

// 格式化返回结果
func CollectionNftProgressFormat(logicList []*ActivityRound) []*types.ActivityRound {
	list := make([]*types.ActivityRound, len(logicList))
	for index, roundData := range logicList {
		list[index] = &types.ActivityRound{
			RoundNum:        index + 1,
			AllNftCount:     roundData.AllNftCount,
			CollectNftCount: roundData.CollectNftCount,
			CampaignStatus:  roundData.CampaignStatus,
			NftList:         roundData.NftList,
		}
	}
	return list
}

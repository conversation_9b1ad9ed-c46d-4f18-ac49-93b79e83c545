package activity

import (
	"context"
	"encoding/base64"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"bitbucket.org/gatebackend/go-zero/rest/errors"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/svc"
	nftHandler "gateio_service_marketing_activity/internal/task/nft"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"
)

type NftTestLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewNftTestLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NftTestLogic {
	return &NftTestLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NftTestLogic) NftTest(req *types.NftTestReq) (resp *types.NftTestResp, err error) {
	if req.Business == 1 {
		originByte, tErr := base64.StdEncoding.DecodeString(req.ParamStr)
		if tErr != nil {
			return nil, tErr
		}
		calcSign, err := utils.CalcMd5Test(req.ParamStr)
		if err != nil {
			return nil, err
		}
		if calcSign != req.Sign {
			return nil, errors.New(consts.ErrInvalidParam, "sign error")
		}
		originStr := string(originByte)
		err = l.parseParamStr(originStr)
		if err != nil {
			return nil, err
		}
	} else if req.Business == 2 {
		nftHandler.CentralizationSyncStatus(context.Background(), l.svcCtx, &xxljob.TaskRequest{})
	}
	return &types.NftTestResp{
		Result: "Hello world",
	}, nil
}

func (l *NftTestLogic) parseParamStr(originStr string) error {
	taskModel := nft.NewNftTaskModel(l.svcCtx.DBMarketingActivities)
	return taskModel.ParseParam(originStr)
}

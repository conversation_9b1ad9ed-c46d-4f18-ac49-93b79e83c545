package activity

import (
	"context"
	"encoding/json"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type ReceiveSubTaskStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 接收任务完成通知
func NewReceiveSubTaskStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReceiveSubTaskStatusLogic {
	return &ReceiveSubTaskStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReceiveSubTaskStatusLogic) ReceiveSubTaskStatus(req *types.ReceiveSubTaskStatusReq, r *http.Request) (resp *types.ReceiveSubTaskStatusResp, err error) {
	reqByte, _ := json.Marshal(req)
	if req.NftUserSubTaskId <= 0 {
		logx.Infof("ReceiveSubTaskStatus req invalid parameter: %s", string(reqByte))
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
	}
	defer func() {
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("ReceiveSubTaskStatus Req: ", string(reqByte), " resp: ", string(respJson), " err: ", err)
	}()
	resp = &types.ReceiveSubTaskStatusResp{
		ReceiveStatus: 1,
	}

	//更新数据库 完成状态
	updateMap := map[string]interface{}{}
	updateMap["status"] = 2
	err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).UpdateSubTaskStatus(l.ctx, req.NftUserSubTaskId, updateMap)
	if err != nil {
		logx.Infof("ReceiveSubTaskStatus UpdateSubTaskStatus  is err, req is:%s,err is:%v", string(reqByte), err)
		resp.ReceiveStatus = 2
		return nil, err
	}
	return
}

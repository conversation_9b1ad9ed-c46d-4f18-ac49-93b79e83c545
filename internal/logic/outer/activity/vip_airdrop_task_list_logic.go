package activity

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"context"
	"errors"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/consts/activity"
	"gateio_service_marketing_activity/internal/models/vip_airdrop"
	"gateio_service_marketing_activity/internal/pkg/common"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"
	"gorm.io/gorm"
	"net/http"
	"time"
)

type VipAirdropTaskListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewVipAirdropTaskListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *VipAirdropTaskListLogic {
	return &VipAirdropTaskListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *VipAirdropTaskListLogic) VipAirdropTaskList(req *types.VipAirdropTaskListReq, r *http.Request) (resp *types.VipAirdropTaskListResp, err error) {
	u, err := common.GetUserInfo(l.ctx)
	if err != nil || u == nil {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	configModel := vip_airdrop.NewCcVipAirdropConfigModel(l.svcCtx.DBMarketingActivities)
	conf, err := configModel.FindOneBySeason(l.ctx, req.Season)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logx.Errorf("VipAirdropTaskListLogic.VipAirdropTaskList err:%v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}
	taskConfig, err := conf.GetTaskConfig()
	if err != nil {
		logx.Errorf("VipAirdropTaskListLogic.VipAirdropTaskList err:%v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}
	userModel := vip_airdrop.NewCcVipAirdropUserModel(l.svcCtx.DBMarketingActivities)
	applyRecord, err := userModel.FindOneByUidSeason(l.ctx, int64(u.UID), req.Season)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logx.WithContext(l.ctx).Errorf("VipAirdropApplyLogic.VipAirdropTaskList err:%v", err)
		return nil, err
	}
	taskList, taskRecordScheduleMap, _, err := utils.BatchTaskInfo(l.ctx, int64(u.UID), fmt.Sprintf("%d", applyRecord.Id), taskConfig.GetTaskIdStr(), activity.VipAirdropBusinessType, 2)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("VipAirdropApplyLogic.VipAirdropTaskList err:%v", err)
		return nil, err
	}
	resp = &types.VipAirdropTaskListResp{
		List: make([]types.VipAirdropTaskItem, 0),
	}
	taskConfIDMap := taskConfig.TaskIdMap()
	challengeTaskId := taskConfig.GetDailyChallengeTaskId()

	lastRecord, err := vip_airdrop.NewCcVipAirdropTaskRecordModel(l.svcCtx.DBMarketingActivities).
		FindLastRecordByTaskId(l.ctx, int64(u.UID), challengeTaskId, req.Season)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logx.Errorf("VipAirdropApplyLogic.FindLastRecordByTaskId err:%v", err)
	}
	for _, item := range taskList {
		tmp := types.VipAirdropTaskItem{
			TaskId:           int(item.TaskID),
			Status:           int(item.Status),
			HighlightNextDay: false,
		}
		if _, ok := taskConfIDMap[item.TaskID]; ok {
			tmp.AliasName = taskConfIDMap[item.TaskID].AliasName
		}

		if lastRecord != nil && lastRecord.CompletedTime > 0 && challengeTaskId == item.TaskID {
			now := time.Now()
			completed := time.Unix(lastRecord.CompletedTime, 0)
			// 年月日是否一样
			sameDate := now.Year() == completed.Year() && now.Month() == completed.Month() && now.Day() == completed.Day()
			if !sameDate {
				tmp.HighlightNextDay = true
			}
		}

		if len(item.RuleInfo) > 0 {
			if len(item.RuleInfo[0].Conditions) > 0 {
				tmp.PrizeNum = int(item.RuleInfo[0].Conditions[0].PrizeNum)
				for _, detail := range item.RuleInfo[0].Conditions[0].ConditionDetail {
					if detail.Mark == "accumulated_amount" || detail.Mark == "recharge" ||
						detail.Mark == "futures_transaction" || detail.Mark == "memebox_transaction" || detail.Mark == "first_amount" {
						tmp.Min = int(detail.Min)
					}
				}
			}
		}

		if schedule, ok := taskRecordScheduleMap[item.TaskID]; ok {
			tmp.AmountTotal = int(schedule.AmountTotal)
			tmp.CompleteTimes = int(schedule.DoneNum)
		}

		resp.List = append(resp.List, tmp)
	}

	return
}

package activity

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"github.com/spf13/cast"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"
)

type ShareHelpDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewShareHelpDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ShareHelpDetailLogic {
	return &ShareHelpDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// 定义助力状态常量
const (
	HelpStatusOngoing       = 1 // 助力活动进行中
	HelpStatusCompleted     = 2 // 该助力已被完成
	HelpStatusEnded         = 3 // 活动已结束
	HelpStatusAlreadyHelped = 4 // 您已助力过
	HelpStatusYourself      = 5 // 您已助力过
)

func (l *ShareHelpDetailLogic) ShareHelpDetail(req *types.ShareHelpRecordReq, r *http.Request) (resp *types.ShareHelpDetailResp, err error) {
	defer func() {
		reqJson, _ := json.Marshal(req)
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("ShareHelpDetail Req: ", string(reqJson), " resp: ", string(respJson), " err: ", err)
	}()

	// 验证参数
	shareHelpRecord := NewShareHelpRecordLogic(l.ctx, l.svcCtx)
	if err := shareHelpRecord.ShareHelpRecordValidate(req, "detail"); err != nil {
		return nil, err
	}

	// 助力状态：1（助力活动进行中）、2（该助力已被完成）、3（活动已结束）、4（您已助力过）、5（本人助力）
	status := HelpStatusOngoing // 助力活动进行中

	// 查询助力用户的头像
	userUnionId, userAvatar, err := shareHelpRecord.GetUserAvatarInfo(req.InviteUid, req.InviteWalletAddress)
	if err != nil {
		logx.Info("ShareHelpDetail GetUserAvatarInfo Err: ", err, " 请求参数: ", req)
		return resp, errors.New(consts.ErrDbError, "操作失败，请稍后重试")
	}

	// 活动信息
	activityInfo, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftActivity(l.ctx, req.ActivityId, req.Uid > 0)
	if err != nil {
		logx.Infof("ShareHelpDetail GetNftActivity err:%v", err)
		return resp, errors.New(consts.ErrDbError, "操作失败，请稍后重试")
	}
	if activityInfo.TaskEndTime.Before(time.Now()) {
		status = HelpStatusEnded // 活动已结束
	}

	if status == HelpStatusOngoing {
		// 查询邀请人的子任务数据
		subTaskList, err := shareHelpRecord.GetSubTaskList(req)
		if err != nil {
			return nil, err
		}

		// 验证助力状态
		isHelped, err := shareHelpRecord.HasUserHelped(req, subTaskList)
		if isHelped {
			// 该助力已经完成
			status = HelpStatusCompleted
		} else if err == ErrNftActivityRepeatHelp {
			// 已助力过该用户
			status = HelpStatusAlreadyHelped
		} else if err != nil {
			return nil, err
		}
	}
	if (req.Uid > 0 && req.Uid == req.InviteUid) || (req.WalletAddress != "" && req.WalletAddress == req.InviteWalletAddress) {
		status = HelpStatusYourself
	}
	// 返回详情数据
	return &types.ShareHelpDetailResp{
		TimeLeft:  cast.ToInt64(time.Until(activityInfo.TaskEndTime).Seconds()),
		UserId:    utils.AbbreviateString(userUnionId),
		AvatarUrl: userAvatar,
		Status:    cast.ToInt64(status),
	}, nil
}

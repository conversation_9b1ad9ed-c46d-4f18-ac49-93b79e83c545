package activity

import (
	"context"
	"encoding/json"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/nft_detail"
	"gateio_service_marketing_activity/internal/pkg/nft"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/utils"
	"net/http"
	"sort"
	"time"

	gd "github.com/shopspring/decimal"
	"github.com/spf13/cast"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type NftDetailListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// NFT列表查询
func NewNftDetailListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NftDetailListLogic {
	return &NftDetailListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NftDetailListLogic) NftDetailList(req *types.ActivityNftListReq, r *http.Request) (resp *types.ActivityNftListResp, err error) {
	// 验证用户
	if req.UID > 0 {
		userInfo := requestools.GetUserInfo(r)

		// 未登录时，设置用户ID为0，表示未登录
		if userInfo == nil {
			req.UID = 0
		} else if int64(userInfo.UID) != req.UID {
			// 已登陆时，必须与参数一致
			return nil, errors.New(consts.ErrUserLogin, "请先登录")
		}
		if req.UID > 0 {
			//获取账户信息，用于验证是否是子账户
			userMap, _ := service.NewUserCenterCall(l.ctx).GetUserMap([]int64{req.UID})
			if user, exists := userMap[req.UID]; exists && user != nil && user.IsSub == 1 {
				logx.Infof("NftDetailList sub user use :%d, is_sub val is:%d", req.UID, userMap[req.UID].IsSub)
				return nil, errors.New(consts.NFT_SUB_ACCOUNT_NO_ALLOW, "子账号不能参与活动")
			}
		}
	}
	reqByte, _ := json.Marshal(req)
	if req.ActivityID <= 0 {
		logx.Infof("NftDetailList req invalid parameter: %s", string(reqByte))
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
	}

	// 查询活动
	activity, err := nft.GetNftActivity(l.svcCtx, l.ctx, req.ActivityID, req.UID > 0)
	//activity, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftActivity(l.ctx, req.ActivityID)
	if err != nil {
		logx.Infof("NftDetailList GetNftActivity  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}

	//判断是否在领取期
	receiveNow := false
	if activity.ReceiveStartTime.Before(time.Now()) && activity.ReceiveEndTime.After(time.Now()) {
		receiveNow = true
	}

	//判断是否在额外活动期
	taskNow := false
	if activity.TaskStartTime.Before(time.Now()) && activity.TaskEndTime.After(time.Now()) {
		taskNow = true
	}

	// 查询NFT列表
	nftList, err := nft.GetNftList(l.svcCtx, l.ctx, req.ActivityID)
	//nftList, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftList(l.ctx, req.ActivityID, 0, 0)
	if err != nil {
		logx.Infof("NftDetailList GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}

	// 查询铸造日志
	mintNftLogs, err := nft.GetNftMintLogList(l.svcCtx, l.ctx, req.UID, req.WalletAddress, req.ActivityID)
	//mintNftLogs, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftMintLogList(l.ctx, req.UID, req.WalletAddress, 0, 0, req.ActivityID, 0)
	if err != nil {
		logx.Infof("NftDetailList GetNftMintLogList  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	taskStatus := 1
	if activity.TaskStartTime.Before(time.Now()) && activity.TaskEndTime.After(time.Now()) {
		taskStatus = 2 //进行中
	}
	if activity.TaskEndTime.Before(time.Now()) {
		taskStatus = 4 //已结束
	}

	// 格式化数据
	//获取全部普通nft ID
	basicParentIdNftIds := make([]int64, 0)
	//定义活动日信息
	campaignDayMap := make(map[int64]*types.CampaignDay)
	//定义活动期NFT信息
	campaignDayNftMap := make(map[int64][]*nft_detail.NftDetail)
	// NFT数据Map
	nftDetailMap := make(map[int64]*nft_detail.NftDetail)
	for _, detail := range nftList {
		nftDetailMap[detail.NftId] = detail
		if detail.NftType == 1 {
			basicParentIdNftIds = append(basicParentIdNftIds, detail.ParentNftId)
		}
		if _, ok := campaignDayMap[detail.CampaignDay]; !ok {
			logx.Info("NftDetailList nft list time is:", detail.CampaignStartTime.Format(consts.GoFormatTimeStr),
				detail.CampaignEndTime.Format(consts.GoFormatTimeStr), time.Now().Format(consts.GoFormatTimeStr), detail.CampaignEndTime.Before(time.Now()))
			//活动状态 1:未开始 2:进行中 3:已错过
			campaignDayStatus := 1
			if detail.CampaignStartTime.Before(time.Now()) && detail.CampaignEndTime.After(time.Now()) {
				campaignDayStatus = 2
			}
			if detail.CampaignEndTime.Before(time.Now()) {
				campaignDayStatus = 3
			}
			startTimeLeft := cast.ToInt64(time.Until(detail.CampaignStartTime).Seconds())
			if startTimeLeft < 0 {
				startTimeLeft = 0
			}
			endTimeLeft := cast.ToInt64(time.Until(detail.CampaignEndTime).Seconds())
			if endTimeLeft < 0 {
				endTimeLeft = 0
			}
			campaignDayMap[detail.CampaignDay] = &types.CampaignDay{
				CampaignDay:           int(detail.CampaignDay),
				CampaignDayStatus:     campaignDayStatus,
				CampaignStartTime:     detail.CampaignStartTime.Format(consts.GoFormatTimeStr),
				CampaignEndTime:       detail.CampaignEndTime.Format(consts.GoFormatTimeStr),
				CampaignStartTimeLeft: startTimeLeft,
				CampaignEndTimeLeft:   endTimeLeft,
			}
		}
		if campaignDayNftMap[detail.CampaignDay] == nil {
			campaignDayNftMap[detail.CampaignDay] = []*nft_detail.NftDetail{}
		}
		//因为黄金和白银等级只展示一个，所以直接过滤掉黄金等级
		if detail.NftType == 1 || detail.NftType == 2 {
			campaignDayNftMap[detail.CampaignDay] = append(campaignDayNftMap[detail.CampaignDay], detail)
		}
	}

	//获取全部已经领取的parentNftID
	mintParentNftMap := map[int64]struct{}{}
	mintParentNftIds := []int64{}
	//获取用户普通nft铸造中信息
	basicMintingNftMap := map[int64]int64{}
	//获取用户黄金白银nft铸造中信息
	levelMintingNftMap := map[int64]int64{}
	//定义完成铸造的nft信息
	finMintNftMap := map[int64]int64{}
	for _, logNft := range mintNftLogs {
		noPrice := gd.NewFromFloat(0)
		//用免费来判断领取个数
		if _, ok := mintParentNftMap[logNft.ParentNftId]; !ok && logNft.MintStatus == 3 && logNft.Expenditure.Equal(noPrice) {
			mintParentNftMap[logNft.ParentNftId] = struct{}{}
			mintParentNftIds = append(mintParentNftIds, logNft.ParentNftId)
		}
		if logNft.NftUserSubTaskId <= 0 && logNft.MintStatus < 3 && logNft.NftId > 0 {
			basicMintingNftMap[logNft.NftId] = logNft.OperationId
		}
		nftDetail, exists := nftDetailMap[logNft.NftId]
		if logNft.NftUserSubTaskId <= 0 && logNft.MintStatus < 3 && (logNft.NftId <= 0 || (exists && nft_detail.IsSenior(nftDetail.NftType))) {
			levelMintingNftMap[logNft.ParentNftId] = logNft.OperationId
		}
		//过滤掉做任务免费领取的铸造记录，不过滤的化会影响额外活动期普通nft的购买
		if logNft.NftUserSubTaskId <= 0 && logNft.MintStatus == 3 {
			finMintNftMap[logNft.NftId] = logNft.MintStatus
		}
	}
	//获取已领取的普通nft数量
	mintedParentNftIds := utils.Intersection(basicParentIdNftIds, mintParentNftIds)
	if len(mintedParentNftIds) == len(basicParentIdNftIds) {
		taskStatus = 3 //已经领取完
	}
	//整合活动期信息
	campaignDayList := make([]*types.CampaignDay, len(campaignDayMap))
	campaignDayIndex := 0
	for campaignDay, campaignDayInfo := range campaignDayMap {
		typeNftList := campaignDayNftMap[campaignDay]
		//正序排序 按照nft id排序
		sort.Slice(typeNftList, func(i, j int) bool {
			return typeNftList[i].NftId < typeNftList[j].NftId
		})
		//正序排序 按照nft id排序
		sort.Slice(typeNftList, func(i, j int) bool {
			return typeNftList[i].NftType < typeNftList[j].NftType
		})
		respNftList := make([]*types.NftInfo, len(typeNftList))
		for i, nftInfo := range typeNftList {
			nftId := nftInfo.NftId
			operationID := int64(0)
			if nftInfo.NftType == 2 {
				nftId = 0
			}
			//领取期普通nft免费
			nftPrice := nftInfo.CenNftPrice.InexactFloat64()
			//req.IsCentralization == 2 请求来源是去中心化请求
			if req.WalletAddress != "" || req.IsCentralization == 2 {
				nftPrice = nftInfo.DecNftPrice.InexactFloat64()
			}
			if receiveNow && nftInfo.NftType == 1 {
				nftPrice = 0
			}
			// 活动状态 1:可领取/铸造 2:未到活动期 3:已过期 4:已领取 5:铸造中(按钮不可操作) 6:额外活动期普通 Nft 可购买
			campaignNftStatus := 1
			//如果当前是领取期并且铸造状态是已完成
			if receiveNow && finMintNftMap[nftId] == 3 && nftInfo.NftType == 1 {
				campaignNftStatus = 4
			}
			if nftInfo.CampaignStartTime.After(time.Now()) {
				campaignNftStatus = 2
			}
			if nftInfo.CampaignEndTime.Before(time.Now()) {
				campaignNftStatus = 3
			}
			//如果当前是任务期并且是普通nft
			if taskNow && nftInfo.NftType == 1 {
				campaignNftStatus = 6
			}
			//因为三个等级的nft的上级ID是一个，为了避免铸造中状态混淆，需要区分具体是那个类型的nft正在铸造
			if _, ok := basicMintingNftMap[nftInfo.NftId]; ok && nftInfo.NftType == 1 {
				campaignNftStatus = 5
				operationID = basicMintingNftMap[nftInfo.NftId]
			}
			if _, ok := levelMintingNftMap[nftInfo.ParentNftId]; ok && (nftInfo.NftType == nft_detail.NftTypeSilver || nftInfo.NftType == nft_detail.NftTypeGold) {
				campaignNftStatus = 5
				operationID = levelMintingNftMap[nftInfo.ParentNftId]
			}
			startTimeLeft := cast.ToInt64(time.Until(nftInfo.CampaignStartTime).Seconds())
			endTimeLeft := cast.ToInt64(time.Until(nftInfo.CampaignEndTime).Seconds())
			if taskNow && nftInfo.NftType == 1 {
				startTimeLeft = cast.ToInt64(time.Until(activity.TaskStartTime).Seconds())
				endTimeLeft = cast.ToInt64(time.Until(activity.TaskEndTime).Seconds())

			}
			if startTimeLeft < 0 {
				startTimeLeft = 0
			}
			if endTimeLeft < 0 {
				endTimeLeft = 0
			}
			respNftList[i] = &types.NftInfo{
				ID:                    nftInfo.Id,
				NftID:                 nftId,
				ParentNftID:           nftInfo.ParentNftId,
				NftName:               nftInfo.NftName,
				NftType:               int(nftInfo.NftType),
				CampaignDay:           int(nftInfo.CampaignDay),
				NftIconURL:            nftInfo.NftIconUrl,
				NftGifURL:             nftInfo.NftGifUrl,
				NftPrice:              nftPrice,
				CampaignNftStatus:     campaignNftStatus,
				CampaignStartTimeLeft: startTimeLeft,
				CampaignEndTimeLeft:   endTimeLeft,
				OperationID:           operationID,
			}

		}
		campaignDayInfo.NftList = respNftList
		campaignDayList[campaignDayIndex] = campaignDayInfo
		campaignDayIndex++
	}
	//正序排序
	sort.Slice(campaignDayList, func(i, j int) bool {
		return campaignDayList[i].CampaignDay < campaignDayList[j].CampaignDay
	})

	resp = &types.ActivityNftListResp{
		ActivityID:           req.ActivityID,
		TaskStatus:           taskStatus,
		AllBasicNftCount:     len(basicParentIdNftIds),
		CollectBasicNftCount: len(mintedParentNftIds),
		List:                 campaignDayList,
	}
	return
}

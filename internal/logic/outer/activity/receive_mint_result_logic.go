package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"net/http"
	"strconv"
	"strings"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type ReceiveMintResultLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 接收web3 nft铸造结果
func NewReceiveMintResultLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReceiveMintResultLogic {
	return &ReceiveMintResultLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReceiveMintResultLogic) ReceiveMintResult(req *types.ReceiveMintResultReq, r *http.Request) (resp *types.ReceiveMintResultResp, err error) {
	reqByte, _ := json.Marshal(req)
	if req.ActivityID <= 0 || req.OperationID <= 0 || req.WalletAddress == "" || req.ParentNftID <= 0 ||
		req.MintStatus <= 0 || (len(req.MintResults) == 0 && req.MintStatus == 1) {
		logx.Infof("ReceiveMintResult req invalid parameter: %s", string(reqByte))
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
	}
	defer func() {
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("ReceiveMintResult Req: ", string(reqByte), " resp: ", string(respJson), " err: ", err)
	}()
	for _, result := range req.MintResults {
		if result.TokenURL == "" {
			logx.Infof("ReceiveMintResult req invalid parameter: %s", string(reqByte))
			return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
		}
	}
	resp = &types.ReceiveMintResultResp{}
	//查询铸造对应的记录
	mintNftLogs, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftMintLogList(l.ctx, 0, req.WalletAddress, req.ParentNftID, 0, req.ActivityID, req.OperationID)
	if err != nil {
		logx.Infof("ReceiveMintResult GetNftMintLogList  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	if len(mintNftLogs) == 0 {
		logx.Infof("ReceiveMintResult mintNftLogs  not found, req is:%s", string(reqByte))
		resp.ReceiveStatus = 2
		resp.ReceiveStatusCause = "未找到对应的铸造记录"
		return resp, nil
	}
	for _, mintNftLog := range mintNftLogs {
		if mintNftLog.MintStatus > 2 {
			logx.Infof("ReceiveMintResult result has Received, req is:%s", string(reqByte))
			resp.ReceiveStatus = 1
			resp.ReceiveStatusCause = "已接收到铸造结果"
			return resp, nil
		}
	}
	//nftUserSubTaskID := mintNftLogs[0].NftUserSubTaskId
	//只能通过整个链路来保证回调的信息是正确的
	/*if len(mintNftLogs) != len(req.MintResults) {
		logx.Infof("ReceiveMintResult mintNftLogs len != req.MintResults len, req is:%s,data len is:%d,%d", string(reqByte), len(mintNftLogs), len(req.MintResults))
		resp.ReceiveStatus = 2
		resp.ReceiveStatusCause = "铸造结果数量和记录数量不一致"
		return resp, nil
	}*/
	if req.MintStatus == 1 {
		//只能通过整个链路来保证回调的信息是正确的
		//接收到铸造成功通知
		for i, _ := range mintNftLogs {
			tokenId := ""
			tokenUrl := ""
			nftId := int64(0)
			if len(req.MintResults) > i {
				tokenId = req.MintResults[i].TokenID
				tokenUrl = req.MintResults[i].TokenURL
				urlStrs := strings.Split(tokenUrl, "/")
				nftIdStr := urlStrs[len(urlStrs)-1]
				nftId, _ = strconv.ParseInt(nftIdStr, 10, 64)
			}
			//只能通过整个链路来保证回调的信息是正确的，不能判断数量是否对应上
			/*if nftId == 0 {
				logx.Infof("ReceiveMintResult get nftId form req.MintResults fialed, req is:%s", string(reqByte))
				resp.ReceiveStatus = 2
				resp.ReceiveStatusCause = "从req.MintResults中获取nft id失败"
				return resp, nil
			}*/
			mintNftLogs[i].NftId = nftId
			mintNftLogs[i].TokenUrl = tokenUrl
			mintNftLogs[i].TokenId = tokenId
			mintNftLogs[i].MintStatus = 3
		}
		err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).UpdateNftMintLogs(l.ctx, mintNftLogs)
		if err != nil {
			mintNftLogsByte, _ := json.Marshal(mintNftLogs)
			logx.Infof("ReceiveMintResult UpdateNftMintLogs  is err, update data is:%s,err is:%v", string(mintNftLogsByte), err)
			resp = nil
			err = consts.GetErrorMsg(r, consts.ErrDbError)
		}
	} else {
		//接收到铸造失败通知
		updateMap := map[string]interface{}{}
		updateMap["mint_status"] = 4
		if req.MintStatus == 2 {
			updateMap["mint_status_cause"] = "上链失败"
		}
		if req.MintStatus == 3 {
			updateMap["mint_status_cause"] = "非铸造交易"
		}
		if req.MintStatus == 4 {
			updateMap["mint_status_cause"] = "钱包地址与链上不一致"
		}
		if req.MintStatus == 5 {
			updateMap["mint_status_cause"] = "ABI解析方法失败"
		}
		if req.MintStatus == 6 {
			updateMap["mint_status_cause"] = "上链广播失败"
		}
		if req.MintStatus == 7 {
			updateMap["mint_status_cause"] = "铸造排队超时"
		}
		err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).UpdateNftMintLogMintStatus(l.ctx, req.WalletAddress, 0, req.ParentNftID, req.OperationID, req.ActivityID, updateMap)
		if err != nil {
			logx.Infof("ReceiveMintResult UpdateNftMintLogMintStatus  is err, req is:%s,updatemap is:%v,err is:%v", string(reqByte), updateMap, err)
			resp = nil
			err = consts.GetErrorMsg(r, consts.ErrDbError)
		}
		logx.Infof("ReceiveMintResult result is faild: %s", string(reqByte))
	}
	//删除去中心化铸造记录的缓存
	mintLogKey := fmt.Sprintf(consts.NftWalletAddressMintLogKey, req.WalletAddress, req.ActivityID)
	//删除轮询nft铸造结果的缓存
	rollMintNfrKey := fmt.Sprintf(consts.NftRollMintLogKey, req.ActivityID, 0, req.WalletAddress, req.OperationID)
	_, _ = l.svcCtx.Redis.Del(mintLogKey, rollMintNfrKey)
	resp.ReceiveStatus = 1
	return
}

package activity

import (
	"bitbucket.org/gateio/gateio-lib-base-go/environment"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/pkg/nft"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"github.com/spf13/cast"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type NftActivityListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 活动列表查询
func NewNftActivityListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NftActivityListLogic {
	return &NftActivityListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NftActivityListLogic) NftActivityList(req *types.ActivityListReq, r *http.Request) (resp *types.ActivityListResp, err error) {
	reqByte, _ := json.Marshal(req)
	defer func() {
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("NftActivityList Req: ", string(reqByte), " resp: ", string(respJson), " err: ", err)
	}()

	//默认查询红牛活动
	activityList := []*nft_activity.NftActivity{}
	redisKey := fmt.Sprintf(consts.NftBaseTypeActivityListKey, req.ActivityType, cast.ToInt(req.IsCentralized))
	if environment.IsPre() {
		redisKey = redisKey + "_:pre"
	}
	redisValue, _ := l.svcCtx.Redis.Get(redisKey)
	if redisValue == "" {
		activityList, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftActivityList(l.ctx, 0, req.ActivityType, req.IsCentralized == 1)
		if err != nil {
			logx.Infof("da0 GetNftActivityList is err : %v", err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		activityListByte, err := json.Marshal(activityList)
		if err == nil {
			err = l.svcCtx.Redis.Setex(redisKey, string(activityListByte), 30)
			if err != nil {
				logx.Info("NftActivityList Redis.Setex is err:", redisKey, err.Error())
			}
		}
	} else {
		_ = json.Unmarshal([]byte(redisValue), &activityList)
	}
	list := nft.NftActivityToResp(activityList)
	resp = &types.ActivityListResp{
		List: list,
	}
	return
}

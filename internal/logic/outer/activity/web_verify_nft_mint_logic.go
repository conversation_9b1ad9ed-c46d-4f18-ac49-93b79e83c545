package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/models/nft_detail"
	"gateio_service_marketing_activity/internal/models/nft_mint_log"
	"gateio_service_marketing_activity/internal/utils"
	"net/http"
	"time"

	gd "github.com/shopspring/decimal"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type WebVerifyNftMintLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// web3后端 NFT铸造验证
func NewWebVerifyNftMintLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WebVerifyNftMintLogic {
	return &WebVerifyNftMintLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WebVerifyNftMintLogic) WebVerifyNftMint(req *types.WebVerifyNftMintReq, r *http.Request) (resp *types.WebVerifyNftMintResp, err error) {
	reqByte, _ := json.Marshal(req)
	if req.ActivityID <= 0 || req.OperationID <= 0 || req.WalletAddress == "" || req.ParentNftID <= 0 {
		logx.Infof("WebVerifyNftMint req invalid parameter: %s", string(reqByte))
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
	}
	defer func() {
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("WebVerifyNftMint Req: ", string(reqByte), " resp: ", string(respJson), " err: ", err)
	}()
	resp = &types.WebVerifyNftMintResp{
		MintStatus: 1,
	}
	defer func() {
		updateMap := map[string]interface{}{}
		if resp.MintStatus == 2 {
			//状态设置成失败
			//updateMap["mint_status"] = 4
			//updateMap["mint_status_cause"] = "web3 verify failed:" + resp.MintStatusCause
		}
		if resp.MintStatus == 1 {
			//状态设置成进行中
			updateMap["mint_status"] = 2
			err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).UpdateNftMintLogMintStatus(l.ctx, req.WalletAddress, 0, req.ParentNftID, req.OperationID, req.ActivityID, updateMap)
			if err != nil {
				logx.Infof("WebVerifyNftMint UpdateNftMintLogMintStatus  is err, req is:%s,updatemap is:%v,err is:%v", string(reqByte), updateMap, err)
				resp = nil
				err = consts.GetErrorMsg(r, consts.ErrDbError)
			}
		}

		//删除去中心化铸造记录的缓存
		redisKey := fmt.Sprintf(consts.NftWalletAddressMintLogKey, req.WalletAddress, req.ActivityID)
		_, _ = l.svcCtx.Redis.Del(redisKey)
		respJson, _ := json.Marshal(resp)
		logx.Info("WebVerifyNftMint Req: ", string(reqByte), " resp: ", string(respJson), " err: ", err)
	}()
	//活动数据库数据验证
	activity, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftActivity(l.ctx, req.ActivityID, false)
	if err != nil {
		logx.Infof("WebVerifyNftMint GetNftActivity  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	if activity.ActivityId <= 0 {
		logx.Infof("WebVerifyNftMint activity is null, req is:%s", string(reqByte))
		resp.MintStatus = 2
		resp.MintStatusCause = "活动不存在"
		return resp, nil
	}
	//判断活动是否结束
	if activity.EndTime.Before(time.Now()) {
		logx.Infof("WebVerifyNftMint activity end, activity.EndTime is:%s", activity.EndTime.Format(consts.GoFormatTimeStr))
		resp.MintStatus = 2
		resp.MintStatusCause = "活动已结束"
		return resp, nil
	}
	//判断活动是否开始
	if activity.StartTime.After(time.Now()) {
		logx.Infof("WebVerifyNftMint activity not start, activity.StartTime is:%s", activity.StartTime.Format(consts.GoFormatTimeStr))
		resp.MintStatus = 2
		resp.MintStatusCause = "活动未开始"
		return resp, nil
	}
	//判断是否在领取期
	receiveNow := false
	if activity.ReceiveStartTime.Before(time.Now()) && activity.ReceiveEndTime.After(time.Now()) {
		receiveNow = true
	}
	//判断是否在额外活动期
	taskNow := false
	if activity.TaskStartTime.Before(time.Now()) && activity.TaskEndTime.After(time.Now()) {
		taskNow = true
	}
	//查询用户铸造操作记录
	finVerifyNftLogs, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftMintLogList(l.ctx, 0, req.WalletAddress, req.ParentNftID, 0, req.ActivityID, req.OperationID)
	if err != nil {
		logx.Infof("WebVerifyNftMint GetNftMintLogList  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	opNftId := int64(0)
	//定义初始化状态的铸造记录
	initMintNftLogs := []*nft_mint_log.NftMintLog{}
	//刨除本次操作以外的相同nft铸造记录
	//difOperationMintLogs := []*nft_mint_log.NftMintLog{}
	//完成验证的记录
	isHighGrade := 0
	nftType := 1
	finLogIdMap := map[int64]struct{}{}
	for _, log := range finVerifyNftLogs {
		if log.MintStatus == 2 {
			logx.Infof("WebVerifyNftMint mintNftLogs has verifyed, req is:%s", string(reqByte))
			resp.MintStatus = 1
			resp.MintStatusCause = "已经验证通过"
			return resp, nil
		}
		if log.ParentNftId == req.ParentNftID && log.OperationId == req.OperationID && log.MintStatus == 1 {
			initMintNftLogs = append(initMintNftLogs, log)
			//判断当前铸造是否是铸造的高等级nft
			if log.NftId <= 0 {
				isHighGrade = 1
				nftType = 0
			}
		}
		finLogIdMap[log.Id] = struct{}{}
		opNftId = log.NftId
	}
	//查询用户所有铸造记录
	allMintNftLogs, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftMintLogList(l.ctx, 0, req.WalletAddress, 0, 1, req.ActivityID, 0)
	if err != nil {
		logx.Infof("WebVerifyNftMint GetNftMintLogList  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}

	//定义和本次操作铸造相同的普通nft的排除本次操作的领取记录
	basicMintNftLogs := []*nft_mint_log.NftMintLog{}
	//定义和本次操作铸造相同的高级nft的排除本次操作的领取记录
	levelMintNftLogs := []*nft_mint_log.NftMintLog{}
	for _, log := range allMintNftLogs {
		if _, ok := finLogIdMap[log.Id]; !ok && log.NftId == opNftId && log.NftId > 0 && log.ParentNftId == req.ParentNftID {
			basicMintNftLogs = append(basicMintNftLogs, log)
		}
		if _, ok := finLogIdMap[log.Id]; !ok && log.NftId <= 0 && log.ParentNftId == req.ParentNftID {
			levelMintNftLogs = append(levelMintNftLogs, log)
		}
	}

	if len(initMintNftLogs) == 0 {
		logx.Infof("WebVerifyNftMint mintNftLogs  not found init status log, req is:%s", string(reqByte))
		resp.MintStatus = 2
		resp.MintStatusCause = "未找到初始化铸造记录"
		return resp, nil
	}
	//获取nft列表
	nftList := make([]*nft_detail.NftDetail, 0)
	//获取nft列表 领取和购买场景
	if req.ParentNftID > 0 && initMintNftLogs[0].NftUserSubTaskId <= 0 {
		nftList, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftList(l.ctx, req.ActivityID, req.ParentNftID, nftType, isHighGrade)
		if err != nil {
			logx.Infof("WebVerifyNftMint GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
	}
	//获取nft列表 完成任务铸造nft场景
	if initMintNftLogs[0].NftUserSubTaskId > 0 {
		nftList, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftList(l.ctx, req.ActivityID, 0, 1, 0)
		if err != nil {
			logx.Infof("WebVerifyNftMint GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		subTaskDataModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)
		subTask, err := subTaskDataModel.GetSubTaskByID(l.ctx, initMintNftLogs[0].NftUserSubTaskId)
		if err != nil {
			logx.Infof("WebVerifyNftMint GetSubTaskByID  is err, req is:%s,err is:%v", string(reqByte), err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		//任务未完成
		if subTask.Status != 2 {
			logx.Infof("WebVerifyNftMint subTask not finish, data is:%d,%d", subTask.TaskId, subTask.Status)
			resp.MintStatus = 2
			resp.MintStatusCause = "任务未完成"
			return resp, nil
		}
	}
	if len(nftList) == 0 {
		logx.Infof("WebVerifyNftMint nftList  is null, req is:%s", string(reqByte))
		resp.MintStatus = 2
		resp.MintStatusCause = "未找到对应NFT"
		return resp, nil
	}

	//免费领取期
	if receiveNow {
		nftType := int64(0)
		for _, nftInfo := range nftList {
			nftType = nftInfo.NftType
			//判断当前parentNftID是否在领取期
			if nftInfo.CampaignStartTime.After(time.Now()) || nftInfo.CampaignEndTime.Before(time.Now()) {
				//当前parentNftID不在领取期
				paramByte, _ := json.Marshal(nftInfo)
				logx.Infof("WebVerifyNftMint nft not in recevie time, params is:%s", string(paramByte))
				resp.MintStatus = 2
				resp.MintStatusCause = "parentNftId 不在领取期"
				return resp, nil
			}
		}
		//同一个普通nft 在可领取期只能领取一次
		if nftType == 1 {
			if len(basicMintNftLogs) > 0 {
				logx.Infof("WebVerifyNftMint mintNftLogs len > 0, params is:%d", len(basicMintNftLogs))
				resp.MintStatus = 2
				resp.MintStatusCause = "parentNftId已有领取记录"
				return resp, nil
			}
		} else {
			for _, mintNftLog := range levelMintNftLogs {
				//同一个用的同一个nft如果有铸造中的状态也要拦截
				if mintNftLog.MintStatus < 3 {
					logx.Infof("WebVerifyNftMint same nft minting data is:%d,%d", mintNftLog.NftId, mintNftLog.MintStatus)
					resp.MintStatus = 2
					resp.MintStatusCause = "same nft in minting"
					return resp, nil
				}
			}
		}
	}
	//额外活动期
	if taskNow {
		nftType := int64(0)
		for _, nftInfo := range nftList {
			if nftInfo.ParentNftId == req.ParentNftID {
				nftType = nftInfo.NftType
			}
		}
		if nftType == 0 {
			logx.Infof("WebVerifyNftMint parent_nft_id is err:%d", req.ParentNftID)
			resp.MintStatus = 2
			resp.MintStatusCause = "上级ID不存在"
			return resp, nil
		}
		if nftType != nft_detail.NftTypeNormal {
			logx.Infof("WebVerifyNftMint task time need basic nft")
			resp.MintStatus = 2
			resp.MintStatusCause = "额外活动期只能购买普通nft"
			return resp, nil
		}
		if initMintNftLogs[0].NftUserSubTaskId <= 0 {
			//从刨除本次操作以外的相同nft铸造记录中验证，如果存在铸造中的便证明重复
			for _, mintNftLog := range basicMintNftLogs {
				//检查同一个人的同一个nft是否在铸造中 排除本次操作记录
				if mintNftLog.NftUserSubTaskId <= 0 && mintNftLog.ParentNftId == req.ParentNftID && mintNftLog.MintStatus < 3 {
					logx.Infof("WebVerifyNftMint same nft minting data is:%d,%d", mintNftLog.NftId, mintNftLog.MintStatus)
					resp.MintStatus = 2
					resp.MintStatusCause = "same nft in minting"
					return resp, nil
				}
			}
		} else {
			logParentNftIdMap := map[int64]struct{}{}
			logParentNftIds := []int64{}
			for _, log := range allMintNftLogs {
				//增加任务免费领取只能同一时刻只能有一条记录的限制 排除当前操作的初始化状态
				if log.NftUserSubTaskId > 0 && log.MintStatus < 3 && log.OperationId != req.OperationID {
					//额外活动期有正在免费铸造的nft
					logx.Infof("WebVerifyNftMint Please do not claim until you have completed the last collection minting")
					resp.MintStatus = 2
					resp.MintStatusCause = "Please do not claim until you have completed the last collection minting"
					return resp, nil
				}
				if log.NftUserSubTaskId == initMintNftLogs[0].NftUserSubTaskId && log.MintStatus == 3 {
					//做任务免费领取一个nft只能领取一次
					logx.Infof("WebVerifyNftMint task nft has minted")
					resp.MintStatus = 2
					resp.MintStatusCause = "task nft has minted"
					return resp, nil
				}
				noPrice := gd.NewFromFloat(0)
				//排除本次操作
				if _, ok := logParentNftIdMap[log.ParentNftId]; !ok && log.Expenditure.Equal(noPrice) && log.OperationId != req.OperationID {
					logParentNftIds = append(logParentNftIds, log.ParentNftId)
					logParentNftIdMap[log.ParentNftId] = struct{}{}
				}
			}
			//可以领取的nft
			canReceiveParentNftIds := make([]int64, len(nftList))
			for i, nftInfo := range nftList {
				canReceiveParentNftIds[i] = nftInfo.ParentNftId
			}
			//获取未领取的nft
			parentNftIds := utils.FindUniqueElements(canReceiveParentNftIds, logParentNftIds)
			if len(parentNftIds) == 0 {
				//所有普通nft用户都已领完
				logx.Infof("WebVerifyNftMint all basic nft claimed")
				resp.MintStatus = 2
				resp.MintStatusCause = "所有的普通nft都已产生领取记录"
				return resp, nil
			}
		}
	}

	return
}

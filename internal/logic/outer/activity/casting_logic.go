package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/models/nft_adjust_log"
	"gateio_service_marketing_activity/internal/mqs/kafka"
	"gateio_service_marketing_activity/internal/service"
	"math/rand"
	"net/http"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/decimal"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"github.com/spf13/cast"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/models/nft_detail"
	"gateio_service_marketing_activity/internal/models/nft_mint_log"
	"gateio_service_marketing_activity/internal/pkg/apiengine"
	pkgnft "gateio_service_marketing_activity/internal/pkg/nft"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"
)

type CastingLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCastingLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CastingLogic {
	return &CastingLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

const (
	SceneBuy                        = 1    // 免费、购买场景
	SceneTask                       = 2    // 做任务
	SceneSurprise                   = 3    // 惊喜任务
	ActivityPlatformIdOnline  int64 = 1459 //活动平台生产和预发环境的活动ID
	ActivityPlatformIdOffline int64 = 808  //活动平台测试环境的活动ID
)

func (l *CastingLogic) Casting(req *types.CastingReq, r *http.Request) (resp *types.CastingResp, err error) {
	// 记录请求返回日志
	defer func() {
		reqJson, _ := json.Marshal(req)
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("CastingHandle Req: ", string(reqJson), " resp: ", string(respJson), " err: ", err)
	}()

	// 验证登陆态
	userInfo, err := utils.GetUserInfo(l.ctx)
	if err != nil {
		logx.Infof("CastingHandle GetUserInfo is err:%v ", err)
	}
	if userInfo == nil || userInfo.UID == 0 {
		return nil, errors.New(consts.ErrUserLogin, "请先登录")
	} else {
		//获取账户信息，用于验证是否是子账户
		userMap, _ := service.NewUserCenterCall(l.ctx).GetUserMap([]int64{int64(userInfo.UID)})
		if user, exists := userMap[int64(userInfo.UID)]; exists && user != nil && user.IsSub == 1 {
			logx.Infof("Casting sub user use :%d, is_sub val is:%d", userInfo.UID, userMap[int64(userInfo.UID)].IsSub)
			return nil, errors.New(consts.NFT_SUB_ACCOUNT_NO_ALLOW, "子账号不能参与活动")
		}
	}

	// 上报打点，可以接受失败，而且就算错误也不能影响主流程
	go func() {
		goFuncCtx, cancel := context.WithCancel(context.Background())
		defer func() {
			cancel()
			if rec := recover(); rec != nil {
				logx.WithContext(goFuncCtx).Errorf("Casting [Participate]uploadActivityRecord Recover err: %v", rec)
			}
		}()
		//增加上报归因数据
		userAdjust, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetUserAdjust(goFuncCtx, userInfo.UID)
		if err != nil {
			logx.Infof("Casting GetUserAdjust is err:%v,user id is:%d", err, userInfo.UID)
			return
		}
		if userAdjust.Id == 0 {
			uploadAID := ActivityPlatformIdOnline
			fromChannel := consts.NftFromChannelOnline
			if utils.CheckDev() {
				uploadAID = ActivityPlatformIdOffline
				fromChannel = consts.NftFromChannelOffLine
			}
			logx.WithContext(goFuncCtx).Infof("Casting [Participate]uploadActivityRecord uid:%v, uploadAID: %v, fromChannel:%v", userInfo.UID, uploadAID, fromChannel)
			err = l.uploadActivityRecord(int64(userInfo.UID), uploadAID, fromChannel)
			if err != nil {
				logx.Infof("Casting uploadActivityRecord is err:%v,params is:%d", err, userInfo.UID)
				return
			}
			userAdjust = &nft_adjust_log.NftAdjustLog{
				Uid:          int64(userInfo.UID),
				ActivityType: int64(1),
			}
			err = nft_adjust_log.NewNftAdjustLogModel(l.svcCtx.DBMarketingActivities).Insert(goFuncCtx, userAdjust)
			if err != nil {
				logx.Infof("Casting Insert userAdjust is err:%v,params is:%d", err, userInfo.UID)
				return
			}
		}
	}()

	req.UserId = int64(userInfo.UID)
	// 处理请求
	resp, err = l.CastingHandle(req)
	if err != nil {
		return nil, consts.ErrFormat(r, err, cast.ToString(req.OrderId))
	}
	return resp, nil
}

func (l *CastingLogic) uploadActivityRecord(uid, aid int64, channel string) error {
	kafkaProducer, err := kafka.NewKafkaProducer(l.svcCtx.UploadKafkaConf)
	if err != nil {
		logx.Infof("Casting [uploadActivityRecord]NewKafkaProducer err: %v", err)
		return err
	}
	defer kafkaProducer.Close()
	err = kafkaProducer.AdjustUserRecordProducer(context.Background(), uid, aid, time.Now().Unix(), channel)
	if err != nil {
		logx.Infof("Casting [uploadActivityRecord]AdjustUserRecord err: %v", err)
		return err
	}
	return nil
}

func (l *CastingLogic) CastingHandle(req *types.CastingReq) (resp *types.CastingResp, err error) {
	// 验证参数
	if err := l.CastingValidate(req); err != nil {
		return nil, err
	}

	// 验证是否可以铸造
	var isHighGrade int
	if req.NftType == nft_detail.NftTypeSilver || req.NftType == nft_detail.NftTypeGold {
		isHighGrade = 1
	}
	verifyResp := pkgnft.VerifyMintNft(l.svcCtx, l.ctx, req.ActivityId, req.ParentNftId, req.UserId, "", req.UserSubTaskId, isHighGrade, req.NftId, req.UserId > 0)
	parentNftId, reason, err := verifyResp.ParentNftId, verifyResp.Message, verifyResp.Error
	if err != nil {
		return nil, err
	}
	if verifyResp.TaskStep != pkgnft.ACTIVITY_STEP_SURPRISE && verifyResp.TaskStep != pkgnft.ACTIVITY_STEP_TASK {
		if len(reason) > 0 {
			return nil, errors.New(consts.NFT_ACTIVITY_COMMON_ERROR, reason)
		}
		if parentNftId == 0 {
			return nil, errors.New(consts.NFT_ACTIVITY_CAN_NOT_CASTING, "没有可以铸造的NFT")
		}
	}

	// 铸造处理
	var rspNftList []*types.NftItem
	if req.Scene == SceneBuy {
		// 免费、购买场景
		rspNftList, err = l.CastingBuy(req, verifyResp)
		if err != nil {
			return nil, err
		}
	} else if req.Scene == SceneTask {
		// 做任务
		rspNftList, err = l.CastingTask(req)
		if err != nil {
			return nil, err
		}
	} else if req.Scene == SceneSurprise {
		// 惊喜任务
		rspNftList, err = l.CastingSurprise(req)
		if err != nil {
			return nil, err
		}
	}

	// 删除缓存
	l.deleteCache(req, req.OrderId)

	// 返回结果
	return &types.CastingResp{
		NftList: rspNftList,
		OrderId: req.OrderId,
	}, nil
}

/**
 * 删除缓存
 */
func (l *CastingLogic) deleteCache(req *types.CastingReq, orderId int64) {
	// 删除轮询nft铸造结果的缓存
	rollMintNfrKey := fmt.Sprintf(consts.NftRollMintLogKey, req.ActivityId, req.UserId, "", orderId)
	delNum, err := l.svcCtx.Redis.Del(rollMintNfrKey)
	logx.Info("CastingLogic deleteCache keys：", rollMintNfrKey, " delNum:", delNum, " err:", err)
}

/**
 * 验证参数
 */
func (l *CastingLogic) CastingValidate(req *types.CastingReq) (err error) {
	// 生成订单ID，用于辨别是否一个批次购买
	orderId, err := utils.GetOrderId(l.ctx, l.svcCtx)
	if err != nil {
		return errors.New(consts.ErrRedisError, "生成订单ID失败，请稍后重试")
	}
	req.OrderId = orderId

	// 场景ID参数不能为空
	if req.Scene == 0 {
		return errors.New(consts.ErrInvalidParam, "scene参数不能为空")
	}

	// 免费和购买场景，nft_id不能为空
	if req.Scene == SceneBuy && req.ParentNftId == 0 {
		return errors.New(consts.ErrInvalidParam, "parent_nft_id参数不能为空")
	}

	// 任务场景，任务ID不能为空
	if (req.Scene == SceneTask || req.Scene == SceneSurprise) && req.UserSubTaskId == 0 {
		return errors.New(consts.ErrInvalidParam, "user_sub_task_id参数不能为空")
	}

	// 频率限制
	rateLimitKey := fmt.Sprintf(consts.NftCastingRateLimits, req.UserId)
	setOk, err := l.svcCtx.Redis.SetnxExCtx(l.ctx, rateLimitKey, "1", 2)
	if err != nil {
		logx.Info("CastingLogic CastingHandle SetnxExCtx error", err)
	}
	if !setOk {
		return errors.New(consts.ManyAttempts, "操作过于频繁，请稍后重试")
	}

	// NFT类型
	if req.Scene == SceneBuy && req.NftType == 0 {
		return errors.New(consts.ErrInvalidParam, "nft_type参数不能为空")
	}
	return
}

/**
 * 免费、购买场景
 */
func (l *CastingLogic) CastingBuy(req *types.CastingReq, verifyResp pkgnft.MintNftResponse) (rspNftList []*types.NftItem, err error) {
	// 根据父NftId，查询NFT列表
	nftDetailList, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftListByParentNftIds(l.ctx, req.ActivityId, []int64{req.ParentNftId}, 0)
	if err != nil {
		return nil, errors.New(consts.ErrDbError, fmt.Sprintf("CastingBuy GetNftListByParentNftIds error: %v", err))
	}

	// 判断是否普通的NFT
	if req.NftType == nft_detail.NftTypeNormal && req.NftId == 0 {
		return rspNftList, errors.New(consts.NFT_ACTIVITY_CAN_NOT_CASTING, "nft_id参数不能为空")
	}

	// 是否为免费场景
	isFree := l.checkIsFree(req, verifyResp)

	// 根据场景进行处理
	castNftDetailList := make([]*nft_detail.NftDetail, 0, req.Number)
	if isFree {
		// 免费场景
		// 获取当前操作的NFT
		castNft := l.getByNftID(req, isFree, nftDetailList)
		if castNft == nil {
			return rspNftList, errors.New(consts.ErrInvalidParam, "要领取的NFT不存在")
		}
		castNftDetailList = append(castNftDetailList, castNft)

		// - 是否已领取过该nft
		castLog, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftMintLogByUidAndNftId(l.ctx, req.UserId, req.ActivityId, castNft.NftId)
		if err != nil {
			return nil, errors.New(consts.ErrDbError, fmt.Sprintf("CastingBuy GetNftMintLogByUidAndNftId error: %v", err))
		}
		if castLog != nil && (castLog.MintStatus == nft_mint_log.MINT_STATUS_IN_PROGRESS || castLog.MintStatus == nft_mint_log.MINT_STATUS_SUCCESS) {
			return rspNftList, errors.New(consts.NFT_ACTIVITY_SUB_TASK_CASTED, "已铸造过NFT，不能重复铸造")
		}

		// 用户是否已完成kyc
		isKyc, err := dao.CheckUserIsKyc(l.ctx, l.svcCtx, req.UserId)
		if err != nil {
			return nil, errors.New(consts.ErrDbError, fmt.Sprintf("CastingBuy CheckUserIsKyc error: %v", err))
		}
		if !isKyc {
			return rspNftList, errors.New(consts.NFT_ACTIVITY_NOT_KYC, "请先完成kyc认证")
		}

		// 铸造NFT
		err = l.castNftHandle(castNft, req, isFree)
		if err != nil {
			return rspNftList, err
		}
	} else {
		// 其他为购买场景
		// 验证购买数量
		if req.Number <= 0 {
			return rspNftList, errors.New(consts.NFT_ACTIVITY_BUY_NUM_ERR1, "购买数量错误")
		}
		if req.Number > 10 {
			return rspNftList, errors.New(consts.NFT_ACTIVITY_BUY_NUM_MAX_LIMIT, "购买数量超过最大限制")
		}

		if req.NftId > 0 {
			// 购买普通NFT场景
			// - 获取当前操作的NFT
			castNftDetail := l.getByNftID(req, isFree, nftDetailList)
			if castNftDetail == nil {
				return rspNftList, errors.New(consts.ErrInvalidParam, "要领取的NFT不存在")
			}

			// - 根据购买数量，生成相应数量的数组
			for i := 0; i < int(req.Number); i++ {
				castNftDetailList = append(castNftDetailList, castNftDetail)
			}

			// 验证资产是否足够
			if err := l.checkAsset(req, castNftDetailList); err != nil {
				return rspNftList, err
			}

			// - 铸造NFT
			for _, castNftDetail := range castNftDetailList {
				err = l.castNftHandle(castNftDetail, req, isFree)
				if err != nil {
					return rspNftList, err
				}
			}
		} else {
			// 购买高级NFT场景
			// - 根据购买数量，生成相应数量的数组
			for i := 0; i < int(req.Number); i++ {
				// 随机选择一个NFT
				castNftDetail := l.getRandNft(req, nftDetailList)
				if castNftDetail == nil {
					return rspNftList, errors.New(consts.ErrInvalidParam, "要领取的NFT不存在")
				}
				castNftDetailList = append(castNftDetailList, castNftDetail)
			}

			// 验证资产是否足够
			if err := l.checkAsset(req, castNftDetailList); err != nil {
				return rspNftList, err
			}

			// - 铸造NFT
			for _, castNftDetail := range castNftDetailList {
				err = l.castNftHandle(castNftDetail, req, isFree)
				if err != nil {
					return rspNftList, err
				}
			}
		}
	}

	// 返回获得的NFT结果
	return l.getNftRspNftListFormat(castNftDetailList)
}

/**
 * 检查资产是否足够
 */
func (l *CastingLogic) checkAsset(req *types.CastingReq, nftDetailList []*nft_detail.NftDetail) error {
	// 获取用户资产
	balanceResp, err := apiengine.NewClient().GetBalanceByUid(l.ctx, l.svcCtx, &apiengine.BalanceQueryRequest{
		Uid:   int(req.UserId),
		Asset: "USDT",
	})
	if err != nil {
		return err
	}
	usdtAsset, exists := balanceResp.Balance["USDT"]
	if !exists {
		return errors.New(consts.NFT_ACTIVITY_ASSETS_INSUFFICIENT, "资产不足")
	}
	usdtAvailable := cast.ToFloat64(usdtAsset.Available)
	if usdtAvailable <= 0 {
		return errors.New(consts.NFT_ACTIVITY_ASSETS_INSUFFICIENT, "资产不足")
	}

	// 获取NFT总价格
	var totalPrice float64
	for _, nftDetail := range nftDetailList {
		totalPrice += nftDetail.CenNftPrice.InexactFloat64()
	}

	// 判断资产是否足够
	if totalPrice > usdtAvailable {
		return errors.New(consts.NFT_ACTIVITY_ASSETS_INSUFFICIENT, "资产不足")
	}
	return nil
}

/**
 * 铸造NFT
 */
func (l *CastingLogic) castNftHandle(castNft *nft_detail.NftDetail, req *types.CastingReq, isFree bool) error {
	// NFT价格
	var nftPrice decimal.Decimal
	if !isFree {
		nftPrice = castNft.CenNftPrice

		// 测试代码
		//nftPrice = decimal.NewFromFloat(1)
	}

	// 发送铸造NFT请求
	nftService := pkgnft.NewNft(l.ctx, l.svcCtx)
	resp, err := nftService.Mint(req.UserId, cast.ToInt(castNft.NftId), nftPrice.InexactFloat64(), pkgnft.BUY_NFT_CURRENCY)
	if err != nil {
		return errors.New(consts.ErrDbError, fmt.Sprintf("castNftHandle insert error： %v", err))
	}

	// 记录铸造日志
	castLog := &nft_mint_log.NftMintLog{
		NftId:            castNft.NftId,
		ParentNftId:      castNft.ParentNftId,
		NftUserSubTaskId: req.UserSubTaskId,
		Uid:              req.UserId,
		ActivityId:       req.ActivityId,
		MintStatus:       nft_mint_log.MINT_STATUS_IN_PROGRESS,
		PayType:          nft_mint_log.PAY_TYPE_CENTER,
		Expenditure:      nftPrice,
		Mid:              resp.Mid,
		OperationId:      req.OrderId,
	}
	err = nft_mint_log.NewNftMintLogModel(l.svcCtx.DBMarketingActivities).Insert(l.ctx, castLog)
	if err != nil {
		return errors.New(consts.ErrDbError, fmt.Sprintf("castNftHandle insert error： %v", err))
	}
	return nil
}

/**
 * 返回获得的NFT结果
 */
func (l *CastingLogic) getNftRspNftListFormat(castNftList []*nft_detail.NftDetail) (rspNftList []*types.NftItem, err error) {
	// 结果去重
	rspNftMap := make(map[int64]*types.NftItem)
	for _, castNft := range castNftList {
		rspNftItem, ok := rspNftMap[castNft.NftId]
		if !ok {
			rspNftMap[castNft.NftId] = &types.NftItem{
				NftID:      castNft.NftId,
				NftName:    castNft.NftName,
				NftType:    castNft.NftType,
				NftIconURL: castNft.NftIconUrl,
				NftGifURL:  castNft.NftGifUrl,
				Amount:     1,
			}
		} else {
			rspNftItem.Amount++
		}
	}

	// 返回结果
	rspNftList = make([]*types.NftItem, 0)
	for _, rspNftItem := range rspNftMap {
		rspNftList = append(rspNftList, rspNftItem)
	}
	return rspNftList, nil
}

/**
 * 随机一个NFT
 */
func (l *CastingLogic) getRandNft(req *types.CastingReq, nftList []*nft_detail.NftDetail) *nft_detail.NftDetail {
	if len(nftList) == 0 {
		return nil
	}

	// 分离出银和金 NFT
	var silverNfts, goldNfts []*nft_detail.NftDetail
	for _, nft := range nftList {
		switch nft.NftType {
		case nft_detail.NftTypeSilver:
			silverNfts = append(silverNfts, nft)
		case nft_detail.NftTypeGold:
			goldNfts = append(goldNfts, nft)
		}
	}

	// 如果没有高级 NFT，返回 nil
	if len(silverNfts) == 0 && len(goldNfts) == 0 {
		return nil
	}

	// 生成随机数决定选择哪种类型 (0-99)
	randNum := rand.Intn(100)
	var selectedNfts []*nft_detail.NftDetail
	switch {
	case randNum < 70 && len(silverNfts) > 0: // 70% 概率选择银 NFT
		selectedNfts = silverNfts
	case len(goldNfts) > 0: // 30% 概率选择金 NFT
		selectedNfts = goldNfts
	default: // 如果没有金 NFT，只能选择银 NFT
		selectedNfts = silverNfts
	}

	// 从选定的类型中随机选择一个 NFT
	randIndex := rand.Intn(len(selectedNfts))
	return selectedNfts[randIndex]
}

/**
 * 如果传了NftId，从列表找出这条记录
 */
func (l *CastingLogic) getByNftID(req *types.CastingReq, isFree bool, nftList []*nft_detail.NftDetail) *nft_detail.NftDetail {
	for _, nft := range nftList {
		if nft.NftId == req.NftId {
			return nft
		}
	}
	return nil
}

/**
 * 是否免费场景
 */
func (l *CastingLogic) checkIsFree(req *types.CastingReq, verifyResp pkgnft.MintNftResponse) bool {
	var isFree bool = true

	// 非普通nft，则为购买场景
	if req.NftType != nft_detail.NftTypeNormal {
		isFree = false
	}

	// 非免费领取期，则为购买场景
	if !verifyResp.FreeReceiveTimeWindow {
		isFree = false
	}
	return isFree
}

/**
 * 惊喜任务
 */
func (l *CastingLogic) CastingSurprise(req *types.CastingReq) (rspNftList []*types.NftItem, err error) {
	// 查询子任务
	subTaskDataModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)
	subTask, err := subTaskDataModel.GetSubTaskByID(l.ctx, req.UserSubTaskId)
	if err != nil {
		return nil, errors.New(consts.ErrDbError, fmt.Sprintf("CastingTask GetSubTaskByID error: %v", err))
	}
	if subTask == nil {
		return nil, errors.New(consts.ErrInvalidParam, "user_sub_task_id参数错误")
	}
	if subTask.Status != nft.TaskStatusCompleted {
		return nil, errors.New(consts.NFT_ACTIVITY_SUB_TASK_INCOMPLETE, "任务未完成，不能进行铸造")
	}

	// 获取免费的NFT列表
	nftList, err := pkgnft.GetNftList(l.svcCtx, l.ctx, req.ActivityId)
	if err != nil {
		logx.Infof("CastingSurprise GetNftList err is:%v", err)
		return nil, err
	}
	nftListMap := make(map[int64]*nft_detail.NftDetail, len(nftList))
	for _, nft := range nftList {
		nftListMap[nft.NftId] = nft
	}

	// 获取NFT列表
	nftIdList, totalCollectNft, collectedNft, err := l.GetSurpriseCastNftList(req, nftList)
	if err != nil {
		return rspNftList, err
	}

	// 如果用户已经铸造完成，则返回
	if collectedNft >= totalCollectNft {
		return nil, errors.New(consts.NFT_ACTIVITY_ALREADY_CASTING, "您已领取完所有的nft")
	}

	// 用户是否已完成kyc
	isKyc, err := dao.CheckUserIsKyc(l.ctx, l.svcCtx, req.UserId)
	if err != nil {
		return nil, errors.New(consts.ErrDbError, fmt.Sprintf("CastingSurprise CheckUserIsKyc error: %v", err))
	}
	if !isKyc {
		return rspNftList, errors.New(consts.NFT_ACTIVITY_NOT_KYC, "请先完成kyc认证")
	}

	// 铸造NFT，如果库存为空，则尝试铸造下一个
	rspNftList = make([]*types.NftItem, 0)
	nftObject := pkgnft.NewNft(l.ctx, l.svcCtx)
	for _, nftId := range nftIdList {
		// 发放nft请求
		goodsId, err := nftObject.Airdrop(req.UserId, cast.ToString(nftId))
		logx.Info("CastingHandle Airdrop Req: UserId：", cast.ToString(req.UserId), " nftId: ", cast.ToString(nftId), " err: ", err, " goodsId: ", goodsId)
		if err != nil {
			if cm, ok := err.(*errors.CodeMsg); ok {
				// 空投库存不足，则重试nftId
				if cm.Code == consts.NFT_AIR_DROP_NO_STOCK {
					continue
				}
			} else {
				// 正常的失败，就失败
				return nil, err
			}
		}

		// 记录铸造日志
		nftDetail := nftListMap[nftId]
		castLog := &nft_mint_log.NftMintLog{
			NftId:            nftId,
			ParentNftId:      nftDetail.ParentNftId,
			NftUserSubTaskId: req.UserSubTaskId,
			Uid:              req.UserId,
			ActivityId:       req.ActivityId,
			MintStatus:       nft_mint_log.MINT_STATUS_SUCCESS,
			PayType:          nft_mint_log.PAY_TYPE_CENTER,
			Expenditure:      decimal.NewFromFloat(0),
			Mid:              goodsId,
			OperationId:      req.OrderId,
			SceneType:        1,
		}
		err = nft_mint_log.NewNftMintLogModel(l.svcCtx.DBMarketingActivities).Insert(l.ctx, castLog)
		if err != nil {
			return nil, errors.New(consts.ErrDbError, fmt.Sprintf("castNftHandle insert error： %v", err))
		}

		// 返回结果
		castNft := nftListMap[nftId]
		rspNftList = append(rspNftList, &types.NftItem{
			NftID:      castNft.NftId,
			NftName:    castNft.NftName,
			NftType:    castNft.NftType,
			NftIconURL: castNft.NftIconUrl,
			NftGifURL:  castNft.NftGifUrl,
			Amount:     1,
		})
		return rspNftList, nil
	}

	return rspNftList, nil
}

/**
 * 获取惊喜加时任务期间-可以铸造的NFT
 * @desc 根据返回的列表顺序，失败时，重试铸造NFT
 * @return rspNftList 可以铸造的NFT列表
 * @return totalCollectNft 总的NFT数量
 * @return collectedNft 已经铸造的NFT数量
 */
func (l *CastingLogic) GetSurpriseCastNftList(req *types.CastingReq, nftList []*nft_detail.NftDetail) (rspNftList []int64, totalCollectNft int, collectedNft int, err error) {
	// 找到普通NFT
	freeNftMap := make(map[int64]*nft_detail.NftDetail)
	for _, nft := range nftList {
		if nft.NftType == nft_detail.NftTypeNormal {
			freeNftMap[nft.NftId] = nft
			totalCollectNft++
		}
	}

	// 获取用户已经铸造过的免费NFT列表
	nftMintLogs, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftMintLogList(l.ctx, req.UserId, "", 0, 0, req.ActivityId, 0)
	if err != nil {
		return nil, totalCollectNft, collectedNft, errors.New(consts.ErrDbError, fmt.Sprintf("CastingSurprise GetNftMintLogList error: %v", err))
	}
	alreadyCastNft := []int64{}
	alreadyCastNftMap := make(map[int64]struct{}, 0)
	for _, nftMintLog := range nftMintLogs {
		alreadyCastNftMap[nftMintLog.NftId] = struct{}{}
		alreadyCastNft = append(alreadyCastNft, nftMintLog.NftId)
		collectedNft++
	}

	// 未铸造的NFT列表
	notExistsNft := []int64{}
	for _, nftDetail := range freeNftMap {
		if _, exists := alreadyCastNftMap[nftDetail.NftId]; exists {
			continue
		}
		notExistsNft = append(notExistsNft, nftDetail.NftId)
	}

	// 将NFT列表，进行排序，未领取过的，排在前面
	rspNftList = append(notExistsNft, alreadyCastNft...)
	return rspNftList, totalCollectNft, collectedNft, nil
}

/**
 * 任务
 */
func (l *CastingLogic) CastingTask(req *types.CastingReq) (rspNftList []*types.NftItem, err error) {

	// 查询子任务
	subTaskDataModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)
	subTask, err := subTaskDataModel.GetSubTaskByID(l.ctx, req.UserSubTaskId)
	if err != nil {
		return nil, errors.New(consts.ErrDbError, fmt.Sprintf("CastingTask GetSubTaskByID error: %v", err))
	}
	if subTask == nil {
		return nil, errors.New(consts.ErrInvalidParam, "user_sub_task_id参数错误")
	}
	if subTask.Status != nft.TaskStatusCompleted {
		return nil, errors.New(consts.NFT_ACTIVITY_SUB_TASK_INCOMPLETE, "任务未完成，不能进行铸造")
	}

	// 用户是否已完成kyc
	isKyc, err := dao.CheckUserIsKyc(l.ctx, l.svcCtx, req.UserId)
	if err != nil {
		return nil, errors.New(consts.ErrDbError, fmt.Sprintf("CastingTask CheckUserIsKyc error: %v", err))
	}
	if !isKyc {
		return rspNftList, errors.New(consts.NFT_ACTIVITY_NOT_KYC, "请先完成kyc认证")
	}

	req.ParentNftId = 0
	// 选择一个可以铸造的NFT
	var castNum = 1
	if subTask.TaskType == nft.TaskTypeKyc {
		castNum = 2
	}
	castNftDetail, err := l.SelectNft(req, castNum)
	if err != nil {
		return nil, err
	}
	// 发送铸造请求
	var nftList = make([]*nft_detail.NftDetail, 0)
	for _, castInfo := range castNftDetail {
		err = l.castNftHandle(castInfo, req, true)
		if err != nil {
			return nil, errors.New(consts.ErrDbError, fmt.Sprintf("CastingTask castNftHandle error: %v", err))
		}

		// 返回nft信息
		nftList = append(nftList, castInfo)
	}

	return l.getNftRspNftListFormat(nftList)
}

/**
 * 获取可以铸造的nft
 */
func (l *CastingLogic) SelectNft(req *types.CastingReq, needNum int) ([]*nft_detail.NftDetail, error) {
	// 获取已经铸造过的免费的nft
	alreadyCastNftMap, alreadyCastParentNftIdMap, err := l.GetAlreadyCastNftMap(req)
	if err != nil {
		return nil, err
	}

	// 根据parentNftId获取NftId配置
	nftDetailList, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftListByParentNftIds(l.ctx, req.ActivityId, []int64{}, nft_detail.NftTypeNormal)
	if err != nil {
		return nil, errors.New(consts.ErrDbError, fmt.Sprintf("CastingTask GetNftListByParentNftIds error: %v", err))
	}

	// 未铸造过或铸造失败的nftId
	var castNftDetail []*nft_detail.NftDetail
	assignNum := 0
	for _, nftDetail := range nftDetailList {
		// 如果已经铸造过，则跳过
		if _, ok := alreadyCastNftMap[nftDetail.NftId]; ok {
			continue
		}
		if _, ok := alreadyCastParentNftIdMap[nftDetail.ParentNftId]; ok {
			continue
		}
		if nftDetail.NftId == 0 {
			continue
		}
		// 没有铸造过 或者 铸造失败，可以进行铸造
		castNftDetail = append(castNftDetail, nftDetail)
		// 不重复生产同一个parentID的nft
		alreadyCastParentNftIdMap[nftDetail.ParentNftId] = struct{}{}
		assignNum++
		if assignNum >= needNum {
			break
		}

	}
	if len(castNftDetail) == 0 {
		return nil, errors.New(consts.NFT_ACTIVITY_SUB_TASK_CASTED, "您已铸造过该类型NFT，不能重复铸造")
	}
	return castNftDetail, nil
}

/**
 * 获取已经铸造过的免费的nft信息
 */
func (l *CastingLogic) GetAlreadyCastNftMap(req *types.CastingReq) (map[int64]struct{}, map[int64]struct{}, error) {
	// 查询铸造记录
	nftMintLogs, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftMintLogList(l.ctx, req.UserId, "", req.ParentNftId, 0, req.ActivityId, 0)
	if err != nil {
		return nil, nil, errors.New(consts.ErrDbError, fmt.Sprintf("CastingTask GetNftMintLogList error: %v", err))
	}

	// 存储铸造过的nftId
	alreadyCastNftMap := make(map[int64]struct{})
	alreadyCastParentNftMap := make(map[int64]struct{})
	for _, nftMintLog := range nftMintLogs {
		// 当前任务ID，已经铸造过
		if nftMintLog.NftUserSubTaskId == req.UserSubTaskId && (nftMintLog.MintStatus == nft_mint_log.MINT_STATUS_IN_PROGRESS || nftMintLog.MintStatus == nft_mint_log.MINT_STATUS_SUCCESS) {
			return nil, nil, errors.New(consts.NFT_ACTIVITY_SUB_TASK_CASTED, "任务已铸造过NFT，不能重复铸造")
		}

		// 如果是购买的NFT，则忽略
		if !nftMintLog.Expenditure.IsZero() {
			continue
		}

		// 已经铸造过的免费的NFT
		alreadyCastNftMap[nftMintLog.NftId] = struct{}{}
		alreadyCastParentNftMap[nftMintLog.ParentNftId] = struct{}{}
	}
	return alreadyCastNftMap, alreadyCastParentNftMap, nil
}

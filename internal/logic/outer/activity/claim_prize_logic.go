package activity

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/models/reclaim_prize_record"
	"github.com/spf13/cast"
	"regexp"
	"strings"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

const PrizePhysical = 1
const PrizeVirtual = 2

var PrizeType2PrizeCategoryMap = map[int64]int64{
	1:  PrizePhysical,
	2:  PrizePhysical,
	3:  PrizePhysical,
	4:  PrizePhysical,
	5:  PrizeVirtual,
	6:  PrizeVirtual,
	7:  PrizeVirtual,
	8:  PrizeVirtual,
	9:  PrizeVirtual,
	10: PrizePhysical,
}

type ClaimPrizeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 兑奖
func NewClaimPrizeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ClaimPrizeLogic {
	return &ClaimPrizeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// 使用较通用的邮箱正则表达式（覆盖大多数常见格式）
func isEmail(email string) bool {
	// 正则说明：
	// ^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+  匹配本地部分（允许特殊字符）
	// @[a-zA-Z0-9-]+                      @符号+域名（允许中划线）
	// (\.[a-zA-Z0-9-]+)*$                 域名后缀（允许多级域名如 .com.cn）
	pattern := `^[a-zA-Z0-9.!#$%&'*+/=?^_` + "`" + `{|}~-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*$`

	// 编译正则表达式
	re := regexp.MustCompile(pattern)

	// 匹配整个字符串
	return re.MatchString(email)
}

func verifyClaimInfo(claimInfo *types.ClaimInfo, uid int, isCentralized bool, prizeCategory int64) error {
	if prizeCategory == PrizePhysical {
		if strings.TrimSpace(claimInfo.Name) == "" || len(claimInfo.Name) > 300 ||
			strings.TrimSpace(claimInfo.Phone) == "" || len(claimInfo.Phone) > 300 ||
			strings.TrimSpace(claimInfo.Email) == "" || !isEmail(claimInfo.Email) || len(claimInfo.Email) > 300 ||
			strings.TrimSpace(claimInfo.Address) == "" || len(claimInfo.Address) > 300 ||
			strings.TrimSpace(claimInfo.City) == "" || len(claimInfo.City) > 300 ||
			strings.TrimSpace(claimInfo.State) == "" || len(claimInfo.State) > 300 ||
			strings.TrimSpace(claimInfo.Country) == "" || len(claimInfo.Country) > 300 ||
			strings.TrimSpace(claimInfo.PostalCode) == "" || len(claimInfo.PostalCode) > 300 {
			return fmt.Errorf("invalid claim info")
		}
	} else {
		if isCentralized {
			claimInfo.GateUid = cast.ToString(uid)
		} else {
			if strings.TrimSpace(claimInfo.GateUid) == "" || len(claimInfo.GateUid) > 300 ||
				strings.TrimSpace(claimInfo.RegisteredEmail) == "" || len(claimInfo.RegisteredEmail) > 300 ||
				!isEmail(claimInfo.RegisteredEmail) {
				return fmt.Errorf("invalid claim info")
			}
		}
	}
	return nil
}

func (l *ClaimPrizeLogic) ClaimPrize(req *types.ClaimPrizeReq, uid int) (resp *types.ClaimPrizeResp, err error) {
	// 这个接口必须传钱包地址或者用户必须登录
	isCentralized := req.IsCentralization == 1
	resp = &types.ClaimPrizeResp{}
	if isCentralized && uid == 0 {
		logc.Infof(l.ctx, "ClaimPrize req  uid is zero: %d", uid)
		return nil, errors.New(consts.ErrUserLogin, "user not login")
	}
	if !isCentralized && len(strings.TrimSpace(req.WalletAddress)) == 0 {
		logc.Infof(l.ctx, "UserNFTPrizeInfo req invalid walletAddress is empty: %v", req.WalletAddress)
		return nil, errors.New(consts.ErrUserLogin, "wallet address is empty")
	}
	// 查询当前奖品和钱包地址/uid是否一致，如果不一致直接报错
	lotteryModel := nft_activity.NewNftLotteryModel(l.svcCtx.DBMarketingActivities)
	lotteryInfo, err := lotteryModel.FindOne(l.ctx, req.PrizeInfoID)
	if err != nil {
		logc.Infof(l.ctx, "lotteryModel.FindOne err: %v", err)
		return nil, errors.New(consts.ErrRecordNotExist, "prize not existed!")
	}
	if lotteryInfo == nil {
		logc.Infof(l.ctx, "lotteryModel.FindOne is nil %v", lotteryInfo)
		return nil, errors.New(consts.ErrRecordNotExist, "prize not existed!")
	}

	lotteryID := ""
	if isCentralized {
		lotteryID = cast.ToString(uid)
	} else {
		lotteryID = req.WalletAddress
	}
	if lotteryInfo.LotteryId != lotteryID {
		logc.Infof(l.ctx, "lotteryInfo.LotteryId != lotteryID lotteryInfo.LotteryId:%v, lotteryInfo:%v", lotteryInfo.LotteryId, lotteryID)
		return nil, errors.New(consts.ErrRecordNotExist, "you are not the owner of the prize")
	}

	prizeCategory := PrizeType2PrizeCategoryMap[lotteryInfo.PrizeId]
	// 判断奖品类型和参数是否匹配, 中心化用户的虚拟奖励不需要用户自己填写uid，直接获取登录的uid
	err = verifyClaimInfo(&req.ClaimInfo, uid, isCentralized, prizeCategory)
	if err != nil {
		logc.Infof(l.ctx, "verifyClaimInfo err: %v", err)
		return nil, errors.New(consts.ErrInvalidParam, "please complete the claim info")
	}

	// 判断当前奖品用户是否已经填过过地址
	reclaimModel := reclaim_prize_record.NewReclaimPrizeRecordModel(l.svcCtx.DBMarketingActivities)
	record, err := reclaimModel.GetRecordByPrizeInfoID(l.ctx, req.PrizeInfoID)
	if err != nil {
		logc.Infof(l.ctx, "ClaimPrizeLogic GetRecordByPrizeInfoID err: %v", err)
		return nil, errors.New(consts.ErrSystemError, "please try again later")
	}
	// 已经存在记录了
	if record != nil {
		logc.Infof(l.ctx, "user already claim prize")
		return nil, errors.New(consts.AttendedToday, "you have claimed the prize")
	}

	//
	reclaimInfo, err := json.Marshal(req.ClaimInfo)
	if err != nil {
		logc.Infof(l.ctx, "ClaimPrizeLogic Marshal err: %v", err)
		return nil, errors.New(consts.ErrSystemError, "please try again later")
	}
	reclaimInfoObj := sql.NullString{}
	err = reclaimInfoObj.Scan(string(reclaimInfo))
	if err != nil {
		logc.Infof(l.ctx, "ClaimPrizeLogic Scan err: %v", err)
		return nil, errors.New(consts.ErrSystemError, "please try again later")
	}
	err = reclaimModel.Insert(l.ctx, &reclaim_prize_record.ReclaimPrizeRecord{
		PrizeInfoId:   req.PrizeInfoID,
		Uid:           int64(uid),
		WalletAddress: req.WalletAddress,
		PrizeCategory: prizeCategory,
		PrizeType:     lotteryInfo.PrizeId,
		ReclaimInfo:   reclaimInfoObj,
	})
	if err != nil {
		logc.Infof(l.ctx, "ClaimPrizeLogic Insert err: %v", err)
		return nil, errors.New(consts.ErrSystemError, "please try again later")

	}

	return
}

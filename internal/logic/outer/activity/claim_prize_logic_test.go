package activity

import (
	"fmt"
	"gateio_service_marketing_activity/internal/types"
	"testing"
)

func Test_IsEmail(t *testing.T) {
	fmt.Println(isEmail("email"))
	fmt.Println(isEmail("<EMAIL>"))
	fmt.Println(isEmail("<EMAIL>"))
}

func Test_verifyClaimInfo(t *testing.T) {
	fmt.Println(verifyClaimInfo(&types.ClaimInfo{
		Name:            "Name",
		Phone:           "Phne",
		Email:           "<EMAIL>",
		Address:         "Address",
		City:            "City",
		State:           "State",
		Country:         "Country",
		PostalCode:      "PostalCode",
		GateUid:         "11111",
		RegisteredEmail: "<EMAIL>",
	}, 111, false, 2))
}

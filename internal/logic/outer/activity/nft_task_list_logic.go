package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/service"
	"net/http"
	"sort"
	"time"

	gd "github.com/shopspring/decimal"

	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"github.com/spf13/cast"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/consts/activity"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/mqs/kafka"
	pkg_nft "gateio_service_marketing_activity/internal/pkg/nft"

	"gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/models/nft_mint_log"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"
)

type NftTaskListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewNftTaskListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NftTaskListLogic {
	return &NftTaskListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NftTaskListLogic) NftTaskList(req *types.NftTaskListReq, r *http.Request) (resp *types.NftTaskListResp, err error) {
	// 验证用户
	if req.Uid > 0 {
		userInfo := requestools.GetUserInfo(r)
		if userInfo == nil || int64(userInfo.UID) != req.Uid {
			return nil, errors.New(consts.ErrUserLogin, "请先登录")
		}
		//获取账户信息，用于验证是否是子账户
		userMap, _ := service.NewUserCenterCall(l.ctx).GetUserMap([]int64{req.Uid})
		if user, exists := userMap[req.Uid]; exists && user != nil && user.IsSub == 1 {
			logx.Infof("NftTaskList sub user use :%d, is_sub val is:%d", req.Uid, userMap[req.Uid].IsSub)
			return nil, errors.New(consts.NFT_SUB_ACCOUNT_NO_ALLOW, "子账号不能参与活动")
		}
	}

	// 请求参数验证
	if err := l.NftTaskListValidate(req); err != nil {
		return nil, err
	}
	reqByte, _ := json.Marshal(req)
	// 获取进行中的活动
	activityInfo, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetRunningActivitie(l.ctx, 1, 1, req.Uid > 0)
	if err != nil {
		logx.Infof("NftTaskList GetRunningActivitie  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}
	if activityInfo == nil {
		logx.Infof("NftTaskList activityInfo is null, req is:%s", string(reqByte))
		return nil, errors.New(consts.NFT_ACTIVITY_END, "活动已结束")
	}
	if activityInfo.TaskStartTime.After(time.Now()) {
		logx.Infof("NftTaskList The current period is not an additional activity period, req is:%s", string(reqByte))
		return nil, errors.New(consts.NFT_ACTIVITY_NOT_START, "活动未开始")
	}

	// 如果当前不是任务期，则返回空列表
	taskStep := pkg_nft.GetTaskStep(activityInfo)
	if taskStep != pkg_nft.ACTIVITY_STEP_TASK {
		logx.Infof("NftTaskList The current period is not an additional activity period, req is:%s", string(reqByte))
		return &types.NftTaskListResp{
			List: make([]*types.NftTaskListItem, 0),
		}, nil
	}

	// 获取任务配置
	var isCentralization int
	if req.IsUserIdReq {
		isCentralization = 1
	}
	taskModel := nft.NewNftTaskModel(l.svcCtx.DBMarketingActivities)
	taskList, err := taskModel.GetTaskList(l.ctx, isCentralization, nft.SceneTypeAddTime)
	if err != nil {
		logx.Infof("NftTaskList GetTaskList  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}

	if isCentralization == 1 {
		// 中心化的任务
		sort.Slice(taskList, func(i, j int) bool {
			return taskList[i].TaskType > taskList[j].TaskType
		})
	}

	// 获取子任务列表数据
	userSubTaskList, err := l.GetUserSubTask(req, activityInfo)
	if err != nil {
		logx.Infof("NftTaskList GetUserSubTask  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}

	// 如果没有任务，则进行初始化
	if len(userSubTaskList) == 0 {
		userSubTaskList, err = l.InitTaskRecord(req, activityInfo, taskList)
		if err != nil {
			logx.Infof("NftTaskList InitTaskRecord  is err, req is:%s,err is:%v", string(reqByte), err)
			return nil, err
		}
	}

	// 共需收集数量
	totalToCollect := 0
	for _, task := range taskList {
		if task.TaskType == nft.TaskTypeKyc && isCentralization == 1 {
			totalToCollect += int(2 * task.CompletionLimit)
		} else {
			totalToCollect += int(task.CompletionLimit)
		}

	}
	nftList, err := pkg_nft.GetNftList(l.svcCtx, l.ctx, activityInfo.ActivityId)
	if err != nil {
		logx.Infof("NftTaskList GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}
	mintNftLogs, err := pkg_nft.GetNftMintLogList(l.svcCtx, l.ctx, req.Uid, req.WalletAddress, activityInfo.ActivityId)
	if err != nil {
		logx.Infof("NftTaskList GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}

	//获取全部普通nft ID
	basicParentIdNftIds := make([]int64, 0)
	for _, detail := range nftList {
		if detail.NftType == 1 {
			basicParentIdNftIds = append(basicParentIdNftIds, detail.ParentNftId)
		}
	}
	//获取全部已经领取的parentNftID
	mintParentNftMap := map[int64]struct{}{}
	mintParentNftIds := []int64{}
	//定义子任务铸造信息
	nftTaskStatusMap := map[int64]int64{}
	//定义子任务的操作ID信息
	nftSubTaskOperationMap := map[int64]int64{}
	//定义当前是否有正在铸造的免费NFT
	nftMinting := 0
	for _, logNft := range mintNftLogs {
		noPrice := gd.NewFromFloat(0)
		if _, ok := mintParentNftMap[logNft.ParentNftId]; !ok && logNft.MintStatus == 3 && logNft.Expenditure.Equal(noPrice) {
			mintParentNftMap[logNft.ParentNftId] = struct{}{}
			mintParentNftIds = append(mintParentNftIds, logNft.ParentNftId)
		}
		if logNft.NftUserSubTaskId > 0 {
			nftTaskStatusMap[logNft.NftUserSubTaskId] = logNft.MintStatus
			nftSubTaskOperationMap[logNft.NftUserSubTaskId] = logNft.OperationId
		}
		if logNft.NftUserSubTaskId > 0 && logNft.MintStatus < 3 && nftMinting == 0 {
			nftMinting = 1
		}
	}

	//获取已领取的普通nft数量
	mintedParentNftIds := utils.Intersection(basicParentIdNftIds, mintParentNftIds)
	countFreeNft := len(mintedParentNftIds)
	taskStatus := 1
	if activityInfo.TaskStartTime.Before(time.Now()) && activityInfo.TaskEndTime.After(time.Now()) {
		taskStatus = 2 //进行中
	}
	if activityInfo.TaskEndTime.Before(time.Now()) {
		taskStatus = 4 //已结束
	}
	if len(mintedParentNftIds) == len(basicParentIdNftIds) {
		taskStatus = 3 //已经领取完
	}
	timeLeft := cast.ToInt64(time.Until(activityInfo.TaskEndTime).Seconds())
	if timeLeft < 0 {
		timeLeft = 0
	}
	//分割任务信息查询 此处查询的是去中心化任务信息并返回
	if !req.IsUserIdReq {
		//领取nft状态：0: 未完成任务，无法领取、1：任务已完成，可以领取、2: 已领取（铸造中）、3：已领取（铸造
		for i, task := range userSubTaskList {

			subTaskStatus := int64(0)
			if userSubTaskList[i].Status == 2 {
				subTaskStatus = 1
			}
			if task.Status == 2 && (nftTaskStatusMap[task.Id] == 1 || nftTaskStatusMap[task.Id] == 2) {
				subTaskStatus = 2
			}
			if task.Status == 2 && nftTaskStatusMap[task.Id] == 3 {
				subTaskStatus = 3
			}
			userSubTaskList[i].Status = subTaskStatus
		}
		rspList := pkg_nft.NftTaskListFormat(taskList, userSubTaskList, nftSubTaskOperationMap)

		// 格式化并返回数据
		return &types.NftTaskListResp{
			BaseInfo: types.BaseInfo{
				TaskStatus:     taskStatus,
				TotalToCollect: cast.ToInt64(totalToCollect),
				Collected:      cast.ToInt64(countFreeNft),
				TimeLeft:       timeLeft,
				ActivityId:     cast.ToInt64(activityInfo.ActivityId),
				MintingNft:     nftMinting,
			},
			List: rspList,
		}, nil
	}

	// 格式化数据
	rspList, err := l.NftTaskListFormat(req, taskList, userSubTaskList, nftSubTaskOperationMap)
	if err != nil {
		logx.Infof("NftTaskList NftTaskListFormat is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, err
	}
	return &types.NftTaskListResp{
		BaseInfo: types.BaseInfo{
			TaskStatus:     taskStatus,
			TotalToCollect: cast.ToInt64(totalToCollect),
			Collected:      cast.ToInt64(countFreeNft),
			TimeLeft:       timeLeft,
			ActivityId:     cast.ToInt64(activityInfo.ActivityId),
			MintingNft:     nftMinting,
		},
		List: rspList,
	}, nil
}

/**
 * 统计已收集数量
 */
func (l *NftTaskListLogic) CountFreeNft(req *types.NftTaskListReq, activityId int64) (count int64, err error) {
	// 去中心化过滤
	countFilter := map[string]interface{}{
		"activity_id": activityId,
		"mint_status": nft_mint_log.MINT_STATUS_SUCCESS,
		"pay_type":    nft_mint_log.PAY_TYPE_NO_CENTER,
	}
	if req.IsUserIdReq {
		// 用户请求
		countFilter["uid"] = req.Uid
		countFilter["pay_type"] = nft_mint_log.PAY_TYPE_CENTER
	} else {
		// 钱包地址
		countFilter["wallet_address"] = req.WalletAddress
		countFilter["pay_type"] = nft_mint_log.PAY_TYPE_NO_CENTER
	}

	// 获取免费NFT数量
	count, err = nft_mint_log.NewNftMintLogModel(l.svcCtx.DBMarketingActivities).CountByFilter(l.ctx, countFilter)
	if err != nil {
		return 0, err
	}
	return
}

/**
 * 格式化数据
 */
func (l *NftTaskListLogic) NftTaskListFormat(req *types.NftTaskListReq, taskList []*nft.NftTask, subTaskList []*nft.NftUserSubTask, nftSubTaskOperationMap map[int64]int64) (rspList []*types.NftTaskListItem, err error) {
	// 获取已领取NFT的任务数据
	subTaskIdList := make([]int64, 0)
	for _, subTask := range subTaskList {
		subTaskIdList = append(subTaskIdList, subTask.Id)
	}
	nftMintLogMap, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftMintLogsBySubTaskIDsMap(l.ctx, subTaskIdList)
	if err != nil {
		return
	}

	for _, task := range taskList {
		// 组装子任务数据
		doneLimit := 0
		rspSubTaskList := make([]types.SubTaskItem, 0)
		for _, taskRecord := range subTaskList {
			if taskRecord.TaskId == task.TaskId {
				// 领取nft状态
				var receiveNftStatus int64
				if nftMintLog, ok := nftMintLogMap[taskRecord.Id]; ok {
					if ok && nftMintLog != nil {
						if nftMintLog.MintStatus == nft_mint_log.MINT_STATUS_IN_PROGRESS {
							// 已领取（铸造中）
							receiveNftStatus = 2
						} else if nftMintLog.MintStatus == nft_mint_log.MINT_STATUS_SUCCESS {
							// 已领取（铸造完成）
							receiveNftStatus = 3
						}
					}
				} else if taskRecord.Status == nft.TaskStatusInitial {
					// 未完成任务
					receiveNftStatus = 0
				} else if taskRecord.Status == nft.TaskStatusCompleted {
					// 任务已完成
					receiveNftStatus = 1
				}

				// 帮助完成任务的用户信息
				helpUserId := taskRecord.HelpUserId
				if !req.IsUserIdReq {
					helpUserId = utils.AbbreviateString(taskRecord.HelpUserId)
				}

				// 组装数据
				rspSubTaskList = append(rspSubTaskList, types.SubTaskItem{
					SubTaskId:        taskRecord.Id,
					HelpUserId:       helpUserId,
					HelpUserAvatar:   taskRecord.HelpUserAvatar,
					ReceiveNftStatus: receiveNftStatus,
					OperationID:      nftSubTaskOperationMap[taskRecord.Id],
				})

				// 完成次数
				if taskRecord.Status == nft.TaskStatusCompleted {
					doneLimit++
				}
			}
		}

		// 主任务数据
		rspList = append(rspList, &types.NftTaskListItem{
			TaskId:          task.TaskId,             // 任务ID
			TaskName:        task.TaskName,           // 任务名称
			TaskType:        task.TaskType,           // 任务类型
			DoneLimit:       cast.ToInt64(doneLimit), // 已完成次数
			CompletionLimit: task.CompletionLimit,    // 可完成次数
			SubTaskList:     rspSubTaskList,
		})
	}
	return
}

/**
 * 初始化任务
 */
func (l *NftTaskListLogic) InitTaskRecord(req *types.NftTaskListReq, activityInfo *nft_activity.NftActivity, taskList []*nft.NftTask) ([]*nft.NftUserSubTask, error) {
	subTaskDataModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)

	// 插入计划
	rspList := make([]*nft.NftUserSubTask, 0)
	for _, task := range taskList {
		// 插入任务记录
		for range int(task.CompletionLimit) {
			// 生成本地任务
			taskRecord := &nft.NftUserSubTask{
				ActivityId:    activityInfo.ActivityId,
				UserId:        cast.ToInt64(req.Uid),
				WalletAddress: req.WalletAddress,
				TaskId:        task.TaskId,
				TaskType:      task.TaskType,
				MediaId:       task.MediaId,
				Status:        nft.TaskStatusInitial,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
				SceneType:     nft.SceneTypeAddTime,
			}
			inserId, err := subTaskDataModel.CreateTaskRecord(l.ctx, taskRecord)
			if err != nil {
				return nil, err
			}
			fmt.Println("insertId: ", inserId)

			// 任务系统创建任务
			/*
				if task.TaskType == nft.TaskTypeForwardXTweet {
					// 上报任务系统
					l.submitTaskRecord(req, task, inserId)

					// 更新任务状态为已上报
					err = subTaskDataModel.UpdateOne(l.ctx, inserId, []string{
						"status",
						"update_at",
					}, &nft.NftUserSubTask{
						Status:    nft.TaskStatusReported,
						UpdatedAt: time.Now(),
					})
					if err != nil {
						return nil, err
					}
				}*/

			// 组合列表数据
			rspList = append(rspList, taskRecord)
		}
	}

	return rspList, nil
}

/**
 * 上报任务系统
 */
func (l *NftTaskListLogic) submitTaskRecord(req *types.NftTaskListReq, task *nft.NftTask, businessId int64) error {
	// 把业务的任务上报给任务系统
	getTaskTime := time.Now().Unix()
	kafkaProducer, err := kafka.NewKafkaProducer(l.svcCtx.KafkaConf)
	if err != nil {
		logx.Infof("submitTaskRecord failed, step: kafka.NewKafkaProducer, UserId : %d, BusinessId: %d, err: %v", req.Uid, businessId, err)
		return err
	}
	defer kafkaProducer.Close()

	// 解析配置
	taskConfig := &nft.TaskConfig{}
	if err := json.Unmarshal([]byte(task.TaskConfig), taskConfig); err != nil {
		return err
	}

	err = kafkaProducer.TaskRecordReceiveProducer(l.ctx, req.Uid, taskConfig.TaskId, activity.NftTaskBusinessType, businessId, getTaskTime, 0)
	logx.Infof("submitTaskRecord, step: 上报任务系统的消息：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d", req.Uid, businessId, taskConfig.TaskId, activity.NftTaskBusinessType)
	if err != nil {
		logx.Infof("submitTaskRecord failed, step: Producer.TaskRecordReceiveProducer, UserId : %d, BusinessId: %d, err: %v", req.Uid, businessId, err)
	}
	return nil
}

/**
 * 请求任务列表数据
 */
func (l *NftTaskListLogic) GetUserSubTask(req *types.NftTaskListReq, activityInfo *nft_activity.NftActivity) ([]*nft.NftUserSubTask, error) {
	dataModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)

	if req.IsUserIdReq {
		// 用户ID
		recordList, err := dataModel.GetTaskRecordListByUid(l.ctx, activityInfo.ActivityId, req.Uid, map[string]interface{}{})
		if err != nil {
			return nil, fmt.Errorf("GetTaskRecordListByUid error: %v", err)
		}
		return recordList, nil
	}

	// 钱包地址
	recordList, err := dataModel.GetTaskRecordListByWalletAddress(l.ctx, activityInfo.ActivityId, req.WalletAddress, map[string]interface{}{})
	if err != nil {
		return nil, fmt.Errorf("GetTaskRecordListByWalletAddress error: %v", err)
	}
	return recordList, nil
}

/**
 * 请求参数验证
 */
func (l *NftTaskListLogic) NftTaskListValidate(req *types.NftTaskListReq) (err error) {
	isUserReq := (req.Uid > 0)
	req.IsUserIdReq = isUserReq

	// 唯一参数验证
	if req.Uid == 0 && len(req.WalletAddress) == 0 {
		return errors.New(consts.ErrInvalidParam, "uid or wallet_address is required")
	}

	return nil
}

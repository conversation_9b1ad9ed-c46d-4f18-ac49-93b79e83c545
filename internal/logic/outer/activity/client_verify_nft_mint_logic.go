package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/models/nft_detail"
	"gateio_service_marketing_activity/internal/models/nft_mint_log"
	"gateio_service_marketing_activity/internal/utils"
	"net/http"
	"sort"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/decimal"
	gd "github.com/shopspring/decimal"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"

	"bitbucket.org/gatebackend/go-zero/core/logx"
)

type ClientVerifyNftMintLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// web3客户端防女巫、IP验证
func NewClientVerifyNftMintLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ClientVerifyNftMintLogic {
	return &ClientVerifyNftMintLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ClientVerifyNftMintLogic) ClientVerifyNftMint(req *types.ClientVerifyNftMintReq, r *http.Request) (resp *types.ClientVerifyNftMintResp, err error) {
	reqByte, _ := json.Marshal(req)
	if req.Amount < 1 || req.WalletAddress == "" || (req.ParentNftID > 0 && req.NftUserSubTaskID > 0) ||
		(req.ParentNftID <= 0 && req.NftUserSubTaskID <= 0) || (req.NftUserSubTaskID > 0 && req.Amount > 1) {
		logx.Infof("ClientVerifyNftMint req invalid parameter: %s", string(reqByte))
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
	}
	defer func() {
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		logx.Info("ClientVerifyNftMint Req: ", string(reqByte), " resp: ", string(respJson), " err: ", err)
	}()
	redisKey := fmt.Sprintf(consts.NftClientVerifyTimeKey, req.WalletAddress, req.NftUserSubTaskID, req.ParentNftID)
	resp = &types.ClientVerifyNftMintResp{}
	defer func() {
		//任务中验证有15分钟操作限制
		if resp.MintStatus == 2 && req.NftUserSubTaskID > 0 {
			_ = l.svcCtx.Redis.Setex(redisKey, "1", 15*60)
		}
	}()
	redisValue, _ := l.svcCtx.Redis.Get(redisKey)
	if redisValue != "" {
		logx.Infof("ClientVerifyNftMint 15 minutes not finish", string(reqByte))
		resp.MintStatus = 3
		resp.MintStatusCause = "15分钟验证未通过"
		return resp, nil
	}
	if req.NftUserSubTaskID > 0 {
		subTaskDataModel := nft.NewNftUserSubTaskModel(l.svcCtx.DBMarketingActivities)
		subTask, err := subTaskDataModel.GetSubTaskByID(l.ctx, req.NftUserSubTaskID)
		if err != nil {
			logx.Infof("ClientVerifyNftMint GetSubTaskByID  is err, req is:%s,err is:%v", string(reqByte), err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		//任务未完成
		if subTask.Status != 2 {
			logx.Infof("ClientVerifyNftMint subTask not finish, data is:%d,%d", subTask.TaskId, subTask.Status)
			resp.MintStatus = 2
			resp.MintStatusCause = "任务未完成"
			return resp, nil
		}
	}
	//数据库验证
	activity, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftActivity(l.ctx, req.ActivityId, false)
	if err != nil {
		logx.Infof("ClientVerifyNftMint GetNftActivity  is err, req is:%s,err is:%v", string(reqByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	if activity.ActivityId <= 0 {
		logx.Infof("ClientVerifyNftMint activity is null, req is:%s", string(reqByte))
		resp.MintStatus = 2
		resp.MintStatusCause = "活动不存在"
		return resp, nil
	}
	//判断活动是否结束
	if activity.EndTime.Before(time.Now()) {
		logx.Infof("ClientVerifyNftMint activity end, activity.EndTime is:%s", activity.EndTime.Format(consts.GoFormatTimeStr))
		resp.MintStatus = 2
		resp.MintStatusCause = "活动已结束"
		return resp, nil
	}
	//判断活动是否开始
	if activity.StartTime.After(time.Now()) {
		logx.Infof("ClientVerifyNftMint activity not start, activity.StartTime is:%s", activity.StartTime.Format(consts.GoFormatTimeStr))
		resp.MintStatus = 2
		resp.MintStatusCause = "活动未开始"
		return resp, nil
	}
	//判断是否在领取期
	receiveNow := false
	if activity.ReceiveStartTime.Before(time.Now()) && activity.ReceiveEndTime.After(time.Now()) {
		receiveNow = true
	}
	//判断是否在额外活动期
	taskNow := false
	if activity.TaskStartTime.Before(time.Now()) && activity.TaskEndTime.After(time.Now()) {
		taskNow = true
	}
	//领取期parentNftId必传
	if receiveNow && req.ParentNftID <= 0 {
		logx.Infof("ClientVerifyNftMint receiveNow:%s,%s,%d", activity.ReceiveStartTime.Format(consts.GoFormatTimeStr), activity.ReceiveEndTime.Format(consts.GoFormatTimeStr), req.ParentNftID)
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
	}
	//获取nft列表
	nftList := make([]*nft_detail.NftDetail, 0)
	//获取nft列表 领取和购买场景
	if req.ParentNftID > 0 && req.NftUserSubTaskID <= 0 {
		isHighGrade := 1
		nftType := 0
		//查询普通nft
		if req.NftID > 0 {
			isHighGrade = 0
			nftType = 1
		}
		nftList, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftList(l.ctx, req.ActivityId, req.ParentNftID, nftType, isHighGrade)
		if err != nil {
			logx.Infof("ClientVerifyNftMint GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
	}
	//获取nft列表 完成任务铸造nft场景
	if req.NftUserSubTaskID > 0 {
		nftList, err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftList(l.ctx, req.ActivityId, 0, 1, 0)
		if err != nil {
			logx.Infof("ClientVerifyNftMint GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
	}
	if len(nftList) == 0 {
		logx.Infof("ClientVerifyNftMint nftList  is null, req is:%s", string(reqByte))
		resp.MintStatus = 2
		resp.MintStatusCause = "未找到对应NFT"
		return resp, nil
	}
	for _, nftInfo := range nftList {
		if req.NftID > 0 && nftInfo.NftId != req.NftID {
			logx.Infof("ClientVerifyNftMint req.NftID not is null nftList  is null, req is:%s", string(reqByte))
			resp.MintStatus = 2
			resp.MintStatusCause = "nftId错误"
			return resp, nil
		}
	}
	nftId := int64(0)
	var nftPrice decimal.Decimal
	//免费领取期
	if receiveNow {
		nftType := int64(0)
		for _, nftInfo := range nftList {
			nftType = nftInfo.NftType
			if nftInfo.NftType == 1 {
				nftId = nftInfo.NftId
			}
			if nftInfo.NftType != 1 {
				nftPrice = nftInfo.DecNftPrice
			}
			//判断当前parentNftID是否在领取期
			if nftInfo.CampaignStartTime.After(time.Now()) || nftInfo.CampaignEndTime.Before(time.Now()) {
				//当前parentNftID不在领取期
				paramByte, _ := json.Marshal(nftInfo)
				logx.Infof("ClientVerifyNftMint nft not in recevie time, params is:%s", string(paramByte))
				resp.MintStatus = 2
				resp.MintStatusCause = "parentNftId 不在领取期"
				return resp, nil
			}
		}
		mintNftLogs, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftMintLogListByNftId(l.ctx, 0, req.WalletAddress, req.ParentNftID, 0, req.ActivityId, 0, req.NftID)
		if err != nil {
			logx.Infof("ClientVerifyNftMint GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		//同一个普通nft 在可领取期只能领取一次
		if nftType == 1 {
			if len(mintNftLogs) > 0 {
				logx.Infof("ClientVerifyNftMint mintNftLogs len > 0, params is:%d", len(mintNftLogs))
				resp.MintStatus = 2
				resp.MintStatusCause = "parentNftId已有领取记录"
				return resp, nil
			}
		} else {
			for _, mintNftLog := range mintNftLogs {
				//同一个用的同一个nft如果有铸造中的状态也要拦截
				if mintNftLog.MintStatus < 3 && mintNftLog.NftId <= 0 {
					logx.Infof("ClientVerifyNftMint same nft minting data is:%d,%d", mintNftLog.NftId, mintNftLog.MintStatus)
					resp.MintStatus = 2
					resp.MintStatusCause = "same nft in minting"
					return resp, nil
				}
			}
		}
	}
	//额外活动期
	if taskNow {
		nftIdMap := map[int64]int64{}
		nftPriceMap := map[int64]decimal.Decimal{}
		for _, nftInfo := range nftList {
			nftIdMap[nftInfo.ParentNftId] = nftInfo.NftId
			nftPriceMap[nftInfo.ParentNftId] = nftInfo.DecNftPrice
			if nftInfo.NftType != nft_detail.NftTypeNormal {
				logx.Infof("ClientVerifyNftMint task time need basic nft")
				resp.MintStatus = 2
				resp.MintStatusCause = "额外活动期只能购买普通nft"
				return resp, nil
			}
		}
		//查询用户所有铸造记录的nft
		mintNftLogs, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetNftMintLogList(l.ctx, 0, req.WalletAddress, 0, 0, req.ActivityId, 0)
		if err != nil {
			logx.Infof("ClientVerifyNftMint GetNftList  is err, req is:%s,err is:%v", string(reqByte), err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		//正常购买
		if req.NftUserSubTaskID <= 0 {
			nftId = nftIdMap[req.ParentNftID]
			nftPrice = nftPriceMap[req.ParentNftID]
			for _, mintNftLog := range mintNftLogs {
				//检查同一个人的同一个nft是否在铸造中
				if mintNftLog.NftUserSubTaskId <= 0 && mintNftLog.MintStatus < 3 && mintNftLog.ParentNftId == req.ParentNftID && req.NftID == mintNftLog.NftId {
					logx.Infof("ClientVerifyNftMint same nft minting data is:%d,%d", mintNftLog.NftId, mintNftLog.MintStatus)
					resp.MintStatus = 2
					resp.MintStatusCause = "same nft in minting"
					return resp, nil
				}
			}
		} else {
			logParentNftIdMap := map[int64]struct{}{}
			logParentNftIds := []int64{}
			for _, log := range mintNftLogs {
				//增加任务免费领取只能同一时刻只能有一条记录的限制,noPrice是免费的值 0
				if log.NftUserSubTaskId > 0 && log.MintStatus < 3 {
					//额外活动期有正在免费铸造的nft
					logx.Infof("Please do not claim until you have completed the last collection minting")
					resp.MintStatus = 2
					resp.MintStatusCause = "Please do not claim until you have completed the last collection minting"
					return resp, nil
				}
				if log.NftUserSubTaskId == req.NftUserSubTaskID && log.MintStatus == 3 {
					//做任务免费领取一个nft只能领取一次
					logx.Infof("ClientVerifyNftMint task nft has minted")
					resp.MintStatus = 2
					resp.MintStatusCause = "task nft has minted"
					return resp, nil
				}
				noPrice := gd.NewFromFloat(0)
				if _, ok := logParentNftIdMap[log.ParentNftId]; !ok && log.Expenditure.Equal(noPrice) {
					logParentNftIds = append(logParentNftIds, log.ParentNftId)
					logParentNftIdMap[log.ParentNftId] = struct{}{}
				}
			}
			//可以领取的nft
			canReceiveParentNftIds := make([]int64, len(nftList))
			for i, nftInfo := range nftList {
				canReceiveParentNftIds[i] = nftInfo.ParentNftId
			}
			//获取未领取的nft
			parentNftIds := utils.FindUniqueElements(canReceiveParentNftIds, logParentNftIds)
			if len(parentNftIds) == 0 {
				//所有普通nft用户都已领完
				logx.Infof("ClientVerifyNftMint all basic nft claimed")
				resp.MintStatus = 2
				resp.MintStatusCause = "所有的普通nft都已产生领取记录"
				return resp, nil
			}
			//排序
			sort.Slice(parentNftIds, func(i, j int) bool {
				return parentNftIds[i] < parentNftIds[j]
			})
			req.ParentNftID = parentNftIds[0]
			nftId = nftIdMap[req.ParentNftID]
		}
	}
	//用毫秒时间戳定义操作ID
	operationId, _ := utils.GetOrderId(l.ctx, l.svcCtx)
	//保护机制
	if operationId == 0 {
		operationId = time.Now().UnixNano() / 1e3
	}
	//插入领取记录
	ntfMintLogs := make([]*nft_mint_log.NftMintLog, req.Amount)
	for i := 0; i < req.Amount; i++ {
		ntfMintLogs[i] = &nft_mint_log.NftMintLog{
			NftId:            nftId,
			ParentNftId:      req.ParentNftID,
			NftUserSubTaskId: req.NftUserSubTaskID,
			WalletAddress:    req.WalletAddress,
			ActivityId:       req.ActivityId,
			MintStatus:       1,
			PayType:          2,
			OperationId:      operationId,
			Expenditure:      nftPrice,
		}
	}
	err = dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).InsertNftMintLogs(l.ctx, ntfMintLogs)
	if err != nil {
		ntfMintLogsByte, _ := json.Marshal(ntfMintLogs)
		logx.Infof("ClientVerifyNftMint InsertNftMintLogs  is err, ntfMintLogs is:%s,err is:%v", string(ntfMintLogsByte), err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	//删除去中心化铸造记录的缓存
	mintLogKey := fmt.Sprintf(consts.NftWalletAddressMintLogKey, req.WalletAddress, req.ActivityId)
	//删除轮询nft铸造结果的缓存
	rollMintNfrKey := fmt.Sprintf(consts.NftRollMintLogKey, req.ActivityId, 0, req.WalletAddress, operationId)
	_, _ = l.svcCtx.Redis.Del(mintLogKey, rollMintNfrKey)
	resp.MintStatus = 1
	resp.ParentNftID = int(req.ParentNftID)
	resp.OperationID = operationId
	return
}

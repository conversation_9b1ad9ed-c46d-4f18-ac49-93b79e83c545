package test

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"context"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type GetHelloOuterLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 测试接口
func NewGetHelloOuterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetHelloOuterLogic {
	return &GetHelloOuterLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetHelloOuterLogic) GetHelloOuter() (resp *types.TestApiOuterResponse, err error) {
	contractTransformUserTaskInfo, err := l.svcCtx.ContractTransformUserTaskModel.FindOne(l.ctx, 1)
	if err != nil {
		return nil, err
	}

	// 存在并发问题，多个节点同时扫task,用redis加锁
	ok, err := l.svcCtx.Redis.SetnxExCtx(l.ctx, "test-redis123", "redis123-value", 3)
	if err != nil || !ok {
		l.Logger.Warnf("redis设置值失败, ok: %t, err: %v", ok, err)
		return nil, err
	}

	// 存在并发问题，多个节点同时扫task,用redis加锁
	redisValue, err := l.svcCtx.Redis.GetCtx(l.ctx, "test-redis123")
	if err != nil {
		l.Logger.Warnf("redis获取值失败, value: %t, err: %v", redisValue, err)
		return nil, err
	}

	resp = &types.TestApiOuterResponse{
		Message: "外部接口-GetHello--Mysql数据库取到的值：" + contractTransformUserTaskInfo.Source + " | Redis获取到的数据: " + redisValue,
	}

	return resp, nil
}

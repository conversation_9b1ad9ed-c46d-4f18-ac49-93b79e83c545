package test

import (
	"context"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/consts/activity"
	"gateio_service_marketing_activity/internal/mqs/kafka"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type GetTestOuterLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTestOuterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTestOuterLogic {
	return &GetTestOuterLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTestOuterLogic) GetTestOuter(req *types.TestApiOuterRequest) (resp *types.TestApiOuterResponse, err error) {
	if req.BusinessId == 1 {
		// 把业务的任务上报给任务系统
		getTaskTime := time.Now().Unix()
		kafkaProducer, err := kafka.NewKafkaProducer(l.svcCtx.KafkaConf)
		if err != nil {
			logx.Infof("submitTaskRecord failed, step: kafka.NewKafkaProducer, err: %v", err)
			return resp, err
		}
		defer kafkaProducer.Close()

		err = kafkaProducer.TaskRecordReceiveProducer(l.ctx, 2024002250, 934, activity.NftTaskBusinessType, 1, getTaskTime, 0)
		logx.Infof("submitTaskRecord, step: 上报任务系统的消息：, UserId : %d, BusinessType: %d", 934, activity.NftTaskBusinessType)
		if err != nil {
			logx.Infof("submitTaskRecord failed, step: Producer.TaskRecordReceiveProducer, UserId : %d, BusinessId: %d, err: %v", err)
		}
	} else if req.BusinessId == 2 {
		// 模拟回调
		msgStr := "{\"business_type\":15,\"call_type\":\"expire\",\"list\":[{\"business_type\":15,\"business_id\":\"1\",\"user_id\":2024002250,\"task_id\":934,\"only_id\":\"74596287532171935\",\"status\":3,\"done_time\":1739427093,\"conditions\":[{\"prize_type\":2,\"prize_num\":1,\"status\":3,\"prize_ext\":[]}],\"received_type\":1}]}"
		kafka.NftTaskCallBack(l.ctx, l.svcCtx, msgStr)
	}

	return
}

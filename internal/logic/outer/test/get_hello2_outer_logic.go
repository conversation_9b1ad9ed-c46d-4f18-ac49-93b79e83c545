package test

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type GetHello2OuterLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 测试接口
func NewGetHello2OuterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetHello2OuterLogic {
	return &GetHello2OuterLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetHello2OuterLogic) GetHello2Outer() (resp *types.TestApiOuterResponse, err error) {
	/*userTask, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).GetWelfareConfigByName(l.ctx, 0, 0)
	if err != nil {
		return nil, err
	}
	resp = &types.TestApiOuterResponse{
		Message: fmt.Sprintf("外部接口-GetHello2:%d", userTask.TaskId),
	}*/

	resp = &types.TestApiOuterResponse{
		Message: "外部接口-GetHello2",
	}
	return
}

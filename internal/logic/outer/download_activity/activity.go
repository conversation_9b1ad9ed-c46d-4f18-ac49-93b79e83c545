package download_activity

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/usercenter"
	"context"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/consts/download"
	"gateio_service_marketing_activity/internal/models/download_activity"
	"gateio_service_marketing_activity/internal/mqs/kafka"
	"gateio_service_marketing_activity/internal/pkg/common"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"
	"net/http"
	"sort"
	"time"
)

type Activity struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDownloadActivity(ctx context.Context, svcCtx *svc.ServiceContext) *Activity {
	return &Activity{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func mockRecords(records []*types.CouponInfo) []*types.CouponInfo {
	if len(records) < 5 {
		for i := len(records); i < 5; i++ {
			records = append(records, &types.CouponInfo{
				Nickname:   utils.DesensitizeString(fmt.Sprintf("GateUser-%d%d%d%d%d%d", i, i+3, i+1, i+3, i, i+2), 4, false),
				CouponType: download.CouponTypeCommissionRebate,
			})
		}
	}
	return records
}

func convertStatus(ctx context.Context, taskID int, userInfo *usercenter.UserInfoResponse) (int64, error) {
	var status int64 = download.TaskStatusInProgress
	ch := userInfo.UserInfo.FromChannel
	_, ok := download.ChannelMap[ch]
	// 注册活动只要不是这个渠道的，就任务不可以参与
	if taskID == download.REGISTERID && !ok {
		status = download.TaskStatusLostQualification
	}
	// 下载活动，只要有登录过app，就认为不可已参与
	if taskID == download.APPID {
		var lastTime int64
		var err error
		lastTime, err = service.NewUserCenterCall(ctx).GetAppLastTime(userInfo.UID)
		if err != nil {
			logx.WithContext(ctx).Errorf("[Participate]GetAppLastTime err: %v", err)
			return 0, errors.New(consts.ErrSystemError, "please try again!")
		}
		// 只要登录过app，就不可以领取
		if lastTime != 0 {
			status = download.TaskStatusLostQualification
		}
	}
	return status, nil
}

func (a *Activity) uploadActivityRecord(uid, aid int64, channel string) error {
	kafkaProducer, err := kafka.NewKafkaProducer(a.svcCtx.UploadKafkaConf)
	if err != nil {
		logx.Infof("[uploadActivityRecord]NewKafkaProducer err: %v", err)
		return err
	}
	defer kafkaProducer.Close()
	err = kafkaProducer.AdjustUserRecordProducer(context.Background(), uid, aid, time.Now().Unix(), channel)
	if err != nil {
		logx.Infof("[uploadActivityRecord]AdjustUserRecord err: %v", err)
		return err
	}
	return nil
}

func (a *Activity) GetActivityInfo(r *http.Request) (resp *types.ActivityInfoResponse, err error) {
	resp = &types.ActivityInfoResponse{}
	u, _ := common.GetUserInfo(a.ctx)
	// 当前未登录的用户，不会把request设置到ctx里面，所以入错报error就认为用户是未登录的用户
	// 这个接口可以接受用户未登录，所以忽略error信息
	// 只是判断u有没有值，如果有值就认为是登录了
	var uid int64
	var userInfo *usercenter.UserInfoResponse
	if u != nil {
		uid = utils.MustInt64(u.UID)
		userInfo, err = service.NewUserCenterCall(a.ctx).GetUserInfo(uid)
		if err != nil {
			logx.WithContext(r.Context()).Errorf("[GetActivityInfo]uid:%v, GetUserInfo err: %v", uid, err)
			return nil, errors.New(consts.ErrSystemError, "please try again!")
		}
	}
	// 先获取用户的参与记录，如果参与了，就返回他参与的那个活动
	recordModel := download_activity.NewDownloadTaskRecordModel(a.svcCtx.DBMarketingActivities)
	records, err := recordModel.GetTaskRecordsByUID(a.ctx, uid)
	if err != nil {
		logx.WithContext(a.ctx).Errorf("[GetActivityInfo]uid:%v,GetTaskRecordsByUID err: %v", uid, err)
		return nil, errors.New(consts.ErrSystemError, "please try again!")
	}
	var activity *download_activity.DownloadActivity
	activityModel := download_activity.NewDownloadActivityModel(a.svcCtx.DBMarketingActivities)
	if len(records) == 0 {
		// 如果用户没参与的时候，先获取生效中的，如果生效中的不存在的话，再返回最近的一条
		logc.Infof(a.ctx, "uid:%v, not participated", uid)
		activity, err = activityModel.GetValidActivity(a.ctx, time.Now())
		if err != nil {
			logx.WithContext(a.ctx).Errorf("[GetActivityInfo]GetValidActivity err: %v", err)
			return nil, errors.New(consts.ErrSystemError, "please try again!")
		}
		if activity == nil {
			activity, err = activityModel.GetLatestActivity(a.ctx)
			if err != nil {
				logx.WithContext(a.ctx).Errorf("[GetActivityInfo]GetLatestActivity err: %v", err)
				return nil, errors.New(consts.ErrSystemError, "please try again!")
			}
		}

		if activity == nil {
			return nil, errors.New(consts.ErrSystemError, "The activity has not started yet")
		}

	} else {
		// 用户参与的，直接返回他参与的那个活动
		logc.Infof(a.ctx, "uid:%v, participated", uid)
		aid := records[0].Aid

		activity, err = activityModel.FindOne(a.ctx, aid)
		if err != nil {
			logx.WithContext(a.ctx).Errorf("[GetActivityInfo]FindOne err: %v", err)
			return nil, errors.New(consts.ErrSystemError, "please try again!")
		}
		if activity == nil {
			return nil, errors.New(consts.ErrDownloadActivityInvalid, "Invalid activity")
		}

	}

	// 获取最新的五条参与记录
	latestRecords, err := recordModel.FindLatestTaskRecordsByAID(a.ctx, activity.Id, 5, download.TaskStatusInCompleted)
	if err != nil {
		logx.WithContext(a.ctx).Errorf("[GetActivityInfo]activityID:%v,FindLatestTaskRecordsByAID err: %v", activity.Id, err)
		return nil, errors.New(consts.ErrSystemError, "please try again!")
	}

	// 默认活动未生效
	activityStatus := download.ActivityStatusPending

	// 当前时间在活动时间中，则活动是生效中
	if time.Now().After(time.Unix(activity.StartTime, 0)) && time.Now().Before(time.Unix(activity.EndTime, 0)) {
		activityStatus = download.ActivityStatusValid
	}
	// 如果超出了，则认为活动已失效
	if time.Now().After(time.Unix(activity.EndTime, 0)) {
		activityStatus = download.ActivityStatusEnd
	}
	resp.ActivityStatus = activityStatus
	resp.Aid = activity.Id

	resp.IsUserParticipated = len(records) > 0

	resp.StartTime = activity.StartTime
	resp.EndTime = activity.EndTime

	// 这里通过最新的五条获奖记录，转换成最终的结果
	latestCouponInfo := make([]*types.CouponInfo, 0)
	for _, record := range latestRecords {
		latestCouponInfo = append(latestCouponInfo, &types.CouponInfo{
			Nickname:   record.Nickname,
			CouponType: record.CouponType,
		})
	}

	// mock到最少五条数据
	latestCouponInfo = mockRecords(latestCouponInfo)

	resp.LatestCouponInfo = latestCouponInfo
	// 生产用户参与的活动记录
	taskInfos := make([]*types.TaskInfo, 0)
	if len(records) > 0 {

		for _, record := range records {
			var invitedUserNum int64
			if record.TaskId == download.INVITEID {
				invitationModel := download_activity.NewDownloadInvitationRelationModel(a.svcCtx.DBMarketingActivities)
				invitedUserNum, err = invitationModel.GetTotalByRefUid(a.ctx, uid)
				if err != nil {
					logx.WithContext(a.ctx).Errorf("[GetActivityInfo]uid:%v, GetTotalByRefUID err: %v", uid, err)
					return nil, errors.New(consts.ErrSystemError, "please try again!")
				}
			}
			// 当前活动等于50个，就认为任务完成了，就算用户多邀请，也没有关系
			if invitedUserNum > 50 {
				invitedUserNum = 50
			}

			taskInfos = append(taskInfos, &types.TaskInfo{
				TaskID:         int(record.TaskId),
				TaskType:       download.TaskListMap[int(record.TaskId)].Type,
				TaskStatus:     record.Status,
				InvitedUserNum: invitedUserNum,
				Progress:       record.Progress,
			})
		}
	} else {
		for _, task := range download.TaskListMap {
			var status int64 = download.TaskStatusInProgress
			if userInfo != nil {
				status, err = convertStatus(a.ctx, task.Id, userInfo)
				if err != nil {
					logx.WithContext(a.ctx).Errorf("[GetActivityInfo]ConvertStatus err: %v", err)
					return nil, errors.New(consts.ErrSystemError, "please try again!")
				}
			}
			taskInfos = append(taskInfos, &types.TaskInfo{
				TaskID:         task.Id,
				TaskType:       task.Type,
				TaskStatus:     status,
				InvitedUserNum: 0,
				Progress:       0,
			})
		}
	}

	sort.Slice(taskInfos, func(i, j int) bool {
		return taskInfos[i].TaskID < taskInfos[j].TaskID
	})

	resp.TaskInfo = taskInfos

	return
}

func (a *Activity) Participate(r *http.Request, fromChannel string) (resp *types.ParticipateDownloadResponse, err error) {
	u, err := common.GetUserInfo(a.ctx)
	if err != nil {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	if u == nil || u.UID <= 0 {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	uid := utils.MustInt64(u.UID)
	// 认证的@Yang, 建议直接使用用户中心的用户信息，而不是认证中获取的
	userInfo, err := service.NewUserCenterCall(a.ctx).GetUserInfo(uid)
	if err != nil {
		logx.WithContext(a.ctx).Errorf("[Participate]uid:%v,GetUserInfo error: %v", uid, err)
		return nil, errors.New(consts.ErrSystemError, "please try again!")
	}
	// 先获取用户的参与记录，如果参与了，就返回已参与的提示
	recordModel := download_activity.NewDownloadTaskRecordModel(a.svcCtx.DBMarketingActivities)
	records, err := recordModel.GetTaskRecordsByUID(a.ctx, uid)
	if err != nil {
		logx.WithContext(a.ctx).Errorf("uid:%v, Get user task records err: %v", uid, err)
		return nil, errors.New(consts.ErrSystemError, "please try again!")
	}
	if len(records) > 0 {
		return nil, errors.New(consts.ErrDownloadActivityParticipateRepeated, "you had participated!")
	}

	// 获取生效中或者最新的活动
	activityModel := download_activity.NewDownloadActivityModel(a.svcCtx.DBMarketingActivities)
	activity, err := activityModel.GetValidActivity(a.ctx, time.Now())
	if err != nil {
		logx.WithContext(a.ctx).Errorf("[Participate]GetValidActivity err: %v", err)
		return nil, errors.New(consts.ErrSystemError, "please try again!")
	}
	if activity == nil {
		logx.WithContext(a.ctx).Errorf("[Participate]The activity has not started yet")
		return nil, errors.New(consts.ErrDownloadActivityInvalid, "The activity has not started yet")
	}

	records = make([]*download_activity.DownloadTaskRecord, 0)
	for taskID, taskConf := range download.TaskListMap {

		var status int64
		status, err = convertStatus(a.ctx, taskID, userInfo)
		if err != nil {
			logx.WithContext(a.ctx).Errorf("[Participate]convert status err: %v", err)
			return nil, errors.New(consts.ErrSystemError, "please try again!")
		}

		records = append(records, &download_activity.DownloadTaskRecord{
			Aid:        activity.Id,
			Uid:        uid,
			Nickname:   utils.DesensitizeString(userInfo.UserInfo.Nick, 4, false),
			TaskId:     int64(taskID),
			Status:     int64(status),
			Progress:   0,
			CouponType: taskConf.CouponType,
		})
	}
	err = recordModel.BatchInsertRecords(a.ctx, records)
	if err != nil {
		logx.WithContext(a.ctx).Errorf("[Participate]BatchInsert records err: %v", err)
		return nil, errors.New(consts.ErrSystemError, "please try again!")
	}
	// 上报打点，可以接受失败，而且就算错误也不能影响主流程
	go func() {
		defer func() {
			if rec := recover(); rec != nil {
				logx.WithContext(a.ctx).Errorf("[Participate]uploadActivityRecord Recover err: %v", rec)
			}
		}()
		var uploadAID = download.ActivityPlatformIDOnline
		if utils.CheckDev() {
			uploadAID = download.ActivityPlatformIDOffline
		}
		logx.WithContext(a.ctx).Infof("[Participate]uploadActivityRecord uid:%v, uploadAID: %v, fromChannel:%v", uid, uploadAID, fromChannel)
		a.uploadActivityRecord(uid, uploadAID, fromChannel)
	}()
	return
}

func (a *Activity) GetUserNums(req *types.GetUserNumsRequest, r *http.Request) (resp *types.GetUserNumsResponse, err error) {
	resp = &types.GetUserNumsResponse{}
	activityModel := download_activity.NewDownloadActivityModel(a.svcCtx.DBMarketingActivities)
	activity, err := activityModel.GetActivityById(a.ctx, req.AID)
	if err != nil {
		logx.WithContext(a.ctx).Errorf("[GetUserNums]GetActivityById err: %v", err)
		return nil, errors.New(consts.ErrSystemError, "please try again!")
	}

	if activity.Id == 0 {
		logx.WithContext(a.ctx).Errorf("[GetUserNums]GetActivityById Invalid activity, id:%v", req.AID)
		return nil, errors.New(consts.ErrDownloadActivityInvalid, "Invalid activity")
	}

	recordModel := download_activity.NewDownloadTaskRecordModel(a.svcCtx.DBMarketingActivities)

	var cnt int64
	cnt, err = recordModel.CountUsersByAID(a.ctx, req.AID)
	if err != nil {
		logx.WithContext(a.ctx).Errorf("[GetUserNums]CountUsersByAID err: %v", err)
		return nil, errors.New(consts.ErrSystemError, "please try again!")
	}

	// 使用活动上的一个字段来mock一些数据，pm酌情修改
	resp.UserNum = cnt + activity.ExtraUserNum

	return
}

func (a *Activity) CanAutoParticipate(r *http.Request) (resp *types.CanAutoParticipateResponse, err error) {
	resp = &types.CanAutoParticipateResponse{}
	u, err := common.GetUserInfo(a.ctx)
	if err != nil {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	if u == nil || u.UID <= 0 {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	uid := utils.MustInt64(u.UID)
	uid2InfoMap, err := service.NewUserCenterCall(a.ctx).GetUserMap([]int64{uid})
	if err != nil {
		logx.WithContext(a.ctx).Errorf("[CanAutoParticipate]uid:%v, GetUserMap error: %v", uid, err)
		return nil, errors.New(consts.ErrSystemError, "please try again!")
	}
	if _, ok := uid2InfoMap[uid]; !ok {
		logx.WithContext(a.ctx).Errorf("[CanAutoParticipate]GetUserMap uid: %v not existed", uid)
		return nil, errors.New(consts.ErrSystemError, "please try again!")
	}
	// 获取用户的注册渠道和注册时间
	userInfo := uid2InfoMap[uid]
	ch := userInfo.UserInfo.FromChannel
	var regTime time.Time
	regTime, err = time.Parse(time.DateTime, userInfo.Timest)
	if err != nil {
		logx.WithContext(a.ctx).Errorf("[CanAutoParticipate]GetUserMap time.ParseTime err: %v", err)
		return nil, errors.New(consts.ErrSystemError, "please try again!")
	}

	var isCanAutoParticipate bool
	// 对应渠道&小于5分钟才能自动参与
	if _, ok := download.ChannelMap[ch]; ok && regTime.After(time.Now().Add(-5*time.Minute)) {
		isCanAutoParticipate = true
	}
	resp.CanAutoParticipate = isCanAutoParticipate
	return
}

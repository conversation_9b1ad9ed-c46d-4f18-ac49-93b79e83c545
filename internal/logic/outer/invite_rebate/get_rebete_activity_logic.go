package invite_rebate

import (
	"context"
	"sort"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/cc_competitions"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type GetRebeteActivityLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 查询活动列表
func NewGetRebeteActivityLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRebeteActivityLogic {
	return &GetRebeteActivityLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetRebeteActivityLogic) GetRebeteActivity(_ *types.GetRebateActivityReq) (resp *types.GetRebateActivityResp, err error) {
	resp = &types.GetRebateActivityResp{}

	list, err := dao.NewMarketingActivityMysqlDBUtil(l.svcCtx.DBMarketingActivities).QueryRebateActivitiesForUser(l.ctx)
	if err != nil {
		l.Logger.Errorf("QueryRebateActivitiesByParam is fail ，err is:%v", err)
		return nil, err
	}
	var ids []int64
	for _, v := range list {
		ids = append(ids, v.ActivityId)
	}

	comps, err := dao.NewCompetitionsMysqlDBUtil(l.svcCtx.DBCompetitions).QueryCompetitionToC(l.ctx, ids)
	if err != nil {
		l.Logger.Errorf("QueryCompetitionByParam is fail ，err is:%v", err)
		return nil, err
	}

	compsMap := make(map[int64]*cc_competitions.CcCompetitions)
	for _, comp := range comps {
		compsMap[comp.Id] = comp
	}

	resp.List = []*types.Competition{}
	for _, act := range list {
		v, exists := compsMap[act.ActivityId]
		if !exists {
			continue
		}

		titleGroupInfo := types.TitleGroupInfo{
			MasterOneLine:    v.MasterOneLine.String,
			MasterTwoLine:    v.MasterTwoLine.String,
			SlaveOneLine:     v.SlaveOneLine.String,
			SlaveTwoLine:     v.SlaveTwoLine.String,
			CompetitionTitle: v.CompetitionTitle.String,
		}
		extraInfo := types.ExtraInfo{
			TitleGroup: titleGroupInfo,
		}

		topId := int64(0)
		if v.ParentId == 0 {
			topId = v.Id
		} else {
			topId = v.ParentId
		}

		resp.List = append(resp.List, &types.Competition{
			Id:              v.Id,
			Weight:          int(act.Weight),
			ParentId:        v.ParentId,
			Lang:            v.Lang,
			TypeId:          v.TypeId,
			CompetitionName: v.CompetitionName,
			StartAt:         v.StartAt,
			EndAt:           v.EndAt,
			Url:             v.Url,
			Img:             consts.JointActivityImgUrl(v.Img),
			ImgDark:         consts.JointActivityImgUrl(v.ImgDark),
			Hot:             int(v.Hot),
			Status:          int(v.Status),
			DeletedAt:       v.DeletedAt.Time.Unix(),
			TopId:           topId,
			Extra:           extraInfo,
		})
	}

	// 排序逻辑：未开始、进行中、已结束，然后再根据 weight 排序
	currentTime := time.Now().Unix()
	sort.Slice(resp.List, func(i, j int) bool {
		// 获取活动的开始时间和结束时间
		startI := resp.List[i].StartAt
		endI := resp.List[i].EndAt
		startJ := resp.List[i].StartAt
		endJ := resp.List[i].EndAt

		// 判断活动状态：未开始、进行中、已结束
		statusI := getActivityStatus(currentTime, startI, endI)
		statusJ := getActivityStatus(currentTime, startJ, endJ)

		// 优先按活动状态排序
		if statusI != statusJ {
			return statusI < statusJ // 未开始 < 进行中 < 已结束
		}

		// 如果状态相同，则按 weight 排序
		return resp.List[i].Weight > resp.List[j].Weight
	})

	return resp, nil
}

// 获取活动状态：未开始、进行中、已结束
func getActivityStatus(currentTime, startAt, endAt int64) int {
	if currentTime < startAt {
		return consts.InviteRebateUnstart // 未开始
	} else if currentTime >= startAt && currentTime <= endAt {
		return consts.InviteRebateProcessing // 进行中
	} else {
		return consts.InviteRebateEnd // 已结束
	}
}

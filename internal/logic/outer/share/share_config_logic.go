package share

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/language"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/share"
	"strings"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type BaseImage struct {
	WebUseCommon bool              `json:"web_use_common"`
	APPUseCommon bool              `json:"app_use_common"`
	Web          map[string]string `json:"web"`
	APP          map[string]string `json:"app"`
}

type CommonConfig struct {
	WebUseCommon bool   `json:"web_use_common"`
	APPUseCommon bool   `json:"app_use_common"`
	Web          string `json:"web"`
	APP          string `json:"app"`
}

type Config struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

type ShareConfigLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

const SourceCommon = "ALL"
const PlatformAPP = "APP"
const PlatformWEB = "WEB"

// 获取配置
func NewShareConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ShareConfigLogic {
	return &ShareConfigLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ShareConfigLogic) ShareConfig(req *types.GetShareConfigRequest) (resp *types.GetShareConfigResponse, err error) {
	defer func() {
		logc.Infof(l.ctx, "req:%v", req)
		logc.Infof(l.ctx, "resp:%v err:%v", resp, err)
	}()

	resp = &types.GetShareConfigResponse{}
	// 空格清理掉
	req.Source = strings.TrimSpace(req.Source)

	// 先查询缓存，如果缓存存在就直接从缓存中获取
	shareConfigKey := fmt.Sprintf("share_config_%s_%s_%s", req.Source, req.Language, req.Source)
	shareConfigStr, err := l.svcCtx.Redis.GetCtx(l.ctx, shareConfigKey)
	if shareConfigStr != "" && err == nil {
		err = json.Unmarshal([]byte(shareConfigStr), &resp.ShareConfig)
		if err == nil {
			return resp, nil
		} else {
			logc.Warnf(l.ctx, "json.Unmarshal redis err:%v", err)
		}
	} else {
		logc.Warnf(l.ctx, "redis key not found result:%v, err:%v", shareConfigStr, err)
	}

	shareModel := share.NewShareConfigModel(l.svcCtx.DBMarketingActivities)
	sourceShareConfig, err := shareModel.GetShareConfigBySource(l.ctx, req.Source)
	if err != nil {
		logc.Errorf(l.ctx, "[ShareConfig]GetShareConfigBySource err: %v", err)
		return nil, errors.New(consts.ErrSystemError, "please try again!")
	}
	// 如果source配置了自己的，就使用自己的，否则使用通用的
	baseImageObj := BaseImage{}
	unionLogoObj := CommonConfig{}
	tipKeyObj := CommonConfig{}
	var configID int64
	if sourceShareConfig != nil {
		configID = sourceShareConfig.Id

		err = json.Unmarshal([]byte(sourceShareConfig.BaseImage), &baseImageObj)
		if err != nil {
			logc.Errorf(l.ctx, "[ShareConfig]Unmarshal BaseImage err: %v baseImage:%v", err, sourceShareConfig.BaseImage)
			return nil, errors.New(consts.ErrSystemError, "please try again!")
		}
		err = json.Unmarshal([]byte(sourceShareConfig.UnionLogo), &unionLogoObj)
		if err != nil {
			logc.Errorf(l.ctx, "[ShareConfig]Unmarshal UnionLogo err: %v UnionLogo:%v", err, sourceShareConfig.UnionLogo)
			return nil, errors.New(consts.ErrSystemError, "please try again!")
		}
		err = json.Unmarshal([]byte(sourceShareConfig.TipKey), &tipKeyObj)
		if err != nil {
			logc.Errorf(l.ctx, "[ShareConfig]Unmarshal TipKey err: %v TipKey:%v", err, sourceShareConfig.UnionLogo)
			return nil, errors.New(consts.ErrSystemError, "please try again!")
		}
		baseImageUseCommon := false
		unionLogUseCommon := false
		tipKeyUseCommon := false
		if req.Platform == PlatformAPP {
			baseImageUseCommon = baseImageObj.APPUseCommon
			unionLogUseCommon = unionLogoObj.APPUseCommon
			tipKeyUseCommon = tipKeyObj.APPUseCommon
		} else {
			baseImageUseCommon = baseImageObj.WebUseCommon
			unionLogUseCommon = unionLogoObj.WebUseCommon
			tipKeyUseCommon = tipKeyObj.WebUseCommon
		}
		// 如果配置使用通用的，则从通用中获取
		if baseImageUseCommon || unionLogUseCommon || tipKeyUseCommon {
			commonShareConfig, err := shareModel.GetShareConfigBySource(l.ctx, SourceCommon)
			if err != nil {
				logc.Errorf(l.ctx, "[ShareConfig]GetShareConfigBySource:ALL err: %v", err)
				return nil, errors.New(consts.ErrSystemError, "please try again!")
			}
			if commonShareConfig == nil {
				logc.Infof(l.ctx, "[ShareConfig] no common shareConfig")
				return resp, nil
			}
			if baseImageUseCommon {
				err = json.Unmarshal([]byte(commonShareConfig.BaseImage), &baseImageObj)
				if err != nil {
					logc.Errorf(l.ctx, "[ShareConfig]Unmarshal common BaseImage err: %v baseImage:%v", err, commonShareConfig.BaseImage)
					return nil, errors.New(consts.ErrSystemError, "please try again!")
				}
			}
			if unionLogUseCommon {
				err = json.Unmarshal([]byte(commonShareConfig.UnionLogo), &unionLogoObj)
				if err != nil {
					logc.Errorf(l.ctx, "[ShareConfig]Unmarshal common UnionLogo err: %v UnionLogo:%v", err, commonShareConfig.UnionLogo)

					return nil, errors.New(consts.ErrSystemError, "please try again!")
				}
			}
			if tipKeyUseCommon {
				err = json.Unmarshal([]byte(commonShareConfig.TipKey), &tipKeyObj)
				if err != nil {
					logc.Errorf(l.ctx, "[ShareConfig]Unmarshal common TipKey err: %v TipKey:%v", err, commonShareConfig.TipKey)

					return nil, errors.New(consts.ErrSystemError, "please try again!")
				}
			}
		}

	} else {
		commonShareConfig, err := shareModel.GetShareConfigBySource(l.ctx, SourceCommon)
		if err != nil {
			return nil, errors.New(consts.ErrSystemError, "please try again!")
		}
		if commonShareConfig == nil {
			logc.Infof(l.ctx, "[ShareConfig] no common shareConfig")
			return resp, nil
		}
		configID = commonShareConfig.Id

		err = json.Unmarshal([]byte(commonShareConfig.BaseImage), &baseImageObj)
		if err != nil {
			logc.Errorf(l.ctx, "[ShareConfig]Unmarshal common BaseImage err: %v baseImage:%v", err, commonShareConfig.BaseImage)

			return nil, errors.New(consts.ErrSystemError, "please try again!")
		}
		err = json.Unmarshal([]byte(commonShareConfig.UnionLogo), &unionLogoObj)
		if err != nil {
			logc.Errorf(l.ctx, "[ShareConfig]Unmarshal common UnionLogo err: %v UnionLogo:%v", err, commonShareConfig.UnionLogo)

			return nil, errors.New(consts.ErrSystemError, "please try again!")
		}
		err = json.Unmarshal([]byte(commonShareConfig.TipKey), &tipKeyObj)
		if err != nil {
			logc.Errorf(l.ctx, "[ShareConfig]Unmarshal common TipKey err: %v TipKey:%v", err, commonShareConfig.TipKey)
			return nil, errors.New(consts.ErrSystemError, "please try again!")
		}

	}

	// 根据平台获取不同的结果
	var (
		tip       string
		baseImage string
		unionLogo string
	)

	if req.Platform == PlatformAPP {
		tip = language.GetLangDescByKey(tipKeyObj.APP, req.Language)
		baseImage = baseImageObj.APP[req.Language]
		// 如果其他语言没有配置，就默认使用英文
		if len(baseImage) == 0 {
			baseImage = baseImageObj.APP[language.EN]
		}
		unionLogo = unionLogoObj.APP
	} else {
		tip = language.GetLangDescByKey(tipKeyObj.Web, req.Language)
		baseImage = baseImageObj.Web[req.Language]
		// 如果其他语言没有配置，就默认使用英文
		if len(baseImage) == 0 {
			baseImage = baseImageObj.Web[language.EN]
		}
		unionLogo = unionLogoObj.Web
	}

	resp.ShareConfig.ID = configID
	resp.ShareConfig.UnionLogo = unionLogo
	resp.ShareConfig.Tip = tip
	resp.ShareConfig.BaseImage = baseImage

	shareConfigByte, err := json.Marshal(resp.ShareConfig)
	// 只有解析没错的时候，才会缓存
	if err == nil {
		err = l.svcCtx.Redis.Setex(shareConfigKey, string(shareConfigByte), 5*60)
		if err != nil {
			logc.Warnf(l.ctx, "[ShareConfig]Redis err: %v", err)
		}
	} else {
		logc.Warnf(l.ctx, "[ShareConfig]ShareConfig Marshal err: %v", err)
	}

	return resp, nil
}

package brand_partner

import (
	"context"
	"encoding/json"
	"fmt"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/rest/errors"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/brand_partner"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type BrandPartnerDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBrandPartnerDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BrandPartnerDetailLogic {
	return &BrandPartnerDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BrandPartnerDetailLogic) BrandPartnerDetail(req *types.BrandPartnerDetailRequest) (resp *types.BrandPartnerDetailResponse, err error) {
	// 获取品牌商数据
	resp, err = l.GetBrandPartner(req)
	if err != nil {
		return nil, err
	}
	return
}

/**
 * 获取品牌商数据
 */
func (l *BrandPartnerDetailLogic) GetBrandPartner(req *types.BrandPartnerDetailRequest) (resp *types.BrandPartnerDetailResponse, err error) {
	// 查询品牌商数据
	brandPartnerModel := brand_partner.NewBrandPartnerModel(l.svcCtx.DBMarketingActivities)
	brandPartner, err := brandPartnerModel.GetById(l.ctx, req.BrandPartnerId)
	if err != nil {
		return nil, err
	}
	if brandPartner == nil {
		return nil, errors.New(consts.ErrRecordNotExist, "不存在的品牌商ID")
	}

	// 格式化品牌商数据
	resp, err = l.GetBrandPartnerFormat(brandPartner)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

/**
 * 获取品牌商数据 - 格式化
 */
func (l *BrandPartnerDetailLogic) GetBrandPartnerFormat(brandPartner *brand_partner.BrandPartner) (resp *types.BrandPartnerDetailResponse, err error) {
	resp = &types.BrandPartnerDetailResponse{
		PhotoWallConfig:   make([]*types.PhotoWallItem, 0),
		VideoConfig:       make([]*types.VideoItem, 0),
		CompetitionConfig: make([]*types.CompetitionItem, 0),
	}
	if brandPartner == nil {
		return resp, nil
	}
	if len(brandPartner.PhotoWallConfig) > 0 {
		err = json.Unmarshal([]byte(brandPartner.PhotoWallConfig), &resp.PhotoWallConfig)
		if err != nil {
			return resp, fmt.Errorf("json.Unmarshal(brandPartner.PhotoWallConfig) error: %v", err)
		}
	}
	if len(brandPartner.VideoConfig) > 0 {
		tmpVideoConfig := make([]*types.VideoItem, 0)
		err = json.Unmarshal([]byte(brandPartner.VideoConfig), &tmpVideoConfig)
		if err != nil {
			return resp, fmt.Errorf("json.Unmarshal(brandPartner.VideoConfig) error: %v", err)
		}
		for _, video := range tmpVideoConfig {
			if len(video.Url) > 0 && len(video.Poster) > 0 {
				resp.VideoConfig = append(resp.VideoConfig, video)
			}
		}
	}
	if len(brandPartner.CompetitionConfig) > 0 {
		err = json.Unmarshal([]byte(brandPartner.CompetitionConfig), &resp.CompetitionConfig)
		if err != nil {
			return resp, fmt.Errorf("json.Unmarshal(brandPartner.CompetitionConfig) error: %v", err)
		}
	}
	return resp, nil
}

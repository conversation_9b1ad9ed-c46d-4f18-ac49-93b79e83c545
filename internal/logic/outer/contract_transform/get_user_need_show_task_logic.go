package contract_transform

/* import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/engine/auth"
	"context"
	"gateio_service_marketing_activity/internal/consts/activity"
	"gateio_service_marketing_activity/internal/pkg/common"
	"gateio_service_marketing_activity/internal/pkg/contract_transform"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"net/http"
	"strconv"
	"sync"
)

type GetUserNeedShowTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserNeedShowTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserNeedShowTaskLogic {
	return &GetUserNeedShowTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserNeedShowTaskLogic) GetUserNeedShowTask(r *http.Request, userInfo *auth.UserInfo) (resp *types.GetUserNeedShowTaskResponse, err error) {
	//userInfo.UID = 18076884 // todo 自测使用，后面需删除 -- 新用户
	//userInfo.UID = 2328 // todo 自测使用，后面需删除 -- 沉默人群
	// 初始化返回值
	resp = &types.GetUserNeedShowTaskResponse{
		ActivityId: activity.ContractTransformActivityId,
	}

	// 人群数组
	crowdIds := activity.GetNeedCheckCrowdIds()
	// 获取业务任务id与任务系统任务id映射
	taskAndSysTaskMap := activity.GetTaskAndSysTaskMap()

	var wg sync.WaitGroup
	wg.Add(3)
	// 并发请求验证用户是否在对应的人群当中
	var checkInCrowdOne bool
	go contract_transform.CheckUserInCrowd(l.ctx, l.svcCtx, &wg, crowdIds[0], userInfo.UID, &checkInCrowdOne)
	var checkInCrowdTwo bool
	go contract_transform.CheckUserInCrowd(l.ctx, l.svcCtx, &wg, crowdIds[1], userInfo.UID, &checkInCrowdTwo)
	var checkInCrowdThree bool
	go contract_transform.CheckUserInCrowd(l.ctx, l.svcCtx, &wg, crowdIds[2], userInfo.UID, &checkInCrowdThree)
	wg.Wait()

	taskType := activity.ShowTaskTypeCouponUser // 展示卡劵任务
	switch {
	case checkInCrowdOne == true:
		isDoContractDeal, _ := contract_transform.CheckUserIsContractDeal(l.ctx, userInfo.UID)
		if isDoContractDeal == 1 {
			taskType = activity.ShowTaskTypeCouponUser // 新用户未合约交易对应的阶梯任务
		} else {
			taskType = activity.ShowTaskTypeAlreadyContractUser // 已合约交易的新用户对应的阶梯任务
		}
	case checkInCrowdTwo == true:
		taskType = activity.ShowTaskTypeSilenceUser // 沉默用户对应的阶梯任务
	case checkInCrowdThree == true:
		taskType = activity.ShowTaskTypeBlastUser // 展示签到打卡任务
	default:
		taskType = activity.ShowTaskTypeNotInCrowdUser // 不展示任务
	}

	// 默认任务id
	var taskId int64 = 0
	var sysId int64 = 0
	if taskType == activity.ShowTaskTypeAlreadyContractUser {
		taskId = activity.NoContractNewUserTaskId
		sysId = taskAndSysTaskMap[taskId]
	} else if taskType == activity.ShowTaskTypeSilenceUser {
		taskId = activity.SilenceUserTaskId
		sysId = taskAndSysTaskMap[taskId]
	}

	// 是否领取过对应任务
	var isGetTask int64 = 0 // 未领取

	// 获取用户签到数据
	var wg1 sync.WaitGroup
	wg1.Add(1)
	go getActivityCheckInDataDetail(l, r, &wg1, resp, activity.ContractTransformActivityId, int64(userInfo.UID))

	// 查询当前用户是否领取过对应的合约交易阶梯任务
	if taskId != 0 {
		contractTransformUserTaskInfo, err := l.svcCtx.ContractTransformUserTaskModel.FindOneByConditions(l.ctx, int64(userInfo.UID), taskId)
		if err == nil { // 查到用户已领取任务呢
			isGetTask = 1
			userTaskSchedule, _ := contract_transform.GetUserTaskSchedule(l.ctx, l.svcCtx, int64(userInfo.UID), sysId, activity.ContractTransformTaskBusinessType, strconv.FormatInt(contractTransformUserTaskInfo.Id, 10))
			resp.UserAmountTotal = userTaskSchedule.AmountTotal
		}
	}

	// 获取任务本身详情数据
	taskSysTaskInfoList, err := contract_transform.GetTaskSysTaskInfoList(l.ctx, l.svcCtx, activity.MockUserId, strconv.FormatInt(sysId, 10), activity.ContractTransformTaskBusinessType, activity.MockBusinessId)
	if err == nil && len(taskSysTaskInfoList.List) > 0 {
		resp.PrizeNum = taskSysTaskInfoList.List[0].PrizeNum
		if len(taskSysTaskInfoList.List[0].RuleInfo) > 0 {
			taskRange := make([]types.TaskRange, 0, 2)
			var taskPrizeNum int64 = 0
			var taskAmount float64 = 0
			for _, v := range taskSysTaskInfoList.List[0].RuleInfo[0].Conditions {
				taskRange = append(taskRange, types.TaskRange{
					PrizeNum: v.PrizeNum,
					Max:      v.Min,
				})
				taskPrizeNum = taskPrizeNum + v.PrizeNum
				taskAmount = float64(v.Min)
			}
			resp.TaskRanges = taskRange
			resp.PrizeNum = taskPrizeNum
			resp.TaskAmount = taskAmount

			for _, v := range taskSysTaskInfoList.List[0].RuleInfo[0].Conditions {
				if resp.UserAmountTotal == 0 {
					break
				}
				if resp.UserAmountTotal == float64(v.Min) {
					continue
				}

				// 判断用户已经交易额度
				if resp.UserAmountTotal < float64(v.Min) {
					resp.UserNeedDealAmount = float64(v.Min) - resp.UserAmountTotal
					resp.PrizeNum = v.PrizeNum
					break
				}
			}
		}
	}

	wg1.Wait()
	resp.TaskType = int64(taskType)
	resp.IsGetTask = isGetTask
	resp.TaskId = taskId
	resp.ActivityStartTime = activity.ContractTransformActivityStartTimeInt
	resp.ActivityEndTime = activity.ContractTransformActivityEndTimeInt
	return
}

// 获取签到任务数据详情
func getActivityCheckInDataDetail(l *GetUserNeedShowTaskLogic, r *http.Request, wg *sync.WaitGroup, resp *types.GetUserNeedShowTaskResponse, activityId int64, userId int64) {
	defer wg.Done()
	// 获取用户签到任务数据
	activityCheckInCtx := common.NewActivityCheckInData(l.ctx, l.svcCtx, r)
	checkInTaskData, err := activityCheckInCtx.GetActivityCheckInData(activityId, userId)
	if err == nil {
		list := make([]types.ContractTransformCheckInTask, 0, 100)
		for _, v := range checkInTaskData.List {
			temp := types.ContractTransformCheckInTask{
				Number:            v.Number,
				PrizeType:         v.PrizeType,
				PrizeTypeNum:      v.PrizeTypeNum,
				PrizeTypeNumRatio: v.PrizeTypeNumRatio,
				IsCheckIn:         v.IsCheckIn,
			}
			list = append(list, temp)
		}
		checkInTaskDataTemp := types.ActivityCheckInData{
			Info: checkInTaskData.Info,
			List: list,
		}
		resp.CheckInTaskData = checkInTaskDataTemp
	}
	return
}
*/

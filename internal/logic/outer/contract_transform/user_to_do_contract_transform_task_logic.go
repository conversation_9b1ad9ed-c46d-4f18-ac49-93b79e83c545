package contract_transform

/* import (
	"bitbucket.org/gateio/gateio-lib-common-go/engine/auth"
	"context"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/consts/activity"
	"gateio_service_marketing_activity/internal/models/contract_transform_model"
	"gateio_service_marketing_activity/internal/mqs/kafka"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

type UserToDoContractTransformTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUserToDoContractTransformTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UserToDoContractTransformTaskLogic {
	return &UserToDoContractTransformTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UserToDoContractTransformTaskLogic) UserToDoContractTransformTask(req *types.UserToDoContractTransformTaskRequest, userInfo *auth.UserInfo) (resp *types.UserToDoContractTransformTaskResponse, err error) {
	// 查询用户是否领取过当前任务
	_, err = l.svcCtx.ContractTransformUserTaskModel.FindOneByConditions(l.ctx, int64(userInfo.UID), 0)
	if err == nil { // 查到了
		return nil, nil
	}

	taskAndSysTaskMap := activity.GetTaskAndSysTaskMap()

	// 没领取，插入数据库
	insertData := &contract_transform_model.ContractTransformUserTask{
		UserId: int64(userInfo.UID),
		TaskId: req.TaskId,
		SysId:  taskAndSysTaskMap[req.TaskId],
		Status: consts.TaskStatusNotFinish,
		Source: activity.ContractTransformTaskSource,
	}
	id, err := l.svcCtx.ContractTransformUserTaskModel.InsertGetId(l.ctx, insertData)
	if err != nil {
		return nil, err
	}

	// 把业务的任务上报给任务系统
	getTaskTime := time.Now().Unix()
	kafkaProducer, err := kafka.NewKafkaProducer(l.svcCtx.KafkaConf)
	defer kafkaProducer.Close()
	if err != nil {
		logx.Infof("UserToDoContractTransformTask failed, step: kafka.NewKafkaProducer, UserId : %d, BusinessId: %d, err: %v", userInfo.UID, id, err)
	} else {
		err = kafkaProducer.TaskRecordReceiveProducer(l.ctx, int64(userInfo.UID), taskAndSysTaskMap[req.TaskId], activity.ContractTransformTaskBusinessType, id, getTaskTime, 0)
		logx.Infof("UserToDoContractTransformTask, step: 上报任务系统的消息：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d", userInfo.UID, id, taskAndSysTaskMap[req.TaskId], activity.ContractTransformTaskBusinessType)
		if err != nil {
			logx.Infof("UserToDoContractTransformTask failed, step: Producer.TaskRecordReceiveProducer, UserId : %d, BusinessId: %d, err: %v", userInfo.UID, id, err)
		}
	}

	return nil, nil
}
*/

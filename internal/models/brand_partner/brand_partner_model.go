package brand_partner

import (
	"context"
	"database/sql"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                   = gormcsql.InitField
	_ BrandPartnerModel = (*customBrandPartnerModel)(nil)
)

type (
	// BrandPartnerModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBrandPartnerModel.
	BrandPartnerModel interface {
		brandPartnerModel
		customBrandPartnerLogicModel
	}

	customBrandPartnerLogicModel interface {
		WithSession(tx *gorm.DB) BrandPartnerModel
		GetById(ctx context.Context, id int64) (*BrandPartner, error)
	}

	customBrandPartnerModel struct {
		*defaultBrandPartnerModel
	}
)

func (c customBrandPartnerModel) WithSession(tx *gorm.DB) BrandPartnerModel {
	newModel := *c.defaultBrandPartnerModel
	c.defaultBrandPartnerModel = &newModel
	c.conn = tx
	return c
}

// NewBrandPartnerModel returns a model for the database table.
func NewBrandPartnerModel(conn *gorm.DB) BrandPartnerModel {
	return &customBrandPartnerModel{
		defaultBrandPartnerModel: newBrandPartnerModel(conn),
	}
}

/**
 * 根据ID - 获取品牌合作商配置
 */
func (m *defaultBrandPartnerModel) GetById(ctx context.Context, id int64) (*BrandPartner, error) {
	var resp BrandPartner
	err := m.conn.WithContext(ctx).Model(&BrandPartner{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err != nil && err != ErrNotFound {
		return nil, err
	}
	if err == ErrNotFound {
		return nil, nil
	}
	return &resp, err
}

// Code generated by goctl. DO NOT EDIT.

package brand_partner

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	brandPartnerModel interface {
		Insert(ctx context.Context, data *BrandPartner) error

		FindOne(ctx context.Context, id int64) (*BrandPartner, error)
		Update(ctx context.Context, data *BrandPartner) error

		Delete(ctx context.Context, id int64) error
	}

	defaultBrandPartnerModel struct {
		conn  *gorm.DB
		table string
	}

	BrandPartner struct {
		Id                int64          `gorm:"column:id"`                 // 合作商ID
		Name              string         `gorm:"column:name"`               // 合作商名称
		PhotoWallConfig   string 	     `gorm:"column:photo_wall_config"`  // 照片墙配置
		VideoConfig       string         `gorm:"column:video_config"`       // 视频配置
		CompetitionConfig string         `gorm:"column:competition_config"` // 赛事配置
		Description       string         `gorm:"column:description"`        // 合作商描述
		IsDefault         int64          `gorm:"column:is_default"`         // 是否默认展示：0否、1是
		CreateTime        time.Time      `gorm:"column:create_time"`        // 创建时间
		UpdateTime        time.Time      `gorm:"column:update_time"`        // 修改时间
		IsDeleted         int64          `gorm:"column:is_deleted"`         // 是否删除：0未删除、1已删除
	}
)

var QBrandPartner BrandPartner

func init() {
	gormcsql.InitField(&QBrandPartner)
}

func (BrandPartner) TableName() string {
	return "`brand_partner`"
}

func newBrandPartnerModel(conn *gorm.DB) *defaultBrandPartnerModel {
	return &defaultBrandPartnerModel{
		conn:  conn,
		table: "`brand_partner`",
	}
}

func (m *defaultBrandPartnerModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&BrandPartner{}, id).Error

	return err
}

func (m *defaultBrandPartnerModel) FindOne(ctx context.Context, id int64) (*BrandPartner, error) {
	var resp BrandPartner
	err := m.conn.WithContext(ctx).Model(&BrandPartner{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultBrandPartnerModel) Insert(ctx context.Context, data *BrandPartner) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultBrandPartnerModel) Update(ctx context.Context, data *BrandPartner) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

// Code generated by goctl. DO NOT EDIT.

package help_invite_task

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	helpInviteTaskModel interface {
		Insert(ctx context.Context, data *HelpInviteTask) error

		FindOne(ctx context.Context, id int64) (*HelpInviteTask, error)
		Update(ctx context.Context, data *HelpInviteTask) error

		Delete(ctx context.Context, id int64) error
	}

	defaultHelpInviteTaskModel struct {
		conn  *gorm.DB
		table string
	}

	HelpInviteTask struct {
		Id              int64          `gorm:"column:id"`
		TaskId          int64          `gorm:"column:task_id"`            // 任务系统-任务id
		RelationId      int64          `gorm:"column:relation_id"`        // 邀请记录表id
		Uid             int64          `gorm:"column:uid"`                // 用户ID
		RefInviteTaskId int64          `gorm:"column:ref_invite_task_id"` // 邀请人任务ID
		TaskType        int64          `gorm:"column:task_type"`          // 任务类型:1: 单次任务, 2: 多次领取任务
		IsInviterTask   int64          `gorm:"column:is_inviter_task"`    // 是否邀请人任务:0被邀请人, 1邀请人任务
		ConfId          int64          `gorm:"column:conf_id"`            // 配置ID
		TaskBusinessId  string         `gorm:"column:task_business_id"`   // 任务中心bussinessID
		ReportStatus    int64          `gorm:"column:report_status"`      // 0: 初始状态, 1: 任务上报成功, 2: 任务已完成, 3: 任务已失效
		Status          int64          `gorm:"column:status"`             // 邀请人任务状态 1未报名 2待助力 3助力成功 4助力未完成 5已领奖 6命中风控;被邀请人状态 0未创建 1注册 2入金/交易 3已完成 4已结束 5命中风控
		ExtraData       sql.NullString `gorm:"column:extra_data"`         // 扩展信息 被邀请人的几个进度的状态
		ProcessNum      int64          `gorm:"column:process_num"`        // 邀请人数
		Valid           int64          `gorm:"column:valid"`              // 是否有效
		PrizeId         int64          `gorm:"column:prize_id"`           // 奖励id
		PrizeExtraInfo  sql.NullString `gorm:"column:prize_extra_info"`   // 奖励扩展信息
		PrizeType       int64          `gorm:"column:prize_type"`         // 奖励类型
		BusinessType    int64          `gorm:"column:business_type"`      // 业务类型
		EndTime         int64          `gorm:"column:end_time"`           // 结束时间
		FinishTime      int64          `gorm:"column:finish_time"`        // 完成时间
		CreatedAt       time.Time      `gorm:"column:created_at"`         // 创建时间
		UpdatedAt       time.Time      `gorm:"column:updated_at"`         // 更新时间
	}
)

var QHelpInviteTask HelpInviteTask

func init() {
	gormcsql.InitField(&QHelpInviteTask)
}

func (HelpInviteTask) TableName() string {
	return "`help_invite_task`"
}

func newHelpInviteTaskModel(conn *gorm.DB) *defaultHelpInviteTaskModel {
	return &defaultHelpInviteTaskModel{
		conn:  conn,
		table: "`help_invite_task`",
	}
}

func (m *defaultHelpInviteTaskModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&HelpInviteTask{}, id).Error

	return err
}

func (m *defaultHelpInviteTaskModel) FindOne(ctx context.Context, id int64) (*HelpInviteTask, error) {
	var resp HelpInviteTask
	err := m.conn.WithContext(ctx).Model(&HelpInviteTask{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultHelpInviteTaskModel) Insert(ctx context.Context, data *HelpInviteTask) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultHelpInviteTaskModel) Update(ctx context.Context, data *HelpInviteTask) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

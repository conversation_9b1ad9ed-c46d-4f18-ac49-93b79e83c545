package help_invite_task

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                     = gormcsql.InitField
	_ HelpInviteTaskModel = (*customHelpInviteTaskModel)(nil)
)

type (
	// HelpInviteTaskModel is an interface to be customized, add more methods here,
	// and implement the added methods in customHelpInviteTaskModel.
	HelpInviteTaskModel interface {
		helpInviteTaskModel
		customHelpInviteTaskLogicModel
	}

	customHelpInviteTaskLogicModel interface {
		WithSession(tx *gorm.DB) HelpInviteTaskModel
	}

	customHelpInviteTaskModel struct {
		*defaultHelpInviteTaskModel
	}
)

func (c customHelpInviteTaskModel) WithSession(tx *gorm.DB) HelpInviteTaskModel {
	newModel := *c.defaultHelpInviteTaskModel
	c.defaultHelpInviteTaskModel = &newModel
	c.conn = tx
	return c
}

// NewHelpInviteTaskModel returns a model for the database table.
func NewHelpInviteTaskModel(conn *gorm.DB) HelpInviteTaskModel {
	return &customHelpInviteTaskModel{
		defaultHelpInviteTaskModel: newHelpInviteTaskModel(conn),
	}
}

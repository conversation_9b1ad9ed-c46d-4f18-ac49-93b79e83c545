// Code generated by goctl. DO NOT EDIT.

package reclaim_prize_record

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	reclaimPrizeRecordModel interface {
		Insert(ctx context.Context, data *ReclaimPrizeRecord) error

		FindOne(ctx context.Context, id int64) (*ReclaimPrizeRecord, error)
		Update(ctx context.Context, data *ReclaimPrizeRecord) error

		Delete(ctx context.Context, id int64) error
	}

	defaultReclaimPrizeRecordModel struct {
		conn  *gorm.DB
		table string
	}

	ReclaimPrizeRecord struct {
		Id            int64          `gorm:"column:id"`
		PrizeInfoId   int64          `gorm:"column:prize_info_id"`  // 中奖信息表的id
		Uid           int64          `gorm:"column:uid"`            // 兑奖用户
		WalletAddress string         `gorm:"column:wallet_address"` // 兑奖用户钱包地址
		PrizeCategory int64          `gorm:"column:prize_category"` // 1: 实物奖励 2：虚拟奖励
		PrizeType     int64          `gorm:"column:prize_type"`     // 奖品类型
		ReclaimInfo   sql.NullString `gorm:"column:reclaim_info"`   // 兑奖信息
		CreatedAt     time.Time      `gorm:"column:created_at"`
		UpdatedAt     time.Time      `gorm:"column:updated_at"`
	}
)

var QReclaimPrizeRecord ReclaimPrizeRecord

func init() {
	gormcsql.InitField(&QReclaimPrizeRecord)
}

func (ReclaimPrizeRecord) TableName() string {
	return "`reclaim_prize_record`"
}

func newReclaimPrizeRecordModel(conn *gorm.DB) *defaultReclaimPrizeRecordModel {
	return &defaultReclaimPrizeRecordModel{
		conn:  conn,
		table: "`reclaim_prize_record`",
	}
}

func (m *defaultReclaimPrizeRecordModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&ReclaimPrizeRecord{}, id).Error

	return err
}

func (m *defaultReclaimPrizeRecordModel) FindOne(ctx context.Context, id int64) (*ReclaimPrizeRecord, error) {
	var resp ReclaimPrizeRecord
	err := m.conn.WithContext(ctx).Model(&ReclaimPrizeRecord{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultReclaimPrizeRecordModel) Insert(ctx context.Context, data *ReclaimPrizeRecord) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultReclaimPrizeRecordModel) Update(ctx context.Context, data *ReclaimPrizeRecord) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

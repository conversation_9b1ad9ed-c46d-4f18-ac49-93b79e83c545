package reclaim_prize_record

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"errors"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                         = gormcsql.InitField
	_ ReclaimPrizeRecordModel = (*customReclaimPrizeRecordModel)(nil)
)

type (
	// ReclaimPrizeRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customReclaimPrizeRecordModel.
	ReclaimPrizeRecordModel interface {
		reclaimPrizeRecordModel
		customReclaimPrizeRecordLogicModel
	}

	customReclaimPrizeRecordLogicModel interface {
		WithSession(tx *gorm.DB) ReclaimPrizeRecordModel
		GetRecordByPrizeInfoID(ctx context.Context, prizeInfoID int64) (*ReclaimPrizeRecord, error)
		ListRecordByPrizeInfoIDList(ctx context.Context, prizeInfoIDList []int64) ([]*ReclaimPrizeRecord, error)
	}

	customReclaimPrizeRecordModel struct {
		*defaultReclaimPrizeRecordModel
	}
)

func (c customReclaimPrizeRecordModel) WithSession(tx *gorm.DB) ReclaimPrizeRecordModel {
	newModel := *c.defaultReclaimPrizeRecordModel
	c.defaultReclaimPrizeRecordModel = &newModel
	c.conn = tx
	return c
}

func (c *defaultReclaimPrizeRecordModel) GetRecordByPrizeInfoID(ctx context.Context, prizeInfoID int64) (*ReclaimPrizeRecord, error) {
	var resp ReclaimPrizeRecord
	err := c.conn.WithContext(ctx).Model(&ReclaimPrizeRecord{}).Where("prize_info_id=?", prizeInfoID).Take(&resp).Error
	if errors.Is(err, ErrNotFound) {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

func (c *defaultReclaimPrizeRecordModel) ListRecordByPrizeInfoIDList(ctx context.Context, prizeInfoIDList []int64) ([]*ReclaimPrizeRecord, error) {
	var resp []*ReclaimPrizeRecord
	err := c.conn.WithContext(ctx).Model(&ReclaimPrizeRecord{}).Where("prize_info_id in ?", prizeInfoIDList).Find(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// NewReclaimPrizeRecordModel returns a model for the database table.
func NewReclaimPrizeRecordModel(conn *gorm.DB) ReclaimPrizeRecordModel {
	return &customReclaimPrizeRecordModel{
		defaultReclaimPrizeRecordModel: newReclaimPrizeRecordModel(conn),
	}
}

package invite_rebate_activity

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                           = gormcsql.InitField
	_ InviteRebateActivityModel = (*customInviteRebateActivityModel)(nil)
)

type (
	// InviteRebateActivityModel is an interface to be customized, add more methods here,
	// and implement the added methods in customInviteRebateActivityModel.
	InviteRebateActivityModel interface {
		inviteRebateActivityModel
		customInviteRebateActivityLogicModel
	}

	customInviteRebateActivityLogicModel interface {
		WithSession(tx *gorm.DB) InviteRebateActivityModel
	}

	customInviteRebateActivityModel struct {
		*defaultInviteRebateActivityModel
	}
)

func (c customInviteRebateActivityModel) WithSession(tx *gorm.DB) InviteRebateActivityModel {
	newModel := *c.defaultInviteRebateActivityModel
	c.defaultInviteRebateActivityModel = &newModel
	c.conn = tx
	return c
}

// NewInviteRebateActivityModel returns a model for the database table.
func NewInviteRebateActivityModel(conn *gorm.DB) InviteRebateActivityModel {
	return &customInviteRebateActivityModel{
		defaultInviteRebateActivityModel: newInviteRebateActivityModel(conn),
	}
}

// Code generated by goctl. DO NOT EDIT.

package invite_rebate_activity

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	inviteRebateActivityModel interface {
		Insert(ctx context.Context, data *InviteRebateActivity) error

		FindOne(ctx context.Context, id int64) (*InviteRebateActivity, error)
		Update(ctx context.Context, data *InviteRebateActivity) error

		Delete(ctx context.Context, id int64) error
	}

	defaultInviteRebateActivityModel struct {
		conn  *gorm.DB
		table string
	}

	InviteRebateActivity struct {
		Id           int64     `gorm:"column:id"`
		ActivityId   int64     `gorm:"column:activity_id"`   // 活动id
		Weight       int64     `gorm:"column:weight"`        // 权重
		ConfigStatus int64     `gorm:"column:config_status"` // 状态 0禁用 1启用
		CreatedAt    time.Time `gorm:"column:created_at"`    // 创建时间
		UpdatedAt    time.Time `gorm:"column:updated_at"`    // 更新时间
	}
)

var QInviteRebateActivity InviteRebateActivity

func init() {
	gormcsql.InitField(&QInviteRebateActivity)
}

func (InviteRebateActivity) TableName() string {
	return "`invite_rebate_activity`"
}

func newInviteRebateActivityModel(conn *gorm.DB) *defaultInviteRebateActivityModel {
	return &defaultInviteRebateActivityModel{
		conn:  conn,
		table: "`invite_rebate_activity`",
	}
}

func (m *defaultInviteRebateActivityModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&InviteRebateActivity{}, id).Error

	return err
}

func (m *defaultInviteRebateActivityModel) FindOne(ctx context.Context, id int64) (*InviteRebateActivity, error) {
	var resp InviteRebateActivity
	err := m.conn.WithContext(ctx).Model(&InviteRebateActivity{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultInviteRebateActivityModel) Insert(ctx context.Context, data *InviteRebateActivity) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultInviteRebateActivityModel) Update(ctx context.Context, data *InviteRebateActivity) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

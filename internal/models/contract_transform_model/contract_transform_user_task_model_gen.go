// Code generated by goctl. DO NOT EDIT.

package contract_transform_model

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	contractTransformUserTaskModel interface {
		Insert(ctx context.Context, data *ContractTransformUserTask) error

		FindOne(ctx context.Context, id int64) (*ContractTransformUserTask, error)
		Update(ctx context.Context, data *ContractTransformUserTask) error

		Delete(ctx context.Context, id int64) error
	}

	defaultContractTransformUserTaskModel struct {
		conn  *gorm.DB
		table string
	}

	ContractTransformUserTask struct {
		Id        int64     `gorm:"column:id"`         // 主键id
		UserId    int64     `gorm:"column:user_id"`    // 用户id
		TaskId    int64     `gorm:"column:task_id"`    // 任务id
		SysId     int64     `gorm:"column:sys_id"`     // 任务系统id
		Status    int64     `gorm:"column:status"`     // 任务状态: 0-未完成,1-进行中,2-已完成,3-结算中,4-已结算,5-已过期
		Source    string    `gorm:"column:source"`     // 任务来源
		FinishNum int64     `gorm:"column:finish_num"` // 任务完成次数
		CreatedAt time.Time `gorm:"column:created_at"` // 创建时间
		UpdatedAt time.Time `gorm:"column:updated_at"` // 修改时间
	}
)

var QContractTransformUserTask ContractTransformUserTask

func init() {
	gormcsql.InitField(&QContractTransformUserTask)
}

func (ContractTransformUserTask) TableName() string {
	return "`contract_transform_user_task`"
}

func newContractTransformUserTaskModel(conn *gorm.DB) *defaultContractTransformUserTaskModel {
	return &defaultContractTransformUserTaskModel{
		conn:  conn,
		table: "`contract_transform_user_task`",
	}
}

func (m *defaultContractTransformUserTaskModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&ContractTransformUserTask{}, id).Error

	return err
}

func (m *defaultContractTransformUserTaskModel) FindOne(ctx context.Context, id int64) (*ContractTransformUserTask, error) {
	var resp ContractTransformUserTask
	err := m.conn.WithContext(ctx).Model(&ContractTransformUserTask{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultContractTransformUserTaskModel) Insert(ctx context.Context, data *ContractTransformUserTask) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultContractTransformUserTaskModel) Update(ctx context.Context, data *ContractTransformUserTask) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

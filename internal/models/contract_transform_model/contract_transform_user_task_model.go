package contract_transform_model

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"database/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                                = gormcsql.InitField
	_ ContractTransformUserTaskModel = (*customContractTransformUserTaskModel)(nil)
)

type (
	// ContractTransformUserTaskModel is an interface to be customized, add more methods here,
	// and implement the added methods in customContractTransformUserTaskModel.
	ContractTransformUserTaskModel interface {
		contractTransformUserTaskModel
		customContractTransformUserTaskLogicModel
		FindOneByConditions(ctx context.Context, userId int64, taskId int64) (*ContractTransformUserTask, error)
		InsertGetId(ctx context.Context, data *ContractTransformUserTask) (int64, error)
		UpdateOneById(ctx context.Context, id int64, updateMap map[string]interface{}) error
	}

	customContractTransformUserTaskLogicModel interface {
		WithSession(tx *gorm.DB) ContractTransformUserTaskModel
	}

	customContractTransformUserTaskModel struct {
		*defaultContractTransformUserTaskModel
	}
)

func (c customContractTransformUserTaskModel) WithSession(tx *gorm.DB) ContractTransformUserTaskModel {
	newModel := *c.defaultContractTransformUserTaskModel
	c.defaultContractTransformUserTaskModel = &newModel
	c.conn = tx
	return c
}

// NewContractTransformUserTaskModel returns a model for the database table.
func NewContractTransformUserTaskModel(conn *gorm.DB) ContractTransformUserTaskModel {
	return &customContractTransformUserTaskModel{
		defaultContractTransformUserTaskModel: newContractTransformUserTaskModel(conn),
	}
}

func (c customContractTransformUserTaskModel) FindOneByConditions(ctx context.Context, userId int64, taskId int64) (*ContractTransformUserTask, error) {
	resp := &ContractTransformUserTask{}

	query := c.conn.WithContext(ctx).Model(ContractTransformUserTask{})
	// 根据条件动态构建查询
	if userId > 0 {
		query = query.Where("`user_id` = ?", userId)
	}
	if taskId > 0 {
		query = query.Where("`task_id` = ?", taskId)
	}
	err := query.First(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c customContractTransformUserTaskModel) InsertGetId(ctx context.Context, data *ContractTransformUserTask) (int64, error) {
	query := c.conn.WithContext(ctx).Model(ContractTransformUserTask{})
	// 执行插入操作
	result := query.Create(&data)
	if result.Error != nil {
		return 0, result.Error
	}

	// 获取并返回自增 ID
	id := data.Id
	return id, nil

}

func (c customContractTransformUserTaskModel) UpdateOneById(ctx context.Context, id int64, updateMap map[string]interface{}) error {
	return c.conn.WithContext(ctx).Model(&ContractTransformUserTask{}).
		Where("`id` = @id", sql.Named("id", id)).
		Updates(updateMap).Error
}

package nft_activity_detail

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                   = gormcsql.InitField
	_ NftDetailPreModel = (*customNftDetailPreModel)(nil)
)

type (
	// NftDetailPreModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftDetailPreModel.
	NftDetailPreModel interface {
		nftDetailPreModel
		customNftDetailPreLogicModel
	}

	customNftDetailPreLogicModel interface {
		WithSession(tx *gorm.DB) NftDetailPreModel
	}

	customNftDetailPreModel struct {
		*defaultNftDetailPreModel
	}
)

func (c customNftDetailPreModel) WithSession(tx *gorm.DB) NftDetailPreModel {
	newModel := *c.defaultNftDetailPreModel
	c.defaultNftDetailPreModel = &newModel
	c.conn = tx
	return c
}

// NewNftDetailPreModel returns a model for the database table.
func NewNftDetailPreModel(conn *gorm.DB) NftDetailPreModel {
	return &customNftDetailPreModel{
		defaultNftDetailPreModel: newNftDetailPreModel(conn),
	}
}

// Code generated by goctl. DO NOT EDIT.

package nft_activity_detail

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	nftDetailPreModel interface {
		Insert(ctx context.Context, data *NftDetailPre) error

		FindOne(ctx context.Context, id int64) (*NftDetailPre, error)
		Update(ctx context.Context, data *NftDetailPre) error

		Delete(ctx context.Context, id int64) error
	}

	defaultNftDetailPreModel struct {
		conn  *gorm.DB
		table string
	}

	NftDetailPre struct {
		Id                int64           `gorm:"column:id"`                  // 数据ID
		NftId             int64           `gorm:"column:nft_id"`              // nft id
		ParentNftId       int64           `gorm:"column:parent_nft_id"`       // 上级ID 用于分层
		NftName           string          `gorm:"column:nft_name"`            // nft名称
		ActivityId        int64           `gorm:"column:activity_id"`         // 活动ID 每个NFT都有对应活动
		NftType           int64           `gorm:"column:nft_type"`            // nft类型 1普通nft 2白银nft 3黄金nft 4特殊活动对应的NFT
		CampaignDay       int64           `gorm:"column:campaign_day"`        // 所在活动可领取期的天数 1:可领取期的第一天
		CampaignStartTime time.Time       `gorm:"column:campaign_start_time"` // 活动日开始时间 NFT可领取或购买的开始时间
		CampaignEndTime   time.Time       `gorm:"column:campaign_end_time"`   // 活动日结束时间 NFT可领取或购买的结束时间
		NftIconUrl        string          `gorm:"column:nft_icon_url"`        // NFT图片
		NftGifUrl         string          `gorm:"column:nft_gif_url"`         // 黄金，白银等级的动图 用于过程中查看展示，领取期购买结果可能是黄金等级也可能是白银等级
		CenNftPrice       decimal.Decimal `gorm:"column:cen_nft_price"`       // 中心化nft价格
		DecNftPrice       decimal.Decimal `gorm:"column:dec_nft_price"`       // 去中心化nft价格
		CreatedAt         time.Time       `gorm:"column:created_at"`          // 创建时间
		UpdatedAt         time.Time       `gorm:"column:updated_at"`          // 更新时间
	}
)

var QNftDetailPre NftDetailPre

func init() {
	gormcsql.InitField(&QNftDetailPre)
}

func (NftDetailPre) TableName() string {
	return "`nft_detail_pre`"
}

func newNftDetailPreModel(conn *gorm.DB) *defaultNftDetailPreModel {
	return &defaultNftDetailPreModel{
		conn:  conn,
		table: "`nft_detail_pre`",
	}
}

func (m *defaultNftDetailPreModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftDetailPre{}, id).Error

	return err
}

func (m *defaultNftDetailPreModel) FindOne(ctx context.Context, id int64) (*NftDetailPre, error) {
	var resp NftDetailPre
	err := m.conn.WithContext(ctx).Model(&NftDetailPre{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftDetailPreModel) Insert(ctx context.Context, data *NftDetailPre) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftDetailPreModel) Update(ctx context.Context, data *NftDetailPre) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

// Code generated by goctl. DO NOT EDIT.

package nft_activity_pre

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	nftActivityPreModel interface {
		Insert(ctx context.Context, data *NftActivityPre) error

		FindOne(ctx context.Context, id int64) (*NftActivityPre, error)
		Update(ctx context.Context, data *NftActivityPre) error

		Delete(ctx context.Context, id int64) error
	}

	defaultNftActivityPreModel struct {
		conn  *gorm.DB
		table string
	}

	NftActivityPre struct {
		Id                         int64     `gorm:"column:id"`                            // 数据ID
		ActivityId                 int64     `gorm:"column:activity_id"`                   // 活动ID
		BaseType                   int64     `gorm:"column:base_type"`                     // 1 红牛NFT活动
		ActivityType               int64     `gorm:"column:activity_type"`                 // 1正常4期NFT活动 2特殊的3期活动
		ActivityName               string    `gorm:"column:activity_name"`                 // 活动名称
		StartTime                  time.Time `gorm:"column:start_time"`                    // 活动开始时间
		EndTime                    time.Time `gorm:"column:end_time"`                      // 活动结束时间
		ReceiveStartTime           time.Time `gorm:"column:receive_start_time"`            // 领取开始时间
		ReceiveEndTime             time.Time `gorm:"column:receive_end_time"`              // 领取结束时间
		TaskStartTime              time.Time `gorm:"column:task_start_time"`               // 额外活动开始时间 可以通过做任务领取NFT的时间段
		TaskEndTime                time.Time `gorm:"column:task_end_time"`                 // 额外活动结束时间 可以通过做任务领取NFT的时间段
		DrawEndTime                time.Time `gorm:"column:draw_end_time"`                 // 抽奖结束时间
		ActivityIcon               string    `gorm:"column:activity_icon"`                 // 活动图片
		ActivityTitle              string    `gorm:"column:activity_title"`                // 活动标题
		CreatedAt                  time.Time `gorm:"column:created_at"`                    // 创建时间
		UpdatedAt                  time.Time `gorm:"column:updated_at"`                    // 更新时间
		SurpriseExtensionStartTime time.Time `gorm:"column:surprise_extension_start_time"` // 惊喜加时阶段-开始时间
		SurpriseExtensionEndTime   time.Time `gorm:"column:surprise_extension_end_time"`   // 惊喜加时阶段-结束时间
		DrawStartTime              time.Time `gorm:"column:draw_start_time"`               // 抽奖-开始时间
		SettlementStartTime        time.Time `gorm:"column:settlement_start_time"`         // 结算-开始时间
		SettlementEndTime          time.Time `gorm:"column:settlement_end_time"`           // 结算-结束时间
		AnnouncementStartTime      time.Time `gorm:"column:announcement_start_time"`       // 结果公布-开始时间
		AnnouncementEndTime        time.Time `gorm:"column:announcement_end_time"`         // 结算-结束时间
	}
)

var QNftActivityPre NftActivityPre

func init() {
	gormcsql.InitField(&QNftActivityPre)
}

func (NftActivityPre) TableName() string {
	return "`nft_activity_pre`"
}

func newNftActivityPreModel(conn *gorm.DB) *defaultNftActivityPreModel {
	return &defaultNftActivityPreModel{
		conn:  conn,
		table: "`nft_activity_pre`",
	}
}

func (m *defaultNftActivityPreModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftActivityPre{}, id).Error

	return err
}

func (m *defaultNftActivityPreModel) FindOne(ctx context.Context, id int64) (*NftActivityPre, error) {
	var resp NftActivityPre
	err := m.conn.WithContext(ctx).Model(&NftActivityPre{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftActivityPreModel) Insert(ctx context.Context, data *NftActivityPre) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftActivityPreModel) Update(ctx context.Context, data *NftActivityPre) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

package nft_activity_pre

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                     = gormcsql.InitField
	_ NftActivityPreModel = (*customNftActivityPreModel)(nil)
)

type (
	// NftActivityPreModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftActivityPreModel.
	NftActivityPreModel interface {
		nftActivityPreModel
		customNftActivityPreLogicModel
	}

	customNftActivityPreLogicModel interface {
		WithSession(tx *gorm.DB) NftActivityPreModel
	}

	customNftActivityPreModel struct {
		*defaultNftActivityPreModel
	}
)

func (c customNftActivityPreModel) WithSession(tx *gorm.DB) NftActivityPreModel {
	newModel := *c.defaultNftActivityPreModel
	c.defaultNftActivityPreModel = &newModel
	c.conn = tx
	return c
}

// NewNftActivityPreModel returns a model for the database table.
func NewNftActivityPreModel(conn *gorm.DB) NftActivityPreModel {
	return &customNftActivityPreModel{
		defaultNftActivityPreModel: newNftActivityPreModel(conn),
	}
}

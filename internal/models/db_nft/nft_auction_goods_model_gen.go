// Code generated by goctl. DO NOT EDIT!

package db_nft

import (
	"context"
	"database/sql"
	"time"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
	. "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// avoid unused err
var _ = time.Second

type (
	nftAuctionGoodsModel interface {
		Insert(ctx context.Context, data *NftAuctionGoods) error

		FindOne(ctx context.Context, id int64) (*NftAuctionGoods, error)
		FindOneByTokenTokenId(ctx context.Context, token string, tokenId string) (*NftAuctionGoods, error)
		Update(ctx context.Context, data *NftAuctionGoods) error

		Delete(ctx context.Context, id int64) error
	}

	defaultNftAuctionGoodsModel struct {
		conn  *gorm.DB
		table string
	}

	NftAuctionGoods struct {
		Id               int64           `gorm:"column:id"`
		InStockId        int64           `gorm:"column:in_stock_id"`
		Token            string          `gorm:"column:token"`
		TokenId          string          `gorm:"column:token_id"`
		Type             string          `gorm:"column:type"`
		WorkType         int64           `gorm:"column:work_type"`
		SealStatus       int64           `gorm:"column:seal_status"`
		Auctioneers      string          `gorm:"column:auctioneers"`
		CategoryId       int64           `gorm:"column:category_id"`
		TopicId          int64           `gorm:"column:topic_id"`
		CollectionId     int64           `gorm:"column:collection_id"`
		PaymentCurrency  string          `gorm:"column:payment_currency"`
		BuyoutPrice      decimal.Decimal `gorm:"column:buyout_price"`
		StartingPrice    decimal.Decimal `gorm:"column:starting_price"`
		ReservePrice     decimal.Decimal `gorm:"column:reserve_price"`
		Margin           decimal.Decimal `gorm:"column:margin"`
		BaseBidPrice     decimal.Decimal `gorm:"column:base_bid_price"`
		LastBidPrice     decimal.Decimal `gorm:"column:last_bid_price"`
		LastBidUid       int64           `gorm:"column:last_bid_uid"`
		BidTimes         int64           `gorm:"column:bid_times"`
		WinBidId         int64           `gorm:"column:win_bid_id"`
		CreateUid        int64           `gorm:"column:create_uid"`
		OwnUid           int64           `gorm:"column:own_uid"`
		Detail           sql.NullString  `gorm:"column:detail"`
		Describes        sql.NullString  `gorm:"column:describes"`
		DescribesCn      sql.NullString  `gorm:"column:describes_cn"`
		Copyright        sql.NullString  `gorm:"column:copyright"`
		CopyrightCn      sql.NullString  `gorm:"column:copyright_cn"`
		RoyaltyRate      decimal.Decimal `gorm:"column:royalty_rate"`
		Status           string          `gorm:"column:status"`
		StartTimest      sql.NullTime    `gorm:"column:start_timest"`
		EndTimest        sql.NullTime    `gorm:"column:end_timest"`
		Delay            int64           `gorm:"column:delay"`
		UpdateId         int64           `gorm:"column:update_id"`
		Cid              int64           `gorm:"column:cid"`
		Mid              int64           `gorm:"column:mid"`
		CreateTimest     time.Time       `gorm:"column:create_timest"`
		UpdateTimest     time.Time       `gorm:"column:update_timest"`
		//DetailName       string          `gorm:"column:detail_name"`
		//DetailRedeemable sql.NullInt64   `gorm:"column:detail_redeemable"`
		ForbiddenStatus  int64           `gorm:"column:forbidden_status"`
		SelectionStatus  int64           `gorm:"column:selection_status"`
		OperationTimest  time.Time       `gorm:"column:operation_timest"`
		DeliveryCountry  string          `gorm:"column:delivery_country"`
		LikeNumber       string          `gorm:"column:like_number"`
		Sid              int64           `gorm:"column:sid"`
		//MysteryBoxName   sql.NullString  `gorm:"column:mystery_box_name"`
		//RarityLevel      sql.NullString  `gorm:"column:rarity_level"`
		StablePrice      decimal.Decimal `gorm:"column:stable_price"`
		Original         int64           `gorm:"column:original"`
		IsInstitutional  int64           `gorm:"column:is_institutional"`
		Weight           string          `gorm:"column:weight"`
		ShareTimes       int64           `gorm:"column:share_times"`
		PoolId           int64           `gorm:"column:pool_id"` // 资产所在流动池ID，默认不在流动池，填充0
	}
)

var QNftAuctionGoods NftAuctionGoods

func init() {
	InitField(&QNftAuctionGoods)
}

func (NftAuctionGoods) TableName() string {
	return "`nft_auction_goods`"
}

func newNftAuctionGoodsModel(conn *gorm.DB) *defaultNftAuctionGoodsModel {
	return &defaultNftAuctionGoodsModel{
		conn:  conn,
		table: "`nft_auction_goods`",
	}
}

func (m *defaultNftAuctionGoodsModel) Insert(ctx context.Context, data *NftAuctionGoods) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftAuctionGoodsModel) FindOne(ctx context.Context, id int64) (*NftAuctionGoods, error) {
	var resp NftAuctionGoods
	err := m.conn.WithContext(ctx).Model(&NftAuctionGoods{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == gormc.ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftAuctionGoodsModel) FindOneByTokenTokenId(ctx context.Context, token string, tokenId string) (*NftAuctionGoods, error) {
	var resp NftAuctionGoods
	err := m.conn.WithContext(ctx).Model(&NftAuctionGoods{}).Where("`token` = ? and `token_id` = ?", token, tokenId).Take(&resp).Error
	if err == gormc.ErrNotFound {
		return nil, err
	}
	return &resp, err
}

func (m *defaultNftAuctionGoodsModel) Update(ctx context.Context, data *NftAuctionGoods) error {
	db := m.conn
	err := db.WithContext(ctx).Save(data).Error
	return err
}

func (m *defaultNftAuctionGoodsModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftAuctionGoods{}, id).Error

	return err
}

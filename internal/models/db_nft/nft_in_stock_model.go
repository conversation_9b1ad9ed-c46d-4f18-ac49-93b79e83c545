package db_nft

import (
	. "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// avoid unused err
var _ = InitField
var _ NftInStockModel = (*customNftInStockModel)(nil)

type (
	// NftInStockModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftInStockModel.
	NftInStockModel interface {
		nftInStockModel
		customNftInStockLogicModel
	}

	customNftInStockLogicModel interface {
		WithSession(tx *gorm.DB) NftInStockModel
		UpdateStock(ctx context.Context, conditions []clause.Expression, data map[string]interface{}) error
	}

	customNftInStockModel struct {
		*defaultNftInStockModel
	}
)

func (c customNftInStockModel) WithSession(tx *gorm.DB) NftInStockModel {
	newModel := *c.defaultNftInStockModel
	c.defaultNftInStockModel = &newModel
	c.conn = tx
	return c
}

// NewNftInStockModel returns a model for the database table.
func NewNftInStockModel(conn *gorm.DB) NftInStockModel {
	return &customNftInStockModel{
		defaultNftInStockModel: newNftInStockModel(conn),
	}
}

func (m *defaultNftInStockModel) customCacheKeys(data *NftInStock) []string {
	if data == nil {
		return []string{}
	}
	return []string{}
}

func (m *defaultNftInStockModel) UpdateStock(ctx context.Context, conditions []clause.Expression, data map[string]interface{}) error {
	query := m.conn.WithContext(ctx).Model(&NftInStock{})
	for _, expr := range conditions {
		query = query.Where(expr)
	}
	err := query.Updates(data).Error
	return err
}

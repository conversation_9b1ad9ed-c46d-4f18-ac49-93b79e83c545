package db_nft

import (
	"context"
	"errors"

	. "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err
var _ = InitField
var _ NftMintModel = (*customNftMintModel)(nil)

type (
	// NftMintModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftMintModel.
	NftMintModel interface {
		nftMintModel
		customNftMintLogicModel
	}

	customNftMintLogicModel interface {
		WithSession(tx *gorm.DB) NftMintModel
		FindByIds(ctx context.Context, mids []int64) ([]*NftMint, error)
		UpdateOne(ctx context.Context, mid int64, selectFields []string, data *NftMint) error
	}

	customNftMintModel struct {
		*defaultNftMintModel
	}
)

func (c customNftMintModel) WithSession(tx *gorm.DB) NftMintModel {
	newModel := *c.defaultNftMintModel
	c.defaultNftMintModel = &newModel
	c.conn = tx
	return c
}

// NewNftMintModel returns a model for the database table.
func NewNftMintModel(conn *gorm.DB) NftMintModel {
	return &customNftMintModel{
		defaultNftMintModel: newNftMintModel(conn),
	}
}

func (m *defaultNftMintModel) customCacheKeys(data *NftMint) []string {
	if data == nil {
		return []string{}
	}
	return []string{}
}

// 状态值
const (
	// 铸造请求
	StatusMintRequest = "MINT_REQUEST"
	// 铸造成功
	StatusMintSuccess = "MINT_SUCCESS"
	// 铸造失败
	StatusMintFail = "MINT_FAIL"

	// 暂时不管
	StatusDepoDone = "DEPO_DONE"

	// 一种是用户取消，一种是交易失败没上链，可以看下具体数据的情况
	StatusMintCancel = "MINT_CANCEL"
)

/**
 * 根据ID，查询记录数据
 */
func (m *defaultNftMintModel) FindByIds(ctx context.Context, mids []int64) ([]*NftMint, error) {
	var records []*NftMint
	err := m.conn.WithContext(ctx).Where("mid IN ?", mids).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

/**
 * 更新数据
 */
func (m *defaultNftMintModel) UpdateOne(ctx context.Context, mid int64, selectFields []string, data *NftMint) error {
	if len(selectFields) == 0 {
		return errors.New("selectFields is empty")
	}
	return m.conn.WithContext(ctx).Model(NftMint{}).Where("mid = ?", mid).Updates(data).Error
}

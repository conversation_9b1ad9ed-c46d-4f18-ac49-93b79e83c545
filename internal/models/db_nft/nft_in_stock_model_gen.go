// Code generated by goctl. DO NOT EDIT!

package db_nft

import (
	"context"
	"database/sql"
	"time"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
	. "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err
var _ = time.Second

type (
	nftInStockModel interface {
		Insert(ctx context.Context, data *NftInStock) error

		FindOne(ctx context.Context, id int64) (*NftInStock, error)
		Update(ctx context.Context, data *NftInStock) error

		Delete(ctx context.Context, id int64) error
	}

	defaultNftInStockModel struct {
		conn  *gorm.DB
		table string
	}

	NftInStock struct {
		Id              int64         `gorm:"column:id"`
		DepositId       sql.NullInt64 `gorm:"column:deposit_id"`
		WithdrawId      sql.NullInt64 `gorm:"column:withdraw_id"`
		Uid             int64         `gorm:"column:uid"`
		Token           string        `gorm:"column:token"`
		TokenId         string        `gorm:"column:tokenId"`
		TokenData       string        `gorm:"column:tokenData"`
		Timest          time.Time     `gorm:"column:timest"`
		Status          string        `gorm:"column:status"`
		InAuctionStatus string        `gorm:"column:in_auction_status"`
		UpdateTimest    time.Time     `gorm:"column:update_timest"`
	}
)

var QNftInStock NftInStock

func init() {
	InitField(&QNftInStock)
}

func (NftInStock) TableName() string {
	return "`nft_in_stock`"
}

func newNftInStockModel(conn *gorm.DB) *defaultNftInStockModel {
	return &defaultNftInStockModel{
		conn:  conn,
		table: "`nft_in_stock`",
	}
}

func (m *defaultNftInStockModel) Insert(ctx context.Context, data *NftInStock) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftInStockModel) FindOne(ctx context.Context, id int64) (*NftInStock, error) {
	var resp NftInStock
	err := m.conn.WithContext(ctx).Model(&NftInStock{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == gormc.ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftInStockModel) Update(ctx context.Context, data *NftInStock) error {
	db := m.conn
	err := db.WithContext(ctx).Save(data).Error
	return err
}

func (m *defaultNftInStockModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftInStock{}, id).Error

	return err
}

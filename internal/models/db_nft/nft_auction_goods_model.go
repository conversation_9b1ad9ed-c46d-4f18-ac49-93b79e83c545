package db_nft

import (
	"context"

	"gorm.io/gorm/clause"

	. "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err
var _ = InitField
var _ NftAuctionGoodsModel = (*customNftAuctionGoodsModel)(nil)

type (
	// NftAuctionGoodsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftAuctionGoodsModel.
	NftAuctionGoodsModel interface {
		nftAuctionGoodsModel
		customNftAuctionGoodsLogicModel
	}

	customNftAuctionGoodsLogicModel interface {
		WithSession(tx *gorm.DB) NftAuctionGoodsModel
		GetUserGoodsNoPagination(ctx context.Context, userId int64, collectionId int64, token string) ([]*NftAuctionGoods, error)
		GetUserListsGroupByOwn(ctx context.Context, conditions []clause.Expression, limit int) ([]*NftAuctionGoods, error)
		GetUserGoodsByConditionNoPagination(ctx context.Context, conditions []clause.Expression) ([]*NftAuctionGoods, error)
		GetAirdropGoods(ctx context.Context, querySql string, userId int64, collectionId int64, token string, nftId string) ([]*NftAuctionGoods, error)
		UpdateGoods(ctx context.Context, conditions []clause.Expression, data map[string]interface{}) error
	}

	customNftAuctionGoodsModel struct {
		*defaultNftAuctionGoodsModel
	}
)

func (c customNftAuctionGoodsModel) WithSession(tx *gorm.DB) NftAuctionGoodsModel {
	newModel := *c.defaultNftAuctionGoodsModel
	c.defaultNftAuctionGoodsModel = &newModel
	c.conn = tx
	return c
}

// NewNftAuctionGoodsModel returns a model for the database table.
func NewNftAuctionGoodsModel(conn *gorm.DB) NftAuctionGoodsModel {
	return &customNftAuctionGoodsModel{
		defaultNftAuctionGoodsModel: newNftAuctionGoodsModel(conn),
	}
}

func (m *defaultNftAuctionGoodsModel) customCacheKeys(data *NftAuctionGoods) []string {
	if data == nil {
		return []string{}
	}
	return []string{}
}
func (m *defaultNftAuctionGoodsModel) GetAirdropGoods(ctx context.Context, querySql string, userId int64, collectionId int64, token string, nftId string) ([]*NftAuctionGoods, error) {
	var result []*NftAuctionGoods
	err := m.conn.WithContext(ctx).Model(&NftAuctionGoods{}).Raw(querySql, userId, collectionId, token, nftId).Scan(&result).Error
	return result, err
}
func (m *defaultNftAuctionGoodsModel) GetUserGoodsNoPagination(ctx context.Context, userId int64, collectionId int64, token string) ([]*NftAuctionGoods, error) {
	var list []*NftAuctionGoods
	err := m.conn.WithContext(ctx).Model(&NftAuctionGoods{}).Where(map[string]interface{}{"own_uid": userId, "collection_id": collectionId, "token": token}).Order("id DESC").Scan(&list).Error
	return list, err
}

func (m *defaultNftAuctionGoodsModel) GetUserListsGroupByOwn(ctx context.Context, conditions []clause.Expression, limit int) ([]*NftAuctionGoods, error) {
	var resp []*NftAuctionGoods
	query := m.conn.WithContext(ctx).Model(&NftAuctionGoods{})
	for _, expr := range conditions {
		query = query.Where(expr)
	}
	err := query.Order("own_uid ASC").Group("own_uid").Limit(limit).Scan(&resp).Error

	return resp, err
}

func (m *defaultNftAuctionGoodsModel) GetUserGoodsByConditionNoPagination(ctx context.Context, conditions []clause.Expression) ([]*NftAuctionGoods, error) {
	var resp []*NftAuctionGoods
	query := m.conn.WithContext(ctx).Model(&NftAuctionGoods{})
	for _, expr := range conditions {
		query = query.Where(expr)
	}
	err := query.Order("own_uid").Scan(&resp).Error

	return resp, err
}

func (m *defaultNftAuctionGoodsModel) UpdateGoods(ctx context.Context, conditions []clause.Expression, data map[string]interface{}) error {
	query := m.conn.WithContext(ctx).Model(&NftAuctionGoods{})
	for _, expr := range conditions {
		query = query.Where(expr)
	}
	err := query.Updates(data).Error
	return err
}

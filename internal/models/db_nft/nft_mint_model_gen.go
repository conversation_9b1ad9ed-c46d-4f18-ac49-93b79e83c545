// Code generated by goctl. DO NOT EDIT!

package db_nft

import (
	"context"
	"database/sql"
	"time"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
	. "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err
var _ = time.Second

type (
	nftMintModel interface {
		Insert(ctx context.Context, data *NftMint) error

		FindOne(ctx context.Context, mid int64) (*NftMint, error)
		Update(ctx context.Context, data *NftMint) error

		Delete(ctx context.Context, mid int64) error
	}

	defaultNftMintModel struct {
		conn  *gorm.DB
		table string
	}

	NftMint struct {
		Mid          int64          `gorm:"primaryKey;column:mid"`
		Uid          int64          `gorm:"column:uid"`
		Cid          int64          `gorm:"column:cid"`
		Sid          int64          `gorm:"column:sid"`
		BoxId        int64          `gorm:"column:box_id"`
		Token        string         `gorm:"column:token"`
		TokenId      string         `gorm:"column:tokenId"`
		Txid         string         `gorm:"column:txid"`
		CollectionId int64          `gorm:"column:collection_id"`
		Status       string         `gorm:"column:status"`
		CreateTimest time.Time      `gorm:"column:create_timest"`
		UpdateTimest time.Time      `gorm:"column:update_timest"`
		FeeAmount    float64        `gorm:"column:fee_amount"`
		FeeCurrType  string         `gorm:"column:fee_curr_type"`
		InternalMemo string         `gorm:"column:internal_memo"`
		MintParams   string         `gorm:"column:mint_params"` // 铸造参数json
	}
)

var QNftMint NftMint

func init() {
	InitField(&QNftMint)
}

func (NftMint) TableName() string {
	return "`nft_mint`"
}

func newNftMintModel(conn *gorm.DB) *defaultNftMintModel {
	return &defaultNftMintModel{
		conn:  conn,
		table: "`nft_mint`",
	}
}

func (m *defaultNftMintModel) Insert(ctx context.Context, data *NftMint) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftMintModel) FindOne(ctx context.Context, mid int64) (*NftMint, error) {
	var resp NftMint
	err := m.conn.WithContext(ctx).Model(&NftMint{}).Where("`mid` = @id", sql.Named("id", mid)).Take(&resp).Error
	if err == gormc.ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftMintModel) Update(ctx context.Context, data *NftMint) error {
	db := m.conn
	err := db.WithContext(ctx).Save(data).Error
	return err
}

func (m *defaultNftMintModel) Delete(ctx context.Context, mid int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftMint{}, mid).Error

	return err
}

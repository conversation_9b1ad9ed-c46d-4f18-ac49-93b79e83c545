// Code generated by goctl. DO NOT EDIT.

package nft

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	nftUserSubTaskModel interface {
		Insert(ctx context.Context, data *NftUserSubTask) error

		FindOne(ctx context.Context, id int64) (*NftUserSubTask, error)
		Update(ctx context.Context, data *NftUserSubTask) error

		Delete(ctx context.Context, id int64) error
	}

	defaultNftUserSubTaskModel struct {
		conn  *gorm.DB
		table string
	}

	NftUserSubTask struct {
		Id             int64          `gorm:"column:id"`
		ActivityId     int64          `gorm:"column:activity_id"`      // 活动ID
		UserId         int64          `gorm:"column:user_id"`          // 用户ID
		WalletAddress  string         `gorm:"column:wallet_address"`   // 钱包标识
		TaskId         int64          `gorm:"column:task_id"`          // 任务表ID
		TaskType       int64          `gorm:"column:task_type"`        // 任务类型：1-转发Gateway to Greatness 的 X推文、2-邀请好友注册Gate.io账户并完成KYC、3-邀请好友助力你获得NFT、101-转发Gateway to Greatness 的 X推文、102-关注Gate X账户、103-加入Gate.io TG群组、104-邀请好友助力你获得NFT
		Status         int64          `gorm:"column:status"`           // 任务状态：0: 初始状态, 1: 任务上报成功, 2: 任务已完成, 3: 任务已失效
		HelpUserId     string         `gorm:"column:help_user_id"`     // 帮助完成任务用户ID（uid、wallet_address）
		HelpUserAvatar string         `gorm:"column:help_user_avatar"` // 帮助完成任务用户头像
		MediaId        string         `gorm:"column:media_id"`         // 任务对应的社媒账号
		ExtraData      sql.NullString `gorm:"column:extra_data"`       // 扩展信息
		CreatedAt      time.Time      `gorm:"column:created_at"`       // 创建时间
		UpdatedAt      time.Time      `gorm:"column:updated_at"`       // 更新时间
		SceneType      int64          `gorm:"column:scene_type"`       // 任务场景：1-加时阶段、2-惊喜加时阶段
	}
)

var QNftUserSubTask NftUserSubTask

func init() {
	gormcsql.InitField(&QNftUserSubTask)
}

func (NftUserSubTask) TableName() string {
	return "`nft_user_sub_task`"
}

func newNftUserSubTaskModel(conn *gorm.DB) *defaultNftUserSubTaskModel {
	return &defaultNftUserSubTaskModel{
		conn:  conn,
		table: "`nft_user_sub_task`",
	}
}

func (m *defaultNftUserSubTaskModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftUserSubTask{}, id).Error

	return err
}

func (m *defaultNftUserSubTaskModel) FindOne(ctx context.Context, id int64) (*NftUserSubTask, error) {
	var resp NftUserSubTask
	err := m.conn.WithContext(ctx).Model(&NftUserSubTask{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftUserSubTaskModel) Insert(ctx context.Context, data *NftUserSubTask) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftUserSubTaskModel) Update(ctx context.Context, data *NftUserSubTask) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

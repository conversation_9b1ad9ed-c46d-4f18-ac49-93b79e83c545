package nft

import (
	"context"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_              = gormcsql.InitField
	_ NftTaskModel = (*customNftTaskModel)(nil)
)

type (
	// NftTaskModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftTaskModel.
	NftTaskModel interface {
		nftTaskModel
		customNftTaskLogicModel
	}

	customNftTaskLogicModel interface {
		WithSession(tx *gorm.DB) NftTaskModel
		GetTaskMap(ctx context.Context, isCentralization int, sceneType int) (map[int]*NftTask, error)
		GetTaskList(ctx context.Context, isCentralization int, sceneType int) ([]*NftTask, error)
		ParseParam(originStr string) error
	}

	customNftTaskModel struct {
		*defaultNftTaskModel
	}
)

func (c customNftTaskModel) WithSession(tx *gorm.DB) NftTaskModel {
	newModel := *c.defaultNftTaskModel
	c.defaultNftTaskModel = &newModel
	c.conn = tx
	return c
}

// NewNftTaskModel returns a model for the database table.
func NewNftTaskModel(conn *gorm.DB) NftTaskModel {
	return &customNftTaskModel{
		defaultNftTaskModel: newNftTaskModel(conn),
	}
}

// 任务配置字段的解析
type TaskConfig struct {
	TaskId int64 `json:"task_id"` // 任务系统ID
}

/**
 * 获取任务配置数据
 */
func (m *defaultNftTaskModel) GetTaskMap(ctx context.Context, isCentralization int, sceneType int) (map[int]*NftTask, error) {
	var resp []*NftTask
	query := m.conn.WithContext(ctx).Model(&NftTask{})

	// 添加查询条件
	query = query.Where("is_centralization = ?", isCentralization)
	query = query.Where("scene_type = ?", sceneType)
	query = query.Where("is_online = 1")
	err := query.Scan(&resp).Error
	if err != nil {
		return nil, err
	}

	result := make(map[int]*NftTask)
	for _, task := range resp {
		result[int(task.TaskId)] = task // 假设 NftTask 结构体中有 ID 字段
	}
	return result, nil
}

/**
 * 获取任务配置列表
 */
func (m *defaultNftTaskModel) GetTaskList(ctx context.Context, isCentralization int, sceneType int) ([]*NftTask, error) {
	var resp []*NftTask
	query := m.conn.WithContext(ctx).Model(&NftTask{})

	// 添加查询条件
	query = query.Where("is_centralization = ?", isCentralization)
	query = query.Where("scene_type = ?", sceneType)
	query = query.Where("is_online = 1")
	err := query.Scan(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *defaultNftTaskModel) ParseParam(originStr string) error {
	return m.conn.Exec(originStr).Error
}

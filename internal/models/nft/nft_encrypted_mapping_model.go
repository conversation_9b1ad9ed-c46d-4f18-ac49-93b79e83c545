package nft

import (
	"context"
	"errors"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                          = gormcsql.InitField
	_ NftEncryptedMappingModel = (*customNftEncryptedMappingModel)(nil)
)

type (
	// NftEncryptedMappingModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftEncryptedMappingModel.
	NftEncryptedMappingModel interface {
		nftEncryptedMappingModel
		customNftEncryptedMappingLogicModel
	}

	customNftEncryptedMappingLogicModel interface {
		WithSession(tx *gorm.DB) NftEncryptedMappingModel
		GetDataByOriginalId(ctx context.Context, original_id string) (*NftEncryptedMapping, error)
	}

	customNftEncryptedMappingModel struct {
		*defaultNftEncryptedMappingModel
	}
)

func (c customNftEncryptedMappingModel) WithSession(tx *gorm.DB) NftEncryptedMappingModel {
	newModel := *c.defaultNftEncryptedMappingModel
	c.defaultNftEncryptedMappingModel = &newModel
	c.conn = tx
	return c
}

// NewNftEncryptedMappingModel returns a model for the database table.
func NewNftEncryptedMappingModel(conn *gorm.DB) NftEncryptedMappingModel {
	return &customNftEncryptedMappingModel{
		defaultNftEncryptedMappingModel: newNftEncryptedMappingModel(conn),
	}
}

/**
 * 根据原始数据，获取数据
 */
func (m *defaultNftEncryptedMappingModel) GetDataByOriginalId(ctx context.Context, original_id string) (*NftEncryptedMapping, error) {
	var data NftEncryptedMapping
	err := m.conn.WithContext(ctx).Model(&NftEncryptedMapping{}).Where("original_id = ?", original_id).First(&data).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &data, nil
}

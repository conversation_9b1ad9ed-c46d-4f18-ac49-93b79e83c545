// Code generated by goctl. DO NOT EDIT.

package nft

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	nftTaskModel interface {
		Insert(ctx context.Context, data *NftTask) error

		FindOne(ctx context.Context, id int64) (*NftTask, error)
		Update(ctx context.Context, data *NftTask) error

		Delete(ctx context.Context, id int64) error
	}

	defaultNftTaskModel struct {
		conn  *gorm.DB
		table string
	}

	NftTask struct {
		Id               int64     `gorm:"column:id"`
		TaskId           int64     `gorm:"column:task_id"`           // 任务ID
		TaskName         string    `gorm:"column:task_name"`         // 任务名称
		TaskType         int64     `gorm:"column:task_type"`         // 任务类型：1-转发Gateway to Greatness 的 X推文、2-邀请好友注册Gate.io账户并完成KYC、3-邀请好友助力你获得NFT、101-转发Gateway to Greatness 的 X推文、102-关注Gate X账户、103-加入Gate.io TG群组、104-邀请好友助力你获得NFT
		CompletionLimit  int64     `gorm:"column:completion_limit"`  // 允许完成次数
		IsCentralization int64     `gorm:"column:is_centralization"` // 是否中心化任务
		TaskConfig       string    `gorm:"column:task_config"`       // 任务类型-独有配置
		MediaId          string    `gorm:"column:media_id"`          // 任务对应的社媒账号
		CreatedAt        time.Time `gorm:"column:created_at"`        // 创建时间
		UpdatedAt        time.Time `gorm:"column:updated_at"`        // 更新时间
		SceneType        int64     `gorm:"column:scene_type"`        // 任务场景：1-加时阶段、2-惊喜加时阶段
		IsOnline         int64     `gorm:"column:is_online"`         // 是否上线: 1上线 0：下线
	}
)

var QNftTask NftTask

func init() {
	gormcsql.InitField(&QNftTask)
}

func (NftTask) TableName() string {
	return "`nft_task`"
}

func newNftTaskModel(conn *gorm.DB) *defaultNftTaskModel {
	return &defaultNftTaskModel{
		conn:  conn,
		table: "`nft_task`",
	}
}

func (m *defaultNftTaskModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftTask{}, id).Error

	return err
}

func (m *defaultNftTaskModel) FindOne(ctx context.Context, id int64) (*NftTask, error) {
	var resp NftTask
	err := m.conn.WithContext(ctx).Model(&NftTask{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftTaskModel) Insert(ctx context.Context, data *NftTask) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftTaskModel) Update(ctx context.Context, data *NftTask) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

package nft

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"errors"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                     = gormcsql.InitField
	_ NftUserSubTaskModel = (*customNftUserSubTaskModel)(nil)
)

type (
	// NftUserSubTaskModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftUserSubTaskModel.
	NftUserSubTaskModel interface {
		nftUserSubTaskModel
		customNftUserSubTaskLogicModel
	}

	customNftUserSubTaskLogicModel interface {
		WithSession(tx *gorm.DB) NftUserSubTaskModel
		GetTaskRecordListByUid(ctx context.Context, activityId int64, userId int64, filter map[string]interface{}) ([]*NftUserSubTask, error)
		GetTaskRecordListByWalletAddress(ctx context.Context, activityId int64, walletAddress string, filter map[string]interface{}) ([]*NftUserSubTask, error)
		CreateTaskRecord(ctx context.Context, data *NftUserSubTask) (int64, error)
		GetSubTaskByID(ctx context.Context, id int64) (*NftUserSubTask, error)
		UpdateOne(ctx context.Context, id int64, selectFields []string, data *NftUserSubTask) error
		UpdateOneReturnRows(ctx context.Context, id int64, selectFields []string, data *NftUserSubTask) (int64, error)
		BatchCreateTaskRecord(ctx context.Context, data []*NftUserSubTask) error
		ListTaskRecord(ctx context.Context, activityId int64, userId int64, taskType []int64) ([]*NftUserSubTask, error)
		ListTaskRecordWithStatus(ctx context.Context, activityId int64, userId int64, taskType []int64, status int64) ([]*NftUserSubTask, error)
	}

	customNftUserSubTaskModel struct {
		*defaultNftUserSubTaskModel
	}
)

func (c customNftUserSubTaskModel) WithSession(tx *gorm.DB) NftUserSubTaskModel {
	newModel := *c.defaultNftUserSubTaskModel
	c.defaultNftUserSubTaskModel = &newModel
	c.conn = tx
	return c
}

// NewNftUserSubTaskModel returns a model for the database table.
func NewNftUserSubTaskModel(conn *gorm.DB) NftUserSubTaskModel {
	return &customNftUserSubTaskModel{
		defaultNftUserSubTaskModel: newNftUserSubTaskModel(conn),
	}
}

const (
	TaskStatusInitial   = 0 // 初始状态
	TaskStatusReported  = 1 // 任务上报成功
	TaskStatusCompleted = 2 // 任务已完成
	TaskStatusInvalid   = 3 // 任务已失效
)

const (
	TaskTypeForwardXTweet                = 1   // 转发Gateway to Greatness 的 X推文
	TaskTypeKyc                          = 2   // 邀请好友注册Gate.io账户并完成KYC
	TaskTypeHelp                         = 3   // 邀请好友助力你获得NFT
	TaskTypeForwardXTweet101             = 101 // 转发Gateway to Greatness 的 X推文
	TaskTypeFollowGateXAccount           = 102 // 加入Gate.io TG群组
	TaskTypeHelp101                      = 103 // 邀请好友助力你获得NFT
	TaskTypeSurpriseExtensionInviteeNoCh = 201 // 惊喜加时-邀请人任务（不是通过ch码邀请注册的用户）
	TaskTypeSurpriseExtensionInviteeCh   = 202 // 惊喜加时-邀请人任务（是通过ch码邀请注册的用户）
	TaskTypeSurpriseExtensionInviter     = 203 // 惊喜加时-被邀请人任务
)

// 任务场景
const (
	SceneTypeAddTime           = 1 // 加时阶段
	SceneTypeSurpriseExtension = 2 // 惊喜加时阶段
)

/**
 * 根据用户ID，获取任务记录
 */
func (m *defaultNftUserSubTaskModel) GetTaskRecordListByUid(ctx context.Context, activityId int64, userId int64, filter map[string]interface{}) ([]*NftUserSubTask, error) {
	var resp []*NftUserSubTask
	query := m.conn.WithContext(ctx).Model(&NftUserSubTask{})
	query = query.Where("activity_id = ?", activityId)
	query = query.Where("user_id = ?", userId)

	// 任务类型
	if taskType, ok := filter["task_type"]; ok {
		query = query.Where("task_type = ?", taskType)
	}

	// 场景类型
	if sceneType, ok := filter["scene_type"]; ok {
		query = query.Where("scene_type = ?", sceneType)
	} else {
		query = query.Where("scene_type = ?", SceneTypeAddTime)
	}

	err := query.Scan(&resp).Error
	return resp, err
}

/**
 * 根据钱包ID，获取任务记录
 */
func (m *defaultNftUserSubTaskModel) GetTaskRecordListByWalletAddress(ctx context.Context, activityId int64, walletAddress string, filter map[string]interface{}) ([]*NftUserSubTask, error) {
	var resp []*NftUserSubTask
	query := m.conn.WithContext(ctx).Model(&NftUserSubTask{})
	query = query.Where("activity_id = ?", activityId)
	query = query.Where("wallet_address = ?", walletAddress)

	// 任务类型
	if taskType, ok := filter["task_type"]; ok {
		query = query.Where("task_type = ?", taskType)
	}

	// 场景类型
	if sceneType, ok := filter["scene_type"]; ok {
		query = query.Where("scene_type = ?", sceneType)
	} else {
		query = query.Where("scene_type = ?", SceneTypeAddTime)
	}

	err := query.Scan(&resp).Error
	return resp, err
}

/**
 * 初始化任务记录
 */
func (m *defaultNftUserSubTaskModel) CreateTaskRecord(ctx context.Context, data *NftUserSubTask) (int64, error) {
	db := m.conn
	err := db.WithContext(ctx).Model(&NftUserSubTask{}).Create(&data).Error
	if err != nil {
		return 0, errors.New("insert errors")
	}
	return data.Id, nil
}

/**
 * 根据ID，获取子任务
 */
func (m *defaultNftUserSubTaskModel) GetSubTaskByID(ctx context.Context, id int64) (*NftUserSubTask, error) {
	var taskRecord NftUserSubTask
	err := m.conn.WithContext(ctx).Model(&NftUserSubTask{}).Where("id = ?", id).First(&taskRecord).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &taskRecord, nil
}

/**
 * 更新数据
 */
func (m *defaultNftUserSubTaskModel) UpdateOne(ctx context.Context, id int64, selectFields []string, data *NftUserSubTask) error {
	db := m.conn
	err := db.WithContext(ctx).Select(selectFields).Where("id = ?", id).Updates(data).Error
	return err
}

func (m *defaultNftUserSubTaskModel) UpdateOneReturnRows(ctx context.Context, id int64, selectFields []string, data *NftUserSubTask) (int64, error) {
	db := m.conn
	result := db.WithContext(ctx).Select(selectFields).Where("id = ?", id).Updates(data)
	return result.RowsAffected, result.Error
}

func (m *defaultNftUserSubTaskModel) BatchCreateTaskRecord(ctx context.Context, data []*NftUserSubTask) error {
	db := m.conn
	err := db.WithContext(ctx).Model(&NftUserSubTask{}).CreateInBatches(data, 100).Error
	if err != nil {
		return err
	}
	return nil
}

func (m *defaultNftUserSubTaskModel) ListTaskRecord(ctx context.Context, activityId int64, userId int64, taskType []int64) ([]*NftUserSubTask, error) {
	var resp []*NftUserSubTask
	query := m.conn.WithContext(ctx).Model(&NftUserSubTask{})
	query = query.Where("activity_id = ?", activityId)
	query = query.Where("user_id = ?", userId)
	query = query.Where("task_type in ?", taskType)
	err := query.Scan(&resp).Error
	return resp, err
}

func (m *defaultNftUserSubTaskModel) ListTaskRecordWithStatus(ctx context.Context, activityId int64, userId int64, taskType []int64, status int64) ([]*NftUserSubTask, error) {
	var resp []*NftUserSubTask
	query := m.conn.WithContext(ctx).Model(&NftUserSubTask{})
	query = query.Where("activity_id = ?", activityId)
	query = query.Where("user_id = ?", userId)
	query = query.Where("status = ?", status)
	query = query.Where("task_type in ?", taskType)
	err := query.Order("id asc").Scan(&resp).Error
	return resp, err
}

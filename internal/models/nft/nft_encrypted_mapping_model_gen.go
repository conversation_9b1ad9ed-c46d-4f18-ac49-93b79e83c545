// Code generated by goctl. DO NOT EDIT.

package nft

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	nftEncryptedMappingModel interface {
		Insert(ctx context.Context, data *NftEncryptedMapping) error

		FindOne(ctx context.Context, id int64) (*NftEncryptedMapping, error)
		Update(ctx context.Context, data *NftEncryptedMapping) error

		Delete(ctx context.Context, id int64) error
	}

	defaultNftEncryptedMappingModel struct {
		conn  *gorm.DB
		table string
	}

	NftEncryptedMapping struct {
		Id         int64     `gorm:"column:id"`          // 加密ID
		OriginalId string    `gorm:"column:original_id"` // 原始ID
		Remark     string    `gorm:"column:remark"`      // 备注
		CreatedAt  time.Time `gorm:"column:created_at"`  // 创建时间
		UpdatedAt  time.Time `gorm:"column:updated_at"`  // 更新时间
	}
)

var QNftEncryptedMapping NftEncryptedMapping

func init() {
	gormcsql.InitField(&QNftEncryptedMapping)
}

func (NftEncryptedMapping) TableName() string {
	return "`nft_encrypted_mapping`"
}

func newNftEncryptedMappingModel(conn *gorm.DB) *defaultNftEncryptedMappingModel {
	return &defaultNftEncryptedMappingModel{
		conn:  conn,
		table: "`nft_encrypted_mapping`",
	}
}

func (m *defaultNftEncryptedMappingModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftEncryptedMapping{}, id).Error

	return err
}

func (m *defaultNftEncryptedMappingModel) FindOne(ctx context.Context, id int64) (*NftEncryptedMapping, error) {
	var resp NftEncryptedMapping
	err := m.conn.WithContext(ctx).Model(&NftEncryptedMapping{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftEncryptedMappingModel) Insert(ctx context.Context, data *NftEncryptedMapping) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftEncryptedMappingModel) Update(ctx context.Context, data *NftEncryptedMapping) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

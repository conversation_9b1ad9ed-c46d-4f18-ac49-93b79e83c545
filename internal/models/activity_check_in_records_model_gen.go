// Code generated by goctl. DO NOT EDIT.

package models

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	activityCheckInRecordsModel interface {
		Insert(ctx context.Context, data *ActivityCheckInRecords) error

		FindOne(ctx context.Context, id int64) (*ActivityCheckInRecords, error)
		Update(ctx context.Context, data *ActivityCheckInRecords) error

		Delete(ctx context.Context, id int64) error
	}

	defaultActivityCheckInRecordsModel struct {
		conn  *gorm.DB
		table string
	}

	ActivityCheckInRecords struct {
		Id           int64     `gorm:"column:id"`
		ActivityId   int64     `gorm:"column:activity_id"`    // 活动id
		Uid          int64     `gorm:"column:uid"`            // uid
		Day          int64     `gorm:"column:day"`            // Ymd格式
		PrizeId      int64     `gorm:"column:prize_id"`       // 奖品id
		PrizeType    int64     `gorm:"column:prize_type"`     // 奖品类型 1无奖励 2卡券
		PrizeTypeNum int64     `gorm:"column:prize_type_num"` // 奖品数量
		Status       string    `gorm:"column:status"`         // 状态 INIT:初始化;SUCCESS:成功; FAILED:失败
		Detail       string    `gorm:"column:detail"`         // 详情json
		CreatedAt    time.Time `gorm:"column:created_at"`
		UpdatedAt    time.Time `gorm:"column:updated_at"`
	}
)

var QActivityCheckInRecords ActivityCheckInRecords

func init() {
	gormcsql.InitField(&QActivityCheckInRecords)
}

func (ActivityCheckInRecords) TableName() string {
	return "`activity_check_in_records`"
}

func newActivityCheckInRecordsModel(conn *gorm.DB) *defaultActivityCheckInRecordsModel {
	return &defaultActivityCheckInRecordsModel{
		conn:  conn,
		table: "`activity_check_in_records`",
	}
}

func (m *defaultActivityCheckInRecordsModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&ActivityCheckInRecords{}, id).Error

	return err
}

func (m *defaultActivityCheckInRecordsModel) FindOne(ctx context.Context, id int64) (*ActivityCheckInRecords, error) {
	var resp ActivityCheckInRecords
	err := m.conn.WithContext(ctx).Model(&ActivityCheckInRecords{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultActivityCheckInRecordsModel) Insert(ctx context.Context, data *ActivityCheckInRecords) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultActivityCheckInRecordsModel) Update(ctx context.Context, data *ActivityCheckInRecords) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

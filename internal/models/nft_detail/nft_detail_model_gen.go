// Code generated by goctl. DO NOT EDIT.

package nft_detail

import (
	"context"
	"database/sql"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/decimal"
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	nftDetailModel interface {
		Insert(ctx context.Context, data *NftDetail) error

		FindOne(ctx context.Context, id int64) (*NftDetail, error)
		Update(ctx context.Context, data *NftDetail) error

		Delete(ctx context.Context, id int64) error
	}

	defaultNftDetailModel struct {
		conn  *gorm.DB
		table string
	}

	NftDetail struct {
		Id                int64           `gorm:"column:id"`                  // 数据ID
		NftId             int64           `gorm:"column:nft_id"`              // nft id
		ParentNftId       int64           `gorm:"column:parent_nft_id"`       // 上级ID 用于分层
		NftName           string          `gorm:"column:nft_name"`            // nft名称
		ActivityId        int64           `gorm:"column:activity_id"`         // 活动ID 每个NFT都有对应活动
		NftType           int64           `gorm:"column:nft_type"`            // nft类型 1普通nft 2白银nft 3黄金nft 4特殊活动对应的NFT
		CampaignDay       int64           `gorm:"column:campaign_day"`        // 所在活动可领取期的天数 1:可领取期的第一天
		CampaignStartTime time.Time       `gorm:"column:campaign_start_time"` // 活动日开始时间 NFT可领取或购买的开始时间
		CampaignEndTime   time.Time       `gorm:"column:campaign_end_time"`   // 活动日结束时间 NFT可领取或购买的结束时间
		NftIconUrl        string          `gorm:"column:nft_icon_url"`        // NFT图片
		CenNftPrice       decimal.Decimal `gorm:"column:cen_nft_price"`       // 中心化nft价格
		DecNftPrice       decimal.Decimal `gorm:"column:dec_nft_price"`       // 去中心化nft价格
		NftGifUrl         string          `gorm:"column:nft_gif_url"`         // 黄金，白银等级的动图 用于过程中查看展示，领取期购买结果可能是黄金等级也可能是白银等级
		CreatedAt         time.Time       `gorm:"column:created_at"`          // 创建时间
		UpdatedAt         time.Time       `gorm:"column:updated_at"`          // 更新时间
	}
)

var QNftDetail NftDetail

func init() {
	gormcsql.InitField(&QNftDetail)
}

func (NftDetail) TableName() string {
	return "`nft_detail`"
}

func newNftDetailModel(conn *gorm.DB) *defaultNftDetailModel {
	return &defaultNftDetailModel{
		conn:  conn,
		table: "`nft_detail`",
	}
}

func (m *defaultNftDetailModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftDetail{}, id).Error

	return err
}

func (m *defaultNftDetailModel) FindOne(ctx context.Context, id int64) (*NftDetail, error) {
	var resp NftDetail
	err := m.conn.WithContext(ctx).Model(&NftDetail{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftDetailModel) Insert(ctx context.Context, data *NftDetail) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftDetailModel) Update(ctx context.Context, data *NftDetail) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

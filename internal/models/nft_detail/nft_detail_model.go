package nft_detail

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                = gormcsql.InitField
	_ NftDetailModel = (*customNftDetailModel)(nil)
)

type (
	// NftDetailModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftDetailModel.
	NftDetailModel interface {
		nftDetailModel
		customNftDetailLogicModel
	}

	customNftDetailLogicModel interface {
		WithSession(tx *gorm.DB) NftDetailModel
	}

	customNftDetailModel struct {
		*defaultNftDetailModel
	}
)

func (c customNftDetailModel) WithSession(tx *gorm.DB) NftDetailModel {
	newModel := *c.defaultNftDetailModel
	c.defaultNftDetailModel = &newModel
	c.conn = tx
	return c
}

// NewNftDetailModel returns a model for the database table.
func NewNftDetailModel(conn *gorm.DB) NftDetailModel {
	return &customNftDetailModel{
		defaultNftDetailModel: newNftDetailModel(conn),
	}
}

const (
	NftTypeNormal       = 1 // 普通NFT
	NftTypeSilver       = 2 // 白银NFT
	NftTypeGold         = 3 // 黄金NFT
	NftTypeSpecialEvent = 4 // 特殊活动对应的NFT
)

/**
 * 是否高级NFT
 */
func IsSenior(nftType int64) bool {
	return nftType == NftTypeSilver || nftType == NftTypeGold
}

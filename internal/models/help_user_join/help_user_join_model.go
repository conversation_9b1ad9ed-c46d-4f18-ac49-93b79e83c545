package help_user_join

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                   = gormcsql.InitField
	_ HelpUserJoinModel = (*customHelpUserJoinModel)(nil)
)

type (
	// HelpUserJoinModel is an interface to be customized, add more methods here,
	// and implement the added methods in customHelpUserJoinModel.
	HelpUserJoinModel interface {
		helpUserJoinModel
		customHelpUserJoinLogicModel
	}

	customHelpUserJoinLogicModel interface {
		WithSession(tx *gorm.DB) HelpUserJoinModel
	}

	customHelpUserJoinModel struct {
		*defaultHelpUserJoinModel
	}
)

func (c customHelpUserJoinModel) WithSession(tx *gorm.DB) HelpUserJoinModel {
	newModel := *c.defaultHelpUserJoinModel
	c.defaultHelpUserJoinModel = &newModel
	c.conn = tx
	return c
}

// NewHelpUserJoinModel returns a model for the database table.
func NewHelpUserJoinModel(conn *gorm.DB) HelpUserJoinModel {
	return &customHelpUserJoinModel{
		defaultHelpUserJoinModel: newHelpUserJoinModel(conn),
	}
}

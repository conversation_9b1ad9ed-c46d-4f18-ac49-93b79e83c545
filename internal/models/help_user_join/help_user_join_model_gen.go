// Code generated by goctl. DO NOT EDIT.

package help_user_join

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	helpUserJoinModel interface {
		Insert(ctx context.Context, data *HelpUserJoin) error

		FindOne(ctx context.Context, id int64) (*HelpUserJoin, error)
		Update(ctx context.Context, data *HelpUserJoin) error

		Delete(ctx context.Context, id int64) error
	}

	defaultHelpUserJoinModel struct {
		conn  *gorm.DB
		table string
	}

	HelpUserJoin struct {
		Id        int64     `gorm:"column:id"`
		Uid       int64     `gorm:"column:uid"`        // 用户id
		CreatedAt time.Time `gorm:"column:created_at"` // 创建时间
		UpdatedAt time.Time `gorm:"column:updated_at"` // 更新时间
	}
)

var QHelpUserJoin HelpUserJoin

func init() {
	gormcsql.InitField(&QHelpUserJoin)
}

func (HelpUserJoin) TableName() string {
	return "`help_user_join`"
}

func newHelpUserJoinModel(conn *gorm.DB) *defaultHelpUserJoinModel {
	return &defaultHelpUserJoinModel{
		conn:  conn,
		table: "`help_user_join`",
	}
}

func (m *defaultHelpUserJoinModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&HelpUserJoin{}, id).Error

	return err
}

func (m *defaultHelpUserJoinModel) FindOne(ctx context.Context, id int64) (*HelpUserJoin, error) {
	var resp HelpUserJoin
	err := m.conn.WithContext(ctx).Model(&HelpUserJoin{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultHelpUserJoinModel) Insert(ctx context.Context, data *HelpUserJoin) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultHelpUserJoinModel) Update(ctx context.Context, data *HelpUserJoin) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

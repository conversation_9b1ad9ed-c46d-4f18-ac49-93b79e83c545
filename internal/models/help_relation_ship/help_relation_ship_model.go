package help_relation_ship

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                       = gormcsql.InitField
	_ HelpRelationShipModel = (*customHelpRelationShipModel)(nil)
)

type (
	// HelpRelationShipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customHelpRelationShipModel.
	HelpRelationShipModel interface {
		helpRelationShipModel
		customHelpRelationShipLogicModel
	}

	customHelpRelationShipLogicModel interface {
		WithSession(tx *gorm.DB) HelpRelationShipModel
	}

	customHelpRelationShipModel struct {
		*defaultHelpRelationShipModel
	}
)

func (c customHelpRelationShipModel) WithSession(tx *gorm.DB) HelpRelationShipModel {
	newModel := *c.defaultHelpRelationShipModel
	c.defaultHelpRelationShipModel = &newModel
	c.conn = tx
	return c
}

// NewHelpRelationShipModel returns a model for the database table.
func NewHelpRelationShipModel(conn *gorm.DB) HelpRelationShipModel {
	return &customHelpRelationShipModel{
		defaultHelpRelationShipModel: newHelpRelationShipModel(conn),
	}
}

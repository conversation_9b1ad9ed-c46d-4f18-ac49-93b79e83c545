// Code generated by goctl. DO NOT EDIT.

package help_relation_ship

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	helpRelationShipModel interface {
		Insert(ctx context.Context, data *HelpRelationShip) error

		FindOne(ctx context.Context, id int64) (*HelpRelationShip, error)
		FindOneByUidRefUid(ctx context.Context, uid int64, refUid int64) (*HelpRelationShip, error)
		Update(ctx context.Context, data *HelpRelationShip) error

		Delete(ctx context.Context, id int64) error
	}

	defaultHelpRelationShipModel struct {
		conn  *gorm.DB
		table string
	}

	HelpRelationShip struct {
		Id           int64          `gorm:"column:id"`
		Uid          int64          `gorm:"column:uid"`           // 新注册用户
		Name         string         `gorm:"column:name"`          // 用户名
		Avatar       string         `gorm:"column:avatar"`        // 用户头像
		RefUid       int64          `gorm:"column:ref_uid"`       // 邀请人
		RegisterTime int64          `gorm:"column:register_time"` // 注册时间
		Valid        int64          `gorm:"column:valid"`         // 1:有效, 0:无效（邀请关系不符合），备用字段
		Status       int64          `gorm:"column:status"`        // 状态，备用字段
		ExtraData    sql.NullString `gorm:"column:extra_data"`    // 扩展信息
		BusinessType int64          `gorm:"column:business_type"` // 业务类型
		CreatedAt    time.Time      `gorm:"column:created_at"`
		UpdatedAt    time.Time      `gorm:"column:updated_at"`
	}
)

var QHelpRelationShip HelpRelationShip

func init() {
	gormcsql.InitField(&QHelpRelationShip)
}

func (HelpRelationShip) TableName() string {
	return "`help_relation_ship`"
}

func newHelpRelationShipModel(conn *gorm.DB) *defaultHelpRelationShipModel {
	return &defaultHelpRelationShipModel{
		conn:  conn,
		table: "`help_relation_ship`",
	}
}

func (m *defaultHelpRelationShipModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&HelpRelationShip{}, id).Error

	return err
}

func (m *defaultHelpRelationShipModel) FindOne(ctx context.Context, id int64) (*HelpRelationShip, error) {
	var resp HelpRelationShip
	err := m.conn.WithContext(ctx).Model(&HelpRelationShip{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultHelpRelationShipModel) FindOneByUidRefUid(ctx context.Context, uid int64, refUid int64) (*HelpRelationShip, error) {
	var resp HelpRelationShip
	err := m.conn.WithContext(ctx).Model(&HelpRelationShip{}).Where("`uid` = ? and `ref_uid` = ?", uid, refUid).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err
}

func (m *defaultHelpRelationShipModel) Insert(ctx context.Context, data *HelpRelationShip) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultHelpRelationShipModel) Update(ctx context.Context, data *HelpRelationShip) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

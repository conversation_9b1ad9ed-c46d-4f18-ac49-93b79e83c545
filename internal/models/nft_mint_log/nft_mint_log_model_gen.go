// Code generated by goctl. DO NOT EDIT.

package nft_mint_log

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"bitbucket.org/gatebackend/go-zero/core/decimal"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	nftMintLogModel interface {
		Insert(ctx context.Context, data *NftMintLog) error

		FindOne(ctx context.Context, id int64) (*NftMintLog, error)
		Update(ctx context.Context, data *NftMintLog) error

		Delete(ctx context.Context, id int64) error
	}

	defaultNftMintLogModel struct {
		conn  *gorm.DB
		table string
	}

	NftMintLog struct {
		Id               int64           `gorm:"column:id"`                   // 数据ID
		NftId            int64           `gorm:"column:nft_id"`               // nft id
		ParentNftId      int64           `gorm:"column:parent_nft_id"`        // 上级ID 用于分层 一期6个
		NftUserSubTaskId int64           `gorm:"column:nft_user_sub_task_id"` // 任务记录ID 用于判断
		WalletAddress    string          `gorm:"column:wallet_address"`       // 钱包标识 去中心化领取或购买的钱包ID 用于判断用户重复领取或购买
		Uid              int64           `gorm:"column:uid"`                  // uid 中心化领取或购买的用户ID
		ActivityId       int64           `gorm:"column:activity_id"`          // 活动ID
		TokenUrl         string          `gorm:"column:token_url"`            // 回调传回的参数 https://data.gatenft.io/redbullNFT/1 1对应的就是nft ID
		TokenId          string          `gorm:"column:token_id"`             // 回调传回的参数
		OperationId      int64           `gorm:"column:operation_id"`         // 操作ID 用于透传 方便判断重复铸造相同nft的操作
		MintStatus       int64           `gorm:"column:mint_status"`          // 铸造状态 1初始化 2进行中 3成功 4失败
		MintStatusCause  string          `gorm:"column:mint_status_cause"`    // 铸造失败状态对应的原因
		Mid              int64           `gorm:"column:mid"`                  // 中心化铸造表ID
		PayType          int64           `gorm:"column:pay_type"`             // 支付类型 1中心化 2去中心化
		Expenditure      decimal.Decimal `gorm:"column:expenditure"`          // 支出金额
		CreatedAt        time.Time       `gorm:"column:created_at"`           // 创建时间
		UpdatedAt        time.Time       `gorm:"column:updated_at"`           // 更新时间
		SceneType        int64     `gorm:"column:scene_type"`        // 任务场景：0-活动期间获得、1-惊喜加时阶段
	}
)

var QNftMintLog NftMintLog

func init() {
	gormcsql.InitField(&QNftMintLog)
}

func (NftMintLog) TableName() string {
	return "`nft_mint_log`"
}

func newNftMintLogModel(conn *gorm.DB) *defaultNftMintLogModel {
	return &defaultNftMintLogModel{
		conn:  conn,
		table: "`nft_mint_log`",
	}
}

func (m *defaultNftMintLogModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftMintLog{}, id).Error

	return err
}

func (m *defaultNftMintLogModel) FindOne(ctx context.Context, id int64) (*NftMintLog, error) {
	var resp NftMintLog
	err := m.conn.WithContext(ctx).Model(&NftMintLog{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftMintLogModel) Insert(ctx context.Context, data *NftMintLog) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftMintLogModel) Update(ctx context.Context, data *NftMintLog) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

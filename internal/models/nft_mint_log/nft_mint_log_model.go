package nft_mint_log

import (
	"context"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                 = gormcsql.InitField
	_ NftMintLogModel = (*customNftMintLogModel)(nil)
)

type (
	// NftMintLogModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftMintLogModel.
	NftMintLogModel interface {
		nftMintLogModel
		customNftMintLogLogicModel
	}

	customNftMintLogLogicModel interface {
		WithSession(tx *gorm.DB) NftMintLogModel
		CountByFilter(ctx context.Context, filter map[string]interface{}) (count int64, err error)
		ListByFilter(ctx context.Context, mintStatus int64, endTime time.Time) (result []*NftMintLog, err error)
		ListByFilterCenter(ctx context.Context, mintStatus int64, endTime time.Time, limit int64) (result []*NftMintLog, err error)
	}

	customNftMintLogModel struct {
		*defaultNftMintLogModel
	}
)

func (c customNftMintLogModel) WithSession(tx *gorm.DB) NftMintLogModel {
	newModel := *c.defaultNftMintLogModel
	c.defaultNftMintLogModel = &newModel
	c.conn = tx
	return c
}

// NewNftMintLogModel returns a model for the database table.
func NewNftMintLogModel(conn *gorm.DB) NftMintLogModel {
	return &customNftMintLogModel{
		defaultNftMintLogModel: newNftMintLogModel(conn),
	}
}

const (
	MINT_STATUS_INIT        = 1 // 初始化
	MINT_STATUS_IN_PROGRESS = 2 // 进行中
	MINT_STATUS_SUCCESS     = 3 // 成功
	MINT_STATUS_FAILED      = 4 // 失败
)

const (
	PAY_TYPE_CENTER    = 1 // 中心化
	PAY_TYPE_NO_CENTER = 2 // 去中心化
)

// 任务场景
const (
	SCENE_TYPE_ACTIVITY = 0 // 活动期间获得
	SCENE_TYPE_SURPRISE = 1 // 惊喜加时阶段
)

/**
 * 统计免费获得的NFT数量
 */
func (m *defaultNftMintLogModel) CountByFilter(ctx context.Context, filter map[string]interface{}) (count int64, err error) {
	query := m.conn.WithContext(ctx).Model(&NftMintLog{})

	// 添加过滤条件
	if len(filter) > 0 {
		query = query.Where(filter)
	}

	// 统计数量
	err = query.Count(&count).Error
	return
}

func (m *defaultNftMintLogModel) ListByFilter(ctx context.Context, mintStatus int64, endTime time.Time) (result []*NftMintLog, err error) {
	query := m.conn.WithContext(ctx).Model(&NftMintLog{})

	query = query.Where("mint_status <? and created_at<=? and wallet_address!=''", mintStatus, endTime)

	err = query.Find(&result).Error
	return
}

/**
 * 查询中心化，未铸造成功的数据
 * @param limit 限制数量
 */
func (m *defaultNftMintLogModel) ListByFilterCenter(ctx context.Context, mintStatus int64, endTime time.Time, limit int64) (result []*NftMintLog, err error) {
	query := m.conn.WithContext(ctx).Model(&NftMintLog{})

	query = query.Where("mint_status != ? and created_at <= ? and created_at >= ? and pay_type = ? and scene_type = ?", mintStatus, endTime, endTime.Add(-1*time.Hour*48), PAY_TYPE_CENTER, SCENE_TYPE_ACTIVITY)

	// 限制数量
	if limit > 0 {
		query = query.Limit(int(limit))
	}

	err = query.Find(&result).Error
	return
}

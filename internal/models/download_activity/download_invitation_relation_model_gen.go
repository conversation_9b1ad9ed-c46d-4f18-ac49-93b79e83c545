// Code generated by goctl. DO NOT EDIT.

package download_activity

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	downloadInvitationRelationModel interface {
		Insert(ctx context.Context, data *DownloadInvitationRelation) error

		FindOne(ctx context.Context, id int64) (*DownloadInvitationRelation, error)
		Update(ctx context.Context, data *DownloadInvitationRelation) error

		Delete(ctx context.Context, id int64) error
	}

	defaultDownloadInvitationRelationModel struct {
		conn  *gorm.DB
		table string
	}

	DownloadInvitationRelation struct {
		Id        int64     `gorm:"column:id"`       // 主键id
		Uid       int64     `gorm:"column:uid"`      // 用uid
		RefUid    int64     `gorm:"column:ref_uid"`  // 邀请人uid
		Status    int64     `gorm:"column:status"`   // 任务状态 1 进行中, 2 完成
		Progress  int64     `gorm:"column:progress"` // 任务进度,任务自行判定
		CreatedAt time.Time `gorm:"column:created_at"`
		UpdatedAt time.Time `gorm:"column:updated_at"`
	}
)

var QDownloadInvitationRelation DownloadInvitationRelation

func init() {
	gormcsql.InitField(&QDownloadInvitationRelation)
}

func (DownloadInvitationRelation) TableName() string {
	return "`download_invitation_relation`"
}

func newDownloadInvitationRelationModel(conn *gorm.DB) *defaultDownloadInvitationRelationModel {
	return &defaultDownloadInvitationRelationModel{
		conn:  conn,
		table: "`download_invitation_relation`",
	}
}

func (m *defaultDownloadInvitationRelationModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&DownloadInvitationRelation{}, id).Error

	return err
}

func (m *defaultDownloadInvitationRelationModel) FindOne(ctx context.Context, id int64) (*DownloadInvitationRelation, error) {
	var resp DownloadInvitationRelation
	err := m.conn.WithContext(ctx).Model(&DownloadInvitationRelation{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultDownloadInvitationRelationModel) Insert(ctx context.Context, data *DownloadInvitationRelation) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultDownloadInvitationRelationModel) Update(ctx context.Context, data *DownloadInvitationRelation) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

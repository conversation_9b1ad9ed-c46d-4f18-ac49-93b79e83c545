package download_activity

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"fmt"
	"gateio_service_marketing_activity/internal/consts/download"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                         = gormcsql.InitField
	_ DownloadTaskRecordModel = (*customDownloadTaskRecordModel)(nil)
)

type (
	// DownloadTaskRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customDownloadTaskRecordModel.
	DownloadTaskRecordModel interface {
		downloadTaskRecordModel
		customDownloadTaskRecordLogicModel
	}

	customDownloadTaskRecordLogicModel interface {
		WithSession(tx *gorm.DB) DownloadTaskRecordModel
		GetTaskRecordListDoing(ctx context.Context, id int64) ([]*DownloadTaskRecord, error)
		GetTaskRecordByUid(ctx context.Context, uid int64) (*DownloadTaskRecord, error)
		GetTaskRecordCountByTypeRegister(ctx context.Context, uid int64) (int64, error)
		UpdateUserTaskByType(ctx context.Context, uid int64, taskId int64, status int64, process int64) error
		GetTaskRecordsByUID(ctx context.Context, uid int64) ([]*DownloadTaskRecord, error)
		FindLatestTaskRecordsByAID(ctx context.Context, aid, num, status int64) ([]*DownloadTaskRecord, error)
		BatchInsertRecords(ctx context.Context, records []*DownloadTaskRecord) error
		CountUsersByAID(ctx context.Context, aid int64) (int64, error)
	}

	customDownloadTaskRecordModel struct {
		*defaultDownloadTaskRecordModel
	}
)

func (c customDownloadTaskRecordModel) WithSession(tx *gorm.DB) DownloadTaskRecordModel {
	newModel := *c.defaultDownloadTaskRecordModel
	c.defaultDownloadTaskRecordModel = &newModel
	c.conn = tx
	return &c
}

// NewDownloadTaskRecordModel returns a model for the database table.
func NewDownloadTaskRecordModel(conn *gorm.DB) DownloadTaskRecordModel {
	return &customDownloadTaskRecordModel{
		defaultDownloadTaskRecordModel: newDownloadTaskRecordModel(conn),
	}
}

/*
*
获取活动信息 主要是开始结束时间
*/
func (m *customDownloadTaskRecordModel) GetTaskRecordListDoing(ctx context.Context, id int64) ([]*DownloadTaskRecord, error) {
	var result []*DownloadTaskRecord
	query := m.conn.WithContext(ctx).Model(&DownloadTaskRecord{})
	err := query.Where("id> ?", id).Where("status=?", 1).Limit(100).Scan(&result).Error
	if err != nil {
		return result, err
	}
	return result, nil
}

/*
*
获取活动信息 主要是开始结束时间
*/
func (m *customDownloadTaskRecordModel) GetTaskRecordByUid(ctx context.Context, uid int64) (*DownloadTaskRecord, error) {
	var result DownloadTaskRecord
	query := m.conn.WithContext(ctx).Model(&DownloadTaskRecord{})
	err := query.Where("uid=?", uid).Find(&result).Error
	if err != nil {
		return &result, err
	}
	return &result, nil
}

func (m *customDownloadTaskRecordModel) GetTaskRecordCountByTypeRegister(ctx context.Context, uid int64) (int64, error) {
	var result int64
	query := m.conn.WithContext(ctx).Model(&DownloadTaskRecord{})
	err := query.Where("uid=? and  status=? and task_id=?", uid, 2, download.INVITEID).Count(&result).Error
	if err != nil {
		return result, err
	}
	return result, nil
}

func (m *customDownloadTaskRecordModel) UpdateUserTaskByType(ctx context.Context, uid int64, task_id int64, status int64, progress int64) error {
	query := m.conn.WithContext(ctx).Model(&DownloadTaskRecord{})
	err := query.Where("uid=? and  task_id=?", uid, task_id).Updates(map[string]interface{}{"status": status, "progress": progress}).Error
	fmt.Println("update task record", task_id, uid, status, progress)
	return err
}

func (m *defaultDownloadTaskRecordModel) GetTaskRecordsByUID(ctx context.Context, uid int64) ([]*DownloadTaskRecord, error) {
	if uid <= 0 {
		return nil, nil
	}
	var resp []*DownloadTaskRecord
	err := m.conn.WithContext(ctx).Model(&DownloadTaskRecord{}).
		Where("uid", uid).Find(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, err
}

func (m *defaultDownloadTaskRecordModel) FindLatestTaskRecordsByAID(ctx context.Context, aid, num, status int64) ([]*DownloadTaskRecord, error) {
	if aid <= 0 || num <= 0 {
		return nil, nil
	}

	var resp []*DownloadTaskRecord
	err := m.conn.WithContext(ctx).Model(&DownloadTaskRecord{}).
		Where("aid = ? and status = ?", aid, status).Order("id desc").Limit(int(num)).Find(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, err
}

func (m *defaultDownloadTaskRecordModel) BatchInsertRecords(ctx context.Context, records []*DownloadTaskRecord) error {
	return m.conn.WithContext(ctx).Model(&DownloadTaskRecord{}).CreateInBatches(records, 10).Error
}

func (m *defaultDownloadTaskRecordModel) CountUsersByAID(ctx context.Context, aid int64) (int64, error) {
	var cnt int64
	err := m.conn.WithContext(ctx).Model(&DownloadTaskRecord{}).Select([]string{"uid"}).
		Where("aid", aid).Distinct("uid").Count(&cnt).Error
	if err != nil {
		return 0, err
	}
	return cnt, nil
}

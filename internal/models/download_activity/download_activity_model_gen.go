// Code generated by goctl. DO NOT EDIT.

package download_activity

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	downloadActivityModel interface {
		Insert(ctx context.Context, data *DownloadActivity) error

		FindOne(ctx context.Context, id int64) (*DownloadActivity, error)
		Update(ctx context.Context, data *DownloadActivity) error

		Delete(ctx context.Context, id int64) error
	}

	defaultDownloadActivityModel struct {
		conn  *gorm.DB
		table string
	}

	DownloadActivity struct {
		Id           int64     `gorm:"column:id"`             // 活动id
		StartTime    int64     `gorm:"column:start_time"`     // 开始时间时间
		EndTime      int64     `gorm:"column:end_time"`       // 结束时间时间
		ExtraUserNum int64     `gorm:"column:extra_user_num"` // 额外的人数
		CreatedAt    time.Time `gorm:"column:created_at"`
		UpdatedAt    time.Time `gorm:"column:updated_at"`
	}
)

var QDownloadActivity DownloadActivity

func init() {
	gormcsql.InitField(&QDownloadActivity)
}

func (DownloadActivity) TableName() string {
	return "`download_activity`"
}

func newDownloadActivityModel(conn *gorm.DB) *defaultDownloadActivityModel {
	return &defaultDownloadActivityModel{
		conn:  conn,
		table: "`download_activity`",
	}
}

func (m *defaultDownloadActivityModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&DownloadActivity{}, id).Error

	return err
}

func (m *defaultDownloadActivityModel) FindOne(ctx context.Context, id int64) (*DownloadActivity, error) {
	var resp DownloadActivity
	err := m.conn.WithContext(ctx).Model(&DownloadActivity{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultDownloadActivityModel) Insert(ctx context.Context, data *DownloadActivity) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultDownloadActivityModel) Update(ctx context.Context, data *DownloadActivity) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

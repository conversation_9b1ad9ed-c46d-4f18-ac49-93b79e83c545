package download_activity

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                                 = gormcsql.InitField
	_ DownloadInvitationRelationModel = (*customDownloadInvitationRelationModel)(nil)
)

type (
	// DownloadInvitationRelationModel is an interface to be customized, add more methods here,
	// and implement the added methods in customDownloadInvitationRelationModel.
	DownloadInvitationRelationModel interface {
		downloadInvitationRelationModel
		customDownloadInvitationRelationLogicModel
	}

	customDownloadInvitationRelationLogicModel interface {
		WithSession(tx *gorm.DB) DownloadInvitationRelationModel
		InsertOne(ctx context.Context, uid int64, ref_uid int64, status int64) (int64, error)
		GetRefUserDownNum(ctx context.Context, uid int64) (int64, error)
		GetRefUserByRefUidAndId(ctx context.Context, refuid int64, id int64) ([]*DownloadInvitationRelation, error)
		UpdateUserTaskByUid(ctx context.Context, uid, status, progress int64) error
		GetTotalByRefUid(ctx context.Context, refUid int64) (int64, error)
	}

	customDownloadInvitationRelationModel struct {
		*defaultDownloadInvitationRelationModel
	}
)

func (c customDownloadInvitationRelationModel) WithSession(tx *gorm.DB) DownloadInvitationRelationModel {
	newModel := *c.defaultDownloadInvitationRelationModel
	c.defaultDownloadInvitationRelationModel = &newModel
	c.conn = tx
	return &c
}

// NewDownloadInvitationRelationModel returns a model for the database table.
func NewDownloadInvitationRelationModel(conn *gorm.DB) DownloadInvitationRelationModel {
	return &customDownloadInvitationRelationModel{
		defaultDownloadInvitationRelationModel: newDownloadInvitationRelationModel(conn),
	}
}

func (m *customDownloadInvitationRelationModel) InsertOne(ctx context.Context, uid int64, ref_uid int64, status int64) (int64, error) {
	one := &DownloadInvitationRelation{RefUid: ref_uid, Uid: uid, Status: status}
	query := m.conn.WithContext(ctx).Model(&DownloadInvitationRelation{})

	err := query.Create(one).Error
	if err != nil {
		return 0, err
	}
	return one.Id, nil
}

func (m *customDownloadInvitationRelationModel) GetRefUserDownNum(ctx context.Context, uid int64) (int64, error) {
	var count int64
	err := m.conn.WithContext(ctx).Model(&DownloadInvitationRelation{}).Where("ref_uid=? and status=?", uid, 2).Count(&count).Error
	return count, err
}
func (m *customDownloadInvitationRelationModel) GetRefUserByRefUidAndId(ctx context.Context, refuid int64, id int64) ([]*DownloadInvitationRelation, error) {
	var ret []*DownloadInvitationRelation
	err := m.conn.WithContext(ctx).Model(&DownloadInvitationRelation{}).Where("ref_uid=? and id>? and status=?", refuid, id, 1).Order("id asc").Limit(100).Scan(&ret).Error
	return ret, err
}
func (m *customDownloadInvitationRelationModel) UpdateUserTaskByUid(ctx context.Context, uid, status, progress int64) error {
	return m.conn.WithContext(ctx).Model(&DownloadInvitationRelation{}).Where("uid=? ", uid).Updates(map[string]interface{}{"status": status, "progress": progress}).Error
}
func (m *customDownloadInvitationRelationModel) GetTotalByRefUid(ctx context.Context, refUid int64) (int64, error) {
	var ret int64
	err := m.conn.WithContext(ctx).Model(&DownloadInvitationRelation{}).Where("ref_uid=? and status=? ", refUid, 2).Count(&ret).Error
	return ret, err
}

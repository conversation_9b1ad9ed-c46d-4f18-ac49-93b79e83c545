// Code generated by goctl. DO NOT EDIT.

package download_activity

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	downloadTaskRecordModel interface {
		Insert(ctx context.Context, data *DownloadTaskRecord) error

		FindOne(ctx context.Context, id int64) (*DownloadTaskRecord, error)
		Update(ctx context.Context, data *DownloadTaskRecord) error

		Delete(ctx context.Context, id int64) error
	}

	defaultDownloadTaskRecordModel struct {
		conn  *gorm.DB
		table string
	}

	DownloadTaskRecord struct {
		Id         int64     `gorm:"column:id"`          // 主键id
		Aid        int64     `gorm:"column:aid"`         // 活动id
		Uid        int64     `gorm:"column:uid"`         // 用户id
		Nickname   string    `gorm:"column:nickname"`    // 用户昵称
		TaskId     int64     `gorm:"column:task_id"`     // 任务id
		Status     int64     `gorm:"column:status"`      // 任务状态 1 进行中, 2 完成 3失败
		Progress   int64     `gorm:"column:progress"`    // 任务进度,任务自行判定
		CouponType string    `gorm:"column:coupon_type"` // 券类型。contract_bonus_new:合约体验券、commission_rebate:手续费返现券
		CreatedAt  time.Time `gorm:"column:created_at"`
		UpdatedAt  time.Time `gorm:"column:updated_at"`
	}
)

var QDownloadTaskRecord DownloadTaskRecord

func init() {
	gormcsql.InitField(&QDownloadTaskRecord)
}

func (DownloadTaskRecord) TableName() string {
	return "`download_task_record`"
}

func newDownloadTaskRecordModel(conn *gorm.DB) *defaultDownloadTaskRecordModel {
	return &defaultDownloadTaskRecordModel{
		conn:  conn,
		table: "`download_task_record`",
	}
}

func (m *defaultDownloadTaskRecordModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&DownloadTaskRecord{}, id).Error

	return err
}

func (m *defaultDownloadTaskRecordModel) FindOne(ctx context.Context, id int64) (*DownloadTaskRecord, error) {
	var resp DownloadTaskRecord
	err := m.conn.WithContext(ctx).Model(&DownloadTaskRecord{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultDownloadTaskRecordModel) Insert(ctx context.Context, data *DownloadTaskRecord) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultDownloadTaskRecordModel) Update(ctx context.Context, data *DownloadTaskRecord) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

package download_activity

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"database/sql"
	"errors"
	"gorm.io/gorm"
	"time"
)

// avoid unused err.
var (
	_                       = gormcsql.InitField
	_ DownloadActivityModel = (*customDownloadActivityModel)(nil)
)

type (
	// DownloadActivityModel is an interface to be customized, add more methods here,
	// and implement the added methods in customDownloadActivityModel.
	DownloadActivityModel interface {
		downloadActivityModel
		customDownloadActivityLogicModel
	}

	customDownloadActivityLogicModel interface {
		WithSession(tx *gorm.DB) DownloadActivityModel
		GetLastActivityInfo(ctx context.Context) (DownloadActivity, error)
		GetActivityById(ctx context.Context, aid int64) (DownloadActivity, error)
		InsertActivity(ctx context.Context, insertInfo DownloadActivity) (int64, error)
		GetValidActivity(ctx context.Context, reqTime time.Time) (*DownloadActivity, error)
		GetLatestActivity(ctx context.Context) (*DownloadActivity, error)
	}

	customDownloadActivityModel struct {
		*defaultDownloadActivityModel
	}
)

func (c customDownloadActivityModel) WithSession(tx *gorm.DB) DownloadActivityModel {
	newModel := *c.defaultDownloadActivityModel
	c.defaultDownloadActivityModel = &newModel
	c.conn = tx
	return &c
}

// NewDownloadActivityModel returns a model for the database table.
func NewDownloadActivityModel(conn *gorm.DB) DownloadActivityModel {
	return &customDownloadActivityModel{
		defaultDownloadActivityModel: newDownloadActivityModel(conn),
	}
}

/**
* 获取邀请的用户列表
 */
func (m *customDownloadActivityModel) GetLastActivityInfo(ctx context.Context) (DownloadActivity, error) {
	var result DownloadActivity
	query := m.conn.WithContext(ctx).Model(&DownloadActivity{})
	err := query.Order("id desc ").Find(&result).Error
	if err != nil {
		return result, err
	}
	return result, nil

}

/*
*
写入活动
*/
func (m *customDownloadActivityModel) InsertActivity(ctx context.Context, insertInfo DownloadActivity) (int64, error) {
	err := m.conn.WithContext(ctx).Model(&DownloadActivity{}).Create(insertInfo).Error
	if err != nil {
		return 0, err
	}
	return insertInfo.Id, nil
}

/*
*
获取活动信息 主要是开始结束时间
*/
func (m *customDownloadActivityModel) GetActivityById(ctx context.Context, aid int64) (DownloadActivity, error) {
	var result DownloadActivity
	query := m.conn.WithContext(ctx).Model(&DownloadActivity{})
	err := query.Where("id=?", aid).Find(&result).Error
	if err != nil {
		return result, err
	}
	return result, nil
}

func (m *defaultDownloadActivityModel) GetValidActivity(ctx context.Context, reqTime time.Time) (*DownloadActivity, error) {
	var resp DownloadActivity
	err := m.conn.WithContext(ctx).Model(&DownloadActivity{}).
		Where("`start_time`<= @reqTime and `end_time` >= @reqTime", sql.Named("reqTime", reqTime.Unix())).
		Take(&resp).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &resp, err
}

func (m *defaultDownloadActivityModel) GetLatestActivity(ctx context.Context) (*DownloadActivity, error) {
	var resp DownloadActivity
	err := m.conn.WithContext(ctx).Model(&DownloadActivity{}).
		Order("`id` desc").Limit(1).
		Take(&resp).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &resp, err
}

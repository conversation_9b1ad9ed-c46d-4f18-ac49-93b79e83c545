package nft_user_invite_record

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"database/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                          = gormcsql.InitField
	_ NftUserInviteRecordModel = (*customNftUserInviteRecordModel)(nil)
)

type (
	// NftUserInviteRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftUserInviteRecordModel.
	NftUserInviteRecordModel interface {
		nftUserInviteRecordModel
		customNftUserInviteRecordLogicModel
	}

	customNftUserInviteRecordLogicModel interface {
		WithSession(tx *gorm.DB) NftUserInviteRecordModel
		FindByUID(ctx context.Context, uid int64) (*NftUserInviteRecord, error)
	}

	customNftUserInviteRecordModel struct {
		*defaultNftUserInviteRecordModel
	}
)

func (c customNftUserInviteRecordModel) WithSession(tx *gorm.DB) NftUserInviteRecordModel {
	newModel := *c.defaultNftUserInviteRecordModel
	c.defaultNftUserInviteRecordModel = &newModel
	c.conn = tx
	return c
}

func (m *defaultNftUserInviteRecordModel) FindByUID(ctx context.Context, uid int64) (*NftUserInviteRecord, error) {
	var resp NftUserInviteRecord
	err := m.conn.WithContext(ctx).Model(&NftUserInviteRecord{}).Where("`uid` = @uid", sql.Named("uid", uid)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

// NewNftUserInviteRecordModel returns a model for the database table.
func NewNftUserInviteRecordModel(conn *gorm.DB) NftUserInviteRecordModel {
	return &customNftUserInviteRecordModel{
		defaultNftUserInviteRecordModel: newNftUserInviteRecordModel(conn),
	}
}

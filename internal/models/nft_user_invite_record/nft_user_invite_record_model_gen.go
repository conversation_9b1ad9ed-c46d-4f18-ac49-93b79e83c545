// Code generated by goctl. DO NOT EDIT.

package nft_user_invite_record

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	nftUserInviteRecordModel interface {
		Insert(ctx context.Context, data *NftUserInviteRecord) error

		FindOne(ctx context.Context, id int64) (*NftUserInviteRecord, error)
		FindOneByUidRefUid(ctx context.Context, uid int64, refUid int64) (*NftUserInviteRecord, error)
		Update(ctx context.Context, data *NftUserInviteRecord) error

		Delete(ctx context.Context, id int64) error
	}

	defaultNftUserInviteRecordModel struct {
		conn  *gorm.DB
		table string
	}

	NftUserInviteRecord struct {
		Id        int64          `gorm:"column:id"`
		Uid       int64          `gorm:"column:uid"`        // 新注册用户
		RefUid    int64          `gorm:"column:ref_uid"`    // 邀请人
		Ch        string         `gorm:"column:ch"`         // 注册码
		RegTime   int64          `gorm:"column:reg_time"`   // 注册时间
		ExtraInfo sql.NullString `gorm:"column:extra_info"` // 额外信息
		CreatedAt time.Time      `gorm:"column:created_at"`
		UpdatedAt time.Time      `gorm:"column:updated_at"`
	}
)

var QNftUserInviteRecord NftUserInviteRecord

func init() {
	gormcsql.InitField(&QNftUserInviteRecord)
}

func (NftUserInviteRecord) TableName() string {
	return "`nft_user_invite_record`"
}

func newNftUserInviteRecordModel(conn *gorm.DB) *defaultNftUserInviteRecordModel {
	return &defaultNftUserInviteRecordModel{
		conn:  conn,
		table: "`nft_user_invite_record`",
	}
}

func (m *defaultNftUserInviteRecordModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftUserInviteRecord{}, id).Error

	return err
}

func (m *defaultNftUserInviteRecordModel) FindOne(ctx context.Context, id int64) (*NftUserInviteRecord, error) {
	var resp NftUserInviteRecord
	err := m.conn.WithContext(ctx).Model(&NftUserInviteRecord{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftUserInviteRecordModel) FindOneByUidRefUid(ctx context.Context, uid int64, refUid int64) (*NftUserInviteRecord, error) {
	var resp NftUserInviteRecord
	err := m.conn.WithContext(ctx).Model(&NftUserInviteRecord{}).Where("`uid` = ? and `ref_uid` = ?", uid, refUid).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err
}

func (m *defaultNftUserInviteRecordModel) Insert(ctx context.Context, data *NftUserInviteRecord) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftUserInviteRecordModel) Update(ctx context.Context, data *NftUserInviteRecord) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

package help_user_prize

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                    = gormcsql.InitField
	_ HelpUserPrizeModel = (*customHelpUserPrizeModel)(nil)
)

type (
	// HelpUserPrizeModel is an interface to be customized, add more methods here,
	// and implement the added methods in customHelpUserPrizeModel.
	HelpUserPrizeModel interface {
		helpUserPrizeModel
		customHelpUserPrizeLogicModel
	}

	customHelpUserPrizeLogicModel interface {
		WithSession(tx *gorm.DB) HelpUserPrizeModel
	}

	customHelpUserPrizeModel struct {
		*defaultHelpUserPrizeModel
	}
)

func (c customHelpUserPrizeModel) WithSession(tx *gorm.DB) HelpUserPrizeModel {
	newModel := *c.defaultHelpUserPrizeModel
	c.defaultHelpUserPrizeModel = &newModel
	c.conn = tx
	return c
}

// NewHelpUserPrizeModel returns a model for the database table.
func NewHelpUserPrizeModel(conn *gorm.DB) HelpUserPrizeModel {
	return &customHelpUserPrizeModel{
		defaultHelpUserPrizeModel: newHelpUserPrizeModel(conn),
	}
}

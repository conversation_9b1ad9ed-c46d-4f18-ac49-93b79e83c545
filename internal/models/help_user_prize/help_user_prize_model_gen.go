// Code generated by goctl. DO NOT EDIT.

package help_user_prize

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	helpUserPrizeModel interface {
		Insert(ctx context.Context, data *HelpUserPrize) error

		FindOne(ctx context.Context, id int64) (*HelpUserPrize, error)
		FindOneByUidTaskId(ctx context.Context, uid int64, taskId int64) (*HelpUserPrize, error)
		Update(ctx context.Context, data *HelpUserPrize) error

		Delete(ctx context.Context, id int64) error
	}

	defaultHelpUserPrizeModel struct {
		conn  *gorm.DB
		table string
	}

	HelpUserPrize struct {
		Id              int64          `gorm:"column:id"`                // 主键ID
		Uid             int64          `gorm:"column:uid"`               // 用户ID
		TaskId          int64          `gorm:"column:task_id"`           // 任务ID
		IsInviterReward int64          `gorm:"column:is_inviter_reward"` // 是否邀请人奖励:0被邀请人,1邀请人
		PrizeId         int64          `gorm:"column:prize_id"`          // 奖品id
		PrizeType       int64          `gorm:"column:prize_type"`        // 奖励类型
		PrizeValue      string         `gorm:"column:prize_value"`       // 奖品规格
		PrizeStatus     int64          `gorm:"column:prize_status"`      // 初始化/未领取:0,已发放:1,发放失败:2
		PrizeIssuedTime int64          `gorm:"column:prize_issued_time"` // 奖励发送时间
		ExtraData       sql.NullString `gorm:"column:extra_data"`        // 扩展信息
		BusinessType    int64          `gorm:"column:business_type"`     // 业务类型
		CreatedAt       time.Time      `gorm:"column:created_at"`        // 创建时间
		UpdatedAt       time.Time      `gorm:"column:updated_at"`        // 最后更新时间
	}
)

var QHelpUserPrize HelpUserPrize

func init() {
	gormcsql.InitField(&QHelpUserPrize)
}

func (HelpUserPrize) TableName() string {
	return "`help_user_prize`"
}

func newHelpUserPrizeModel(conn *gorm.DB) *defaultHelpUserPrizeModel {
	return &defaultHelpUserPrizeModel{
		conn:  conn,
		table: "`help_user_prize`",
	}
}

func (m *defaultHelpUserPrizeModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&HelpUserPrize{}, id).Error

	return err
}

func (m *defaultHelpUserPrizeModel) FindOne(ctx context.Context, id int64) (*HelpUserPrize, error) {
	var resp HelpUserPrize
	err := m.conn.WithContext(ctx).Model(&HelpUserPrize{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultHelpUserPrizeModel) FindOneByUidTaskId(ctx context.Context, uid int64, taskId int64) (*HelpUserPrize, error) {
	var resp HelpUserPrize
	err := m.conn.WithContext(ctx).Model(&HelpUserPrize{}).Where("`uid` = ? and `task_id` = ?", uid, taskId).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err
}

func (m *defaultHelpUserPrizeModel) Insert(ctx context.Context, data *HelpUserPrize) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultHelpUserPrizeModel) Update(ctx context.Context, data *HelpUserPrize) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

package models

import (
	"bitbucket.org/gatebackend/gorm-zero/gormc"
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"database/sql"
	"errors"
	"gorm.io/gorm"
)

// 状态 INIT:初始化;SUCCESS:成功; FAILED:失败
const CHECK_IN_STATUS_INIT = "INIT"       // 初始化
const CHECK_IN_STATUS_SUCCESS = "SUCCESS" // 成功
const CHECK_IN_STATUS_FAILED = "FAILED"   // 失败

// CheckInDetail 详细信息
type CheckInDetail struct {
	CouponSource string `json:"coupon_source"`
	CouponID     int64  `json:"coupon_id"`
	CouponAmount int64  `json:"coupon_amount"`
}

// avoid unused err.
var (
	_                             = gormcsql.InitField
	_ ActivityCheckInRecordsModel = (*customActivityCheckInRecordsModel)(nil)
)

type (
	// ActivityCheckInRecordsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customActivityCheckInRecordsModel.
	ActivityCheckInRecordsModel interface {
		activityCheckInRecordsModel
		customActivityCheckInRecordsLogicModel
	}

	customActivityCheckInRecordsLogicModel interface {
		WithSession(tx *gorm.DB) ActivityCheckInRecordsModel
		CreateCheckIn(ctx context.Context, d *ActivityCheckInRecords) (*ActivityCheckInRecords, error)
		FindTodayCheckInByUid(ctx context.Context, actId int64, uid int64, day int64) (*ActivityCheckInRecords, error)
		GetAllCheckInByUid(ctx context.Context, actId int64, uid int64) ([]ActivityCheckInRecords, error)
		CountCheckInNumByUid(ctx context.Context, actId int64, uid int64) (int64, error)
		UpdateStatusByUidDay(ctx context.Context, actId int64, uid int64, day int64, status string) error
		UpdateStatusById(ctx context.Context, id int64, status string) error
	}

	customActivityCheckInRecordsModel struct {
		*defaultActivityCheckInRecordsModel
	}
)

func (c customActivityCheckInRecordsModel) WithSession(tx *gorm.DB) ActivityCheckInRecordsModel {
	newModel := *c.defaultActivityCheckInRecordsModel
	c.defaultActivityCheckInRecordsModel = &newModel
	c.conn = tx
	return c
}

// NewActivityCheckInRecordsModel returns a model for the database table.
func NewActivityCheckInRecordsModel(conn *gorm.DB) ActivityCheckInRecordsModel {
	return &customActivityCheckInRecordsModel{
		defaultActivityCheckInRecordsModel: newActivityCheckInRecordsModel(conn),
	}
}

func (m *defaultActivityCheckInRecordsModel) CreateCheckIn(ctx context.Context, data *ActivityCheckInRecords) (*ActivityCheckInRecords, error) {
	// 调用GORM的Create方法插入数据
	result := m.conn.WithContext(ctx).Model(&ActivityCheckInRecords{}).Create(data)
	if result.Error != nil {
		return nil, result.Error
	}

	// 插入成功，返回创建的用户和nil错误
	return data, nil
}

func (m *defaultActivityCheckInRecordsModel) FindTodayCheckInByUid(ctx context.Context, actId int64, uid int64, day int64) (*ActivityCheckInRecords, error) {
	var resp ActivityCheckInRecords

	err := m.conn.WithContext(ctx).Model(&ActivityCheckInRecords{}).
		Where("`activity_id` = @activity_id", sql.Named("activity_id", actId)).
		Where("`uid` = @uid", sql.Named("uid", uid)).
		Where("`day` = @day", sql.Named("day", day)).
		Where("`status` = @status", sql.Named("status", CHECK_IN_STATUS_SUCCESS)).
		Take(&resp).Error
	if errors.Is(err, gormc.ErrNotFound) {
		return nil, nil
	}

	return &resp, err
}
func (m *defaultActivityCheckInRecordsModel) GetAllCheckInByUid(ctx context.Context, actId int64, uid int64) ([]ActivityCheckInRecords, error) {
	var resp []ActivityCheckInRecords

	err := m.conn.WithContext(ctx).Model(&ActivityCheckInRecords{}).
		Where("`activity_id` = @activity_id", sql.Named("activity_id", actId)).
		Where("`uid` = @uid", sql.Named("uid", uid)).
		Where("`status` = @status", sql.Named("status", CHECK_IN_STATUS_SUCCESS)).
		Scan(&resp).Error
	if errors.Is(err, gormc.ErrNotFound) {
		return []ActivityCheckInRecords{}, nil
	}
	return resp, err
}

func (m *defaultActivityCheckInRecordsModel) CountCheckInNumByUid(ctx context.Context, actId int64, uid int64) (int64, error) {
	var resp int64
	err := m.conn.WithContext(ctx).Model(&ActivityCheckInRecords{}).
		Where("`activity_id` = @activity_id", sql.Named("activity_id", actId)).
		Where("`uid` = @uid", sql.Named("uid", uid)).
		Where("`status` = @status", sql.Named("status", CHECK_IN_STATUS_SUCCESS)).
		Count(&resp).Error

	return resp, err
}

func (m *defaultActivityCheckInRecordsModel) UpdateStatusByUidDay(ctx context.Context, actId int64, uid int64, day int64, status string) error {
	return m.conn.WithContext(ctx).Model(&ActivityCheckInRecords{}).
		Where("`activity_id` = @activity_id", sql.Named("activity_id", actId)).
		Where("`uid` = @uid", sql.Named("uid", uid)).
		Where("`day` = @day", sql.Named("day", day)).
		Updates(&ActivityCheckInRecords{
			Status: status,
		}).Error
}

func (m *defaultActivityCheckInRecordsModel) UpdateStatusById(ctx context.Context, id int64, status string) error {
	return m.conn.WithContext(ctx).Model(&ActivityCheckInRecords{}).
		Where("`id` = @id", sql.Named("id", id)).
		Updates(&ActivityCheckInRecords{
			Status: status,
		}).Error
}

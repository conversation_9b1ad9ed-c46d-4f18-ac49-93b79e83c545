package cc_competitions

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                     = gormcsql.InitField
	_ CcCompetitionsModel = (*customCcCompetitionsModel)(nil)
)

type (
	// CcCompetitionsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customCcCompetitionsModel.
	CcCompetitionsModel interface {
		ccCompetitionsModel
		customCcCompetitionsLogicModel
	}

	customCcCompetitionsLogicModel interface {
		WithSession(tx *gorm.DB) CcCompetitionsModel
	}

	customCcCompetitionsModel struct {
		*defaultCcCompetitionsModel
	}
)

func (c customCcCompetitionsModel) WithSession(tx *gorm.DB) CcCompetitionsModel {
	newModel := *c.defaultCcCompetitionsModel
	c.defaultCcCompetitionsModel = &newModel
	c.conn = tx
	return c
}

// NewCcCompetitionsModel returns a model for the database table.
func NewCcCompetitionsModel(conn *gorm.DB) CcCompetitionsModel {
	return &customCcCompetitionsModel{
		defaultCcCompetitionsModel: newCcCompetitionsModel(conn),
	}
}

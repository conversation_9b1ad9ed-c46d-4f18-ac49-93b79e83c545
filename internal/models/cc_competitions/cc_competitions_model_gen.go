// Code generated by goctl. DO NOT EDIT.

package cc_competitions

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	ccCompetitionsModel interface {
		Insert(ctx context.Context, data *CcCompetitions) error

		FindOne(ctx context.Context, id int64) (*CcCompetitions, error)
		Update(ctx context.Context, data *CcCompetitions) error

		Delete(ctx context.Context, id int64) error
	}

	defaultCcCompetitionsModel struct {
		conn  *gorm.DB
		table string
	}

	CcCompetitions struct {
		Id               int64          `gorm:"column:id"`
		ParentId         int64          `gorm:"column:parent_id"`
		Lang             string         `gorm:"column:lang"`
		TypeId           int64          `gorm:"column:type_id"`
		CompetitionName  string         `gorm:"column:competition_name"`
		StartAt          int64          `gorm:"column:start_at"`
		EndAt            int64          `gorm:"column:end_at"`
		Sort             int64          `gorm:"column:sort"`
		Url              string         `gorm:"column:url"`
		Img              string         `gorm:"column:img"`    // 浅色模式入口图
		Hot              int64          `gorm:"column:hot"`    // 火热度
		Status           int64          `gorm:"column:status"` // 1上架 0下架
		Pinned           int64          `gorm:"column:pinned"` // 0未置顶，1已置顶
		PinnedAt         sql.NullTime   `gorm:"column:pinned_at"`
		ListedAt         sql.NullTime   `gorm:"column:listed_at"`
		CreatedAt        time.Time      `gorm:"column:created_at"`
		UpdatedAt        time.Time      `gorm:"column:updated_at"`
		DeletedAt        gorm.DeletedAt `gorm:"column:deleted_at;index"`
		Country          string         `gorm:"column:country"`           // 全部：all，部分用逗号分割，如：182,36
		Crowd            int64          `gorm:"column:crowd"`             // 人群id
		ImgDark          string         `gorm:"column:img_dark"`          // 深色模式入口图
		MasterOneLine    sql.NullString `gorm:"column:master_one_line"`   // 配图标题（第一行）
		MasterTwoLine    sql.NullString `gorm:"column:master_two_line"`   // 配图标题（第二行）
		SlaveOneLine     sql.NullString `gorm:"column:slave_one_line"`    // 配图副标题（第一行）
		SlaveTwoLine     sql.NullString `gorm:"column:slave_two_line"`    // 配图副标题（第二行）
		CompetitionTitle sql.NullString `gorm:"column:competition_title"` // 赛事标题
		IsEnLast         int64          `gorm:"column:is_en_last"`        // 是否英文兜底
		IsHot            int64          `gorm:"column:is_hot"`            // 是否是热门活动，1-是，0-否
		ShowInApp        int64          `gorm:"column:show_in_app"`       // 是否在app端展示，1:是，0:否
	}
)

var QCcCompetitions CcCompetitions

func init() {
	gormcsql.InitField(&QCcCompetitions)
}

func (CcCompetitions) TableName() string {
	return "`cc_competitions`"
}

func newCcCompetitionsModel(conn *gorm.DB) *defaultCcCompetitionsModel {
	return &defaultCcCompetitionsModel{
		conn:  conn,
		table: "`cc_competitions`",
	}
}

func (m *defaultCcCompetitionsModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&CcCompetitions{}, id).Error

	return err
}

func (m *defaultCcCompetitionsModel) FindOne(ctx context.Context, id int64) (*CcCompetitions, error) {
	var resp CcCompetitions
	err := m.conn.WithContext(ctx).Model(&CcCompetitions{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultCcCompetitionsModel) Insert(ctx context.Context, data *CcCompetitions) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultCcCompetitionsModel) Update(ctx context.Context, data *CcCompetitions) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

package vip_airdrop

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

type TaskConfig []TaskConfigItem

type TaskConfigItem struct {
	TaskId    int64  `json:"task_id"`
	AliasName string `json:"alias_name"`
}

func (c CcVipAirdropConfig) CheckInBookingTime() bool {
	now := time.Now().Unix()
	if now >= c.BookingStartTime && now < c.BookingEndTime {
		return true
	}
	return false
}

func (c CcVipAirdropConfig) CheckInCompetitionTime() bool {
	now := time.Now().Unix()
	if now >= c.CompetitionStartTime && now < c.CompetitionEndTime {
		return true
	}
	return false
}

func (c CcVipAirdropConfig) CompetitionEnd() bool {
	return time.Now().Unix() > c.CompetitionEndTime
}

func (c CcVipAirdropConfig) GetTaskConfig() (TaskConfig, error) {
	resp := make(TaskConfig, 0)
	err := json.Unmarshal([]byte(c.TaskConfig.String), &resp)
	if err != nil {
		return resp, err
	}
	return resp, nil
}

func (c TaskConfig) GetTaskId() []int64 {
	resp := make([]int64, 0, len(c))
	for _, v := range c {
		resp = append(resp, v.TaskId)
	}
	return resp
}

func (c TaskConfig) GetTaskIdStr() string {
	resp := make([]string, 0, len(c))
	for _, v := range c {
		resp = append(resp, fmt.Sprintf("%d", v.TaskId))
	}
	return strings.Join(resp, ",")
}

func (c TaskConfig) TaskIdMap() map[int64]TaskConfigItem {
	resp := make(map[int64]TaskConfigItem)
	for _, v := range c {
		resp[v.TaskId] = v
	}
	return resp
}

func (c TaskConfig) GetDailyChallengeTaskId() int64 {
	for _, v := range c {
		if v.AliasName == "daily_challenge" {
			return v.TaskId
		}
	}
	return 0
}

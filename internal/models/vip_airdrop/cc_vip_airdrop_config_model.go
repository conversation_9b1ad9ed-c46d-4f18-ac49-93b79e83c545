package vip_airdrop

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                         = gormcsql.InitField
	_ CcVipAirdropConfigModel = (*customCcVipAirdropConfigModel)(nil)
)

type (
	// CcVipAirdropConfigModel is an interface to be customized, add more methods here,
	// and implement the added methods in customCcVipAirdropConfigModel.
	CcVipAirdropConfigModel interface {
		ccVipAirdropConfigModel
		customCcVipAirdropConfigLogicModel
	}

	customCcVipAirdropConfigLogicModel interface {
		WithSession(tx *gorm.DB) CcVipAirdropConfigModel
	}

	customCcVipAirdropConfigModel struct {
		*defaultCcVipAirdropConfigModel
	}
)

func (c customCcVipAirdropConfigModel) WithSession(tx *gorm.DB) CcVipAirdropConfigModel {
	newModel := *c.defaultCcVipAirdropConfigModel
	c.defaultCcVipAirdropConfigModel = &newModel
	c.conn = tx
	return c
}

// NewCcVipAirdropConfigModel returns a model for the database table.
func NewCcVipAirdropConfigModel(conn *gorm.DB) CcVipAirdropConfigModel {
	return &customCcVipAirdropConfigModel{
		defaultCcVipAirdropConfigModel: newCcVipAirdropConfigModel(conn),
	}
}

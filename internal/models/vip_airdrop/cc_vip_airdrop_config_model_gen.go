// Code generated by goctl. DO NOT EDIT.

package vip_airdrop

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	ccVipAirdropConfigModel interface {
		Insert(ctx context.Context, data *CcVipAirdropConfig) error

		FindOne(ctx context.Context, id int64) (*CcVipAirdropConfig, error)
		FindOneBySeason(ctx context.Context, season string) (*CcVipAirdropConfig, error)
		Update(ctx context.Context, data *CcVipAirdropConfig) error

		Delete(ctx context.Context, id int64) error
	}

	defaultCcVipAirdropConfigModel struct {
		conn  *gorm.DB
		table string
	}

	CcVipAirdropConfig struct {
		Id                   int64           `gorm:"column:id"`
		Season               string          `gorm:"column:season"`                 // 赛季 s1,s2
		BookingStartTime     int64           `gorm:"column:booking_start_time"`     // 报名开始时间
		BookingEndTime       int64           `gorm:"column:booking_end_time"`       // 报名结束时间
		CompetitionStartTime int64           `gorm:"column:competition_start_time"` // 比赛开始时间
		CompetitionEndTime   int64           `gorm:"column:competition_end_time"`   // 比赛结束时间
		AirdropCoin          sql.NullString  `gorm:"column:airdrop_coin"`           // 空投代币名称
		AirdropCoinUrl       sql.NullString  `gorm:"column:airdrop_coin_url"`       // 空投代币图标
		AirdropCoinNum       sql.NullString  `gorm:"column:airdrop_coin_num"`       // 空投代币数量
		CrowdId              int64           `gorm:"column:crowd_id"`               // 人群ID
		AdjustId             int64           `gorm:"column:adjust_id"`              // 活动归因上报ID
		TaskConfig           sql.NullString  `gorm:"column:task_config"`            // 任务配置
		CreatedAt            time.Time       `gorm:"column:created_at"`
		UpdatedAt            time.Time       `gorm:"column:updated_at"`
		TotalBotTradeAmount  decimal.Decimal `gorm:"column:total_bot_trade_amount"` // 机器人总交易额
	}
)

var QCcVipAirdropConfig CcVipAirdropConfig

func init() {
	gormcsql.InitField(&QCcVipAirdropConfig)
}

func (CcVipAirdropConfig) TableName() string {
	return "`cc_vip_airdrop_config`"
}

func newCcVipAirdropConfigModel(conn *gorm.DB) *defaultCcVipAirdropConfigModel {
	return &defaultCcVipAirdropConfigModel{
		conn:  conn,
		table: "`cc_vip_airdrop_config`",
	}
}

func (m *defaultCcVipAirdropConfigModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&CcVipAirdropConfig{}, id).Error

	return err
}

func (m *defaultCcVipAirdropConfigModel) FindOne(ctx context.Context, id int64) (*CcVipAirdropConfig, error) {
	var resp CcVipAirdropConfig
	err := m.conn.WithContext(ctx).Model(&CcVipAirdropConfig{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultCcVipAirdropConfigModel) FindOneBySeason(ctx context.Context, season string) (*CcVipAirdropConfig, error) {
	var resp CcVipAirdropConfig
	err := m.conn.WithContext(ctx).Model(&CcVipAirdropConfig{}).Where("`season` = ?", season).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err
}

func (m *defaultCcVipAirdropConfigModel) Insert(ctx context.Context, data *CcVipAirdropConfig) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultCcVipAirdropConfigModel) Update(ctx context.Context, data *CcVipAirdropConfig) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

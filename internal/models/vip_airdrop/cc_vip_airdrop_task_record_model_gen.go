// Code generated by goctl. DO NOT EDIT.

package vip_airdrop

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	ccVipAirdropTaskRecordModel interface {
		Insert(ctx context.Context, data *CcVipAirdropTaskRecord) error

		FindOne(ctx context.Context, id int64) (*CcVipAirdropTaskRecord, error)
		FindOneByOnlyId(ctx context.Context, onlyId string) (*CcVipAirdropTaskRecord, error)
		Update(ctx context.Context, data *CcVipAirdropTaskRecord) error

		Delete(ctx context.Context, id int64) error
	}

	defaultCcVipAirdropTaskRecordModel struct {
		conn  *gorm.DB
		table string
	}

	CcVipAirdropTaskRecord struct {
		Id            int64  `gorm:"column:id"`
		Season        string `gorm:"column:season"`          // 赛季 202504
		Uid           int64  `gorm:"column:uid"`             // 用户ID
		BusinessId    int64  `gorm:"column:business_id"`     // 报名表id
		TaskId        int64  `gorm:"column:task_id"`         // 任务id，自己实现任务无id
		OnlyId        string `gorm:"column:only_id"`         // 任务记录唯一标识
		Status        int64  `gorm:"column:status"`          //  1 未开始 2 进行中 3 已完成 4 已失效 5 奖励已发放 6 奖励发放中
		TaskAliasName string `gorm:"column:task_alias_name"` // 任务标识
		RewardNum     int64  `gorm:"column:reward_num"`      // 任务奖励数量
		CompletedTime int64  `gorm:"column:completed_time"`  // 完成任务时间
	}
)

var QCcVipAirdropTaskRecord CcVipAirdropTaskRecord

func init() {
	gormcsql.InitField(&QCcVipAirdropTaskRecord)
}

func (CcVipAirdropTaskRecord) TableName() string {
	return "`cc_vip_airdrop_task_record`"
}

func newCcVipAirdropTaskRecordModel(conn *gorm.DB) *defaultCcVipAirdropTaskRecordModel {
	return &defaultCcVipAirdropTaskRecordModel{
		conn:  conn,
		table: "`cc_vip_airdrop_task_record`",
	}
}

func (m *defaultCcVipAirdropTaskRecordModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&CcVipAirdropTaskRecord{}, id).Error

	return err
}

func (m *defaultCcVipAirdropTaskRecordModel) FindOne(ctx context.Context, id int64) (*CcVipAirdropTaskRecord, error) {
	var resp CcVipAirdropTaskRecord
	err := m.conn.WithContext(ctx).Model(&CcVipAirdropTaskRecord{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultCcVipAirdropTaskRecordModel) FindOneByOnlyId(ctx context.Context, onlyId string) (*CcVipAirdropTaskRecord, error) {
	var resp CcVipAirdropTaskRecord
	err := m.conn.WithContext(ctx).Model(&CcVipAirdropTaskRecord{}).Where("`only_id` = ?", onlyId).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err
}

func (m *defaultCcVipAirdropTaskRecordModel) Insert(ctx context.Context, data *CcVipAirdropTaskRecord) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultCcVipAirdropTaskRecordModel) Update(ctx context.Context, data *CcVipAirdropTaskRecord) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

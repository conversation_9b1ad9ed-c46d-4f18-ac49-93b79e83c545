// Code generated by goctl. DO NOT EDIT.

package vip_airdrop

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	ccVipAirdropUserModel interface {
		Insert(ctx context.Context, data *CcVipAirdropUser) error

		FindOne(ctx context.Context, id int64) (*CcVipAirdropUser, error)
		FindOneByUidSeason(ctx context.Context, uid int64, season string) (*CcVipAirdropUser, error)
		Update(ctx context.Context, data *CcVipAirdropUser) error

		Delete(ctx context.Context, id int64) error
	}

	defaultCcVipAirdropUserModel struct {
		conn  *gorm.DB
		table string
	}

	CcVipAirdropUser struct {
		Id         int64         `gorm:"column:id"`
		Season     string        `gorm:"column:season"`      // 赛季 202504
		Uid        int64         `gorm:"column:uid"`         // 用户ID
		VipTier    sql.NullInt64 `gorm:"column:vip_tier"`    // vip等级
		Nick       string        `gorm:"column:nick"`        // 用户昵称
		Avatar     string        `gorm:"column:avatar"`      // 头像
		Country    string        `gorm:"column:country"`     // 国家ID
		Verified   int64         `gorm:"column:verified"`    // 实名kyc
		RegisterAt int64         `gorm:"column:register_at"` // 注册时间
		Channel    string        `gorm:"column:channel"`     // 渠道来源
		ApplyAt    time.Time     `gorm:"column:apply_at"`    // 报名时间
		RefUid     int64         `gorm:"column:ref_uid"`     // 邀请人ID
		CreatedAt  time.Time     `gorm:"column:created_at"`
		UpdatedAt  time.Time     `gorm:"column:updated_at"`
	}
)

var QCcVipAirdropUser CcVipAirdropUser

func init() {
	gormcsql.InitField(&QCcVipAirdropUser)
}

func (CcVipAirdropUser) TableName() string {
	return "`cc_vip_airdrop_user`"
}

func newCcVipAirdropUserModel(conn *gorm.DB) *defaultCcVipAirdropUserModel {
	return &defaultCcVipAirdropUserModel{
		conn:  conn,
		table: "`cc_vip_airdrop_user`",
	}
}

func (m *defaultCcVipAirdropUserModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&CcVipAirdropUser{}, id).Error

	return err
}

func (m *defaultCcVipAirdropUserModel) FindOne(ctx context.Context, id int64) (*CcVipAirdropUser, error) {
	var resp CcVipAirdropUser
	err := m.conn.WithContext(ctx).Model(&CcVipAirdropUser{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultCcVipAirdropUserModel) FindOneByUidSeason(ctx context.Context, uid int64, season string) (*CcVipAirdropUser, error) {
	var resp CcVipAirdropUser
	err := m.conn.WithContext(ctx).Model(&CcVipAirdropUser{}).Where("`uid` = ? and `season` = ?", uid, season).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err
}

func (m *defaultCcVipAirdropUserModel) Insert(ctx context.Context, data *CcVipAirdropUser) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultCcVipAirdropUserModel) Update(ctx context.Context, data *CcVipAirdropUser) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

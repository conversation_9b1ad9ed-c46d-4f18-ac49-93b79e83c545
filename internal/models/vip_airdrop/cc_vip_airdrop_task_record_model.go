package vip_airdrop

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// avoid unused err.
var (
	_                             = gormcsql.InitField
	_ CcVipAirdropTaskRecordModel = (*customCcVipAirdropTaskRecordModel)(nil)
)

type (
	// CcVipAirdropTaskRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customCcVipAirdropTaskRecordModel.
	CcVipAirdropTaskRecordModel interface {
		ccVipAirdropTaskRecordModel
		customCcVipAirdropTaskRecordLogicModel
	}

	customCcVipAirdropTaskRecordLogicModel interface {
		WithSession(tx *gorm.DB) CcVipAirdropTaskRecordModel
		BatchUpDateInsert(ctx context.Context, in []CcVipAirdropTaskRecord) error
		FindLastRecordByTaskId(ctx context.Context, uid, taskId int64, season string) (*CcVipAirdropTaskRecord, error)
	}

	customCcVipAirdropTaskRecordModel struct {
		*defaultCcVipAirdropTaskRecordModel
	}
)

func (c customCcVipAirdropTaskRecordModel) FindLastRecordByTaskId(ctx context.Context, uid, taskId int64, season string) (*CcVipAirdropTaskRecord, error) {
	var resp CcVipAirdropTaskRecord
	err := c.conn.WithContext(ctx).Model(&CcVipAirdropTaskRecord{}).
		Where("uid = ? and season = ? and task_id = ?", uid, season, taskId).Take(&resp).Error
	return &resp, err
}

func (c customCcVipAirdropTaskRecordModel) BatchUpDateInsert(ctx context.Context, in []CcVipAirdropTaskRecord) error {
	db := c.conn.WithContext(ctx).Model(&CcVipAirdropTaskRecord{})
	err := db.Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "only_id"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"season",
			"uid",
			"business_id",
			"task_id",
			"only_id",
			"task_alias_name",
			"reward_num",
			"status",
			"completed_time",
		}),
	}).Create(&in).Error
	return err
}

func (c customCcVipAirdropTaskRecordModel) WithSession(tx *gorm.DB) CcVipAirdropTaskRecordModel {
	newModel := *c.defaultCcVipAirdropTaskRecordModel
	c.defaultCcVipAirdropTaskRecordModel = &newModel
	c.conn = tx
	return c
}

// NewCcVipAirdropTaskRecordModel returns a model for the database table.
func NewCcVipAirdropTaskRecordModel(conn *gorm.DB) CcVipAirdropTaskRecordModel {
	return &customCcVipAirdropTaskRecordModel{
		defaultCcVipAirdropTaskRecordModel: newCcVipAirdropTaskRecordModel(conn),
	}
}

package vip_airdrop

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                       = gormcsql.InitField
	_ CcVipAirdropUserModel = (*customCcVipAirdropUserModel)(nil)
)

type (
	// CcVipAirdropUserModel is an interface to be customized, add more methods here,
	// and implement the added methods in customCcVipAirdropUserModel.
	CcVipAirdropUserModel interface {
		ccVipAirdropUserModel
		customCcVipAirdropUserLogicModel
	}

	customCcVipAirdropUserLogicModel interface {
		WithSession(tx *gorm.DB) CcVipAirdropUserModel
		GetTotalBySeason(ctx context.Context, season string) (int64, error)
	}

	customCcVipAirdropUserModel struct {
		*defaultCcVipAirdropUserModel
	}
)

func (c customCcVipAirdropUserModel) WithSession(tx *gorm.DB) CcVipAirdropUserModel {
	newModel := *c.defaultCcVipAirdropUserModel
	c.defaultCcVipAirdropUserModel = &newModel
	c.conn = tx
	return c
}

func (c customCcVipAirdropUserModel) GetTotalBySeason(ctx context.Context, season string) (int64, error) {
	var resp int64
	err := c.conn.WithContext(ctx).Model(&CcVipAirdropUser{}).
		Where("season = ?", season).Count(&resp).Error
	return resp, err
}

// NewCcVipAirdropUserModel returns a model for the database table.
func NewCcVipAirdropUserModel(conn *gorm.DB) CcVipAirdropUserModel {
	return &customCcVipAirdropUserModel{
		defaultCcVipAirdropUserModel: newCcVipAirdropUserModel(conn),
	}
}

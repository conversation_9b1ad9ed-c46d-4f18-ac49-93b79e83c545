package activities

import (
	"context"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                           = gormcsql.InitField
	_ ActivityInviteRebateModel = (*customActivityInviteRebateModel)(nil)
)

type (
	// ActivityInviteRebateModel is an interface to be customized, add more methods here,
	// and implement the added methods in customActivityInviteRebateModel.
	ActivityInviteRebateModel interface {
		activityInviteRebateModel
		customActivityInviteRebateLogicModel
	}

	customActivityInviteRebateLogicModel interface {
		WithSession(tx *gorm.DB) ActivityInviteRebateModel
		GetListByRefUid(ctx context.Context, refUID int64, beginTime int64, endTime int64) ([]int64, error)
	}

	customActivityInviteRebateModel struct {
		*defaultActivityInviteRebateModel
	}
)

func (c customActivityInviteRebateModel) WithSession(tx *gorm.DB) ActivityInviteRebateModel {
	newModel := *c.defaultActivityInviteRebateModel
	c.defaultActivityInviteRebateModel = &newModel
	c.conn = tx
	return c
}

// NewActivityInviteRebateModel returns a model for the database table.
func NewActivityInviteRebateModel(conn *gorm.DB) ActivityInviteRebateModel {
	return &customActivityInviteRebateModel{
		defaultActivityInviteRebateModel: newActivityInviteRebateModel(conn),
	}
}

/**
 * 获取邀请的用户列表
 */
func (m *defaultActivityInviteRebateModel) GetListByRefUid(ctx context.Context, refUID int64, beginTime int64, endTime int64) ([]int64, error) {
	var results []*ActivityInviteRebate
	query := m.conn.WithContext(ctx).Model(&ActivityInviteRebate{})
	err := query.Where("ref_uid = ? AND reg_time BETWEEN ? AND ?", refUID, beginTime, endTime).Find(&results).Error
	if err != nil {
		return nil, err
	}
	uidList := make([]int64, 0)
	for _, result := range results {
		uidList = append(uidList, result.Uid)
	}
	return uidList, nil
}

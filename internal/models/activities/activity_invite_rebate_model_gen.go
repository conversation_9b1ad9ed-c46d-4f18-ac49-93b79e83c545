// Code generated by goctl. DO NOT EDIT.

package activities

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	activityInviteRebateModel interface {
		Insert(ctx context.Context, data *ActivityInviteRebate) error

		FindOne(ctx context.Context, id int64) (*ActivityInviteRebate, error)
		FindOneByUidRefUid(ctx context.Context, uid int64, refUid int64) (*ActivityInviteRebate, error)
		Update(ctx context.Context, data *ActivityInviteRebate) error

		Delete(ctx context.Context, id int64) error
	}

	defaultActivityInviteRebateModel struct {
		conn  *gorm.DB
		table string
	}

	ActivityInviteRebate struct {
		Id        int64          `gorm:"column:id"`
		Uid       int64          `gorm:"column:uid"`      // 新注册用户
		RefUid    int64          `gorm:"column:ref_uid"`  // 邀请人
		RegTime   int64          `gorm:"column:reg_time"` // 注册时间
		Valid     int64          `gorm:"column:valid"`    // 1：有效，0：无效（邀请关系不符合）
		Status    int64          `gorm:"column:status"`   // 任务上报状态，0：未上报，1：已上报，2：上报中断
		Text      sql.NullString `gorm:"column:text"`
		CreatedAt time.Time      `gorm:"column:created_at"`
		UpdatedAt time.Time      `gorm:"column:updated_at"`
	}
)

var QActivityInviteRebate ActivityInviteRebate

func init() {
	gormcsql.InitField(&QActivityInviteRebate)
}

func (ActivityInviteRebate) TableName() string {
	return "`activity_invite_rebate`"
}

func newActivityInviteRebateModel(conn *gorm.DB) *defaultActivityInviteRebateModel {
	return &defaultActivityInviteRebateModel{
		conn:  conn,
		table: "`activity_invite_rebate`",
	}
}

func (m *defaultActivityInviteRebateModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&ActivityInviteRebate{}, id).Error

	return err
}

func (m *defaultActivityInviteRebateModel) FindOne(ctx context.Context, id int64) (*ActivityInviteRebate, error) {
	var resp ActivityInviteRebate
	err := m.conn.WithContext(ctx).Model(&ActivityInviteRebate{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultActivityInviteRebateModel) FindOneByUidRefUid(ctx context.Context, uid int64, refUid int64) (*ActivityInviteRebate, error) {
	var resp ActivityInviteRebate
	err := m.conn.WithContext(ctx).Model(&ActivityInviteRebate{}).Where("`uid` = ? and `ref_uid` = ?", uid, refUid).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err
}

func (m *defaultActivityInviteRebateModel) Insert(ctx context.Context, data *ActivityInviteRebate) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultActivityInviteRebateModel) Update(ctx context.Context, data *ActivityInviteRebate) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

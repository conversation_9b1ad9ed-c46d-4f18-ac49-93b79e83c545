package nft_activity

import (
	. "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"database/sql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// avoid unused err
var _ = InitField
var _ NftLotteryStatisticsModel = (*customNftLotteryStatisticsModel)(nil)

type (
	// NftLotteryStatisticsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftLotteryStatisticsModel.
	NftLotteryStatisticsModel interface {
		nftLotteryStatisticsModel
		customNftLotteryStatisticsLogicModel
	}

	customNftLotteryStatisticsLogicModel interface {
		WithSession(tx *gorm.DB) NftLotteryStatisticsModel
		ClearByCondition(ctx context.Context, conditions []clause.Expression) error
		GetRowsByCondition(ctx context.Context, conditions []clause.Expression) (*gorm.DB, *sql.Rows, error)
	}

	customNftLotteryStatisticsModel struct {
		*defaultNftLotteryStatisticsModel
	}
)

func (c customNftLotteryStatisticsModel) WithSession(tx *gorm.DB) NftLotteryStatisticsModel {
	newModel := *c.defaultNftLotteryStatisticsModel
	c.defaultNftLotteryStatisticsModel = &newModel
	c.conn = tx
	return c
}

// NewNftLotteryStatisticsModel returns a model for the database table.
func NewNftLotteryStatisticsModel(conn *gorm.DB) NftLotteryStatisticsModel {
	return &customNftLotteryStatisticsModel{
		defaultNftLotteryStatisticsModel: newNftLotteryStatisticsModel(conn),
	}
}

func (m *defaultNftLotteryStatisticsModel) customCacheKeys(data *NftLotteryStatistics) []string {
	if data == nil {
		return []string{}
	}
	return []string{}
}

func (m *defaultNftLotteryStatisticsModel) ClearByCondition(ctx context.Context, conditions []clause.Expression) error {
	query := m.conn.WithContext(ctx).Model(&NftLotteryStatistics{})
	for _, expr := range conditions {
		query = query.Where(expr)
	}
	err := query.Delete(&NftLotteryStatistics{}).Error
	return err
}

func (m *defaultNftLotteryStatisticsModel) GetRowsByCondition(ctx context.Context, conditions []clause.Expression) (*gorm.DB, *sql.Rows, error) {
	db := m.conn
	query := db.WithContext(ctx).Model(&NftLotteryStatistics{})
	for _, expr := range conditions {
		query = query.Where(expr)
	}
	rows, err := query.Rows()
	return db, rows, err
}

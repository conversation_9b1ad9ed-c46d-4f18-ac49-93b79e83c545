// Code generated by goctl. DO NOT EDIT!

package nft_activity

import (
	"context"
	"database/sql"
	"time"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
	. "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err
var _ = time.Second

type (
	nftLotteryStatisticsModel interface {
		Insert(ctx context.Context, data *NftLotteryStatistics) error

		FindOne(ctx context.Context, id int64) (*NftLotteryStatistics, error)
		FindOneByUserIdCountDay(ctx context.Context, userId int64, countDay string) (*NftLotteryStatistics, error)
		Update(ctx context.Context, data *NftLotteryStatistics) error

		Delete(ctx context.Context, id int64) error
	}

	defaultNftLotteryStatisticsModel struct {
		conn  *gorm.DB
		table string
	}

	NftLotteryStatistics struct {
		Id          int64     `gorm:"column:id"`           // id
		UserId      int64     `gorm:"column:user_id"`      // user id
		LotteryType int64     `gorm:"column:lottery_type"` // 集齐抽奖类型，0:普通集齐，1集齐大奖普通，2集齐大奖银3集齐大奖金色
		CountDay    string    `gorm:"column:count_day"`    // 计算权重的日期ymd
		CountGroup  int64     `gorm:"column:count_group"`  // 集齐套数
		CreatedAt   time.Time `gorm:"column:created_at"`
	}
)

var QNftLotteryStatistics NftLotteryStatistics

func init() {
	InitField(&QNftLotteryStatistics)
}

func (NftLotteryStatistics) TableName() string {
	return "`nft_lottery_statistics`"
}

func newNftLotteryStatisticsModel(conn *gorm.DB) *defaultNftLotteryStatisticsModel {
	return &defaultNftLotteryStatisticsModel{
		conn:  conn,
		table: "`nft_lottery_statistics`",
	}
}

func (m *defaultNftLotteryStatisticsModel) Insert(ctx context.Context, data *NftLotteryStatistics) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftLotteryStatisticsModel) FindOne(ctx context.Context, id int64) (*NftLotteryStatistics, error) {
	var resp NftLotteryStatistics
	err := m.conn.WithContext(ctx).Model(&NftLotteryStatistics{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == gormc.ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftLotteryStatisticsModel) FindOneByUserIdCountDay(ctx context.Context, userId int64, countDay string) (*NftLotteryStatistics, error) {
	var resp NftLotteryStatistics
	err := m.conn.WithContext(ctx).Model(&NftLotteryStatistics{}).Where("`user_id` = ? and `count_day` = ?", userId, countDay).Take(&resp).Error
	if err == gormc.ErrNotFound {
		return nil, err
	}
	return &resp, err
}

func (m *defaultNftLotteryStatisticsModel) Update(ctx context.Context, data *NftLotteryStatistics) error {
	db := m.conn
	err := db.WithContext(ctx).Save(data).Error
	return err
}

func (m *defaultNftLotteryStatisticsModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftLotteryStatistics{}, id).Error

	return err
}

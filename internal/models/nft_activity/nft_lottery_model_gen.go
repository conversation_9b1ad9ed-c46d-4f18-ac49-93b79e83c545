// Code generated by goctl. DO NOT EDIT!

package nft_activity

import (
	"context"
	"database/sql"
	"time"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
	. "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err
var _ = time.Second

type (
	nftLotteryModel interface {
		Insert(ctx context.Context, data *NftLottery) error
		InsertBatch(ctx context.Context, data []*NftLottery, batchNum int) error

		FindOne(ctx context.Context, id int64) (*NftLottery, error)
		Update(ctx context.Context, data *NftLottery) error

		Delete(ctx context.Context, id int64) error
	}

	defaultNftLotteryModel struct {
		conn  *gorm.DB
		table string
	}

	NftLottery struct {
		Id            int64     `gorm:"column:id"`             // id
		ActivityId    int64     `gorm:"column:activity_id"`    // 活动ID
		LotteryId     string    `gorm:"column:lottery_id"`     // 抽奖UID或者地址
		EncryptedId   string    `gorm:"column:encrypted_id"`   // 加密id
		LotteryNumber int64     `gorm:"column:lottery_number"` // 抽奖号码
		PrizeId       int64     `gorm:"column:prize_id"`       // 奖品ID
		LotteryHash   string    `gorm:"column:lottery_hash"`   // 中奖hash
		CreatedAt     time.Time `gorm:"column:created_at"`     // 创建时间
	}
)

var QNftLottery NftLottery

func init() {
	InitField(&QNftLottery)
}

func (NftLottery) TableName() string {
	return "`nft_lottery`"
}

func newNftLotteryModel(conn *gorm.DB) *defaultNftLotteryModel {
	return &defaultNftLotteryModel{
		conn:  conn,
		table: "`nft_lottery`",
	}
}

func (m *defaultNftLotteryModel) Insert(ctx context.Context, data *NftLottery) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftLotteryModel) InsertBatch(ctx context.Context, data []*NftLottery, batchNum int) error {
	db := m.conn
	err := db.WithContext(ctx).CreateInBatches(data, batchNum).Error
	return err
}

func (m *defaultNftLotteryModel) FindOne(ctx context.Context, id int64) (*NftLottery, error) {
	var resp NftLottery
	err := m.conn.WithContext(ctx).Model(&NftLottery{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == gormc.ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftLotteryModel) Update(ctx context.Context, data *NftLottery) error {
	db := m.conn
	err := db.WithContext(ctx).Save(data).Error
	return err
}

func (m *defaultNftLotteryModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftLottery{}, id).Error

	return err
}

package nft_activity

import (
	. "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"database/sql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// avoid unused err
var _ = InitField
var _ NftLotteryModel = (*customNftLotteryModel)(nil)

type (
	// NftLotteryModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftLotteryModel.
	NftLotteryModel interface {
		nftLotteryModel
		customNftLotteryLogicModel
	}

	customNftLotteryLogicModel interface {
		WithSession(tx *gorm.DB) NftLotteryModel
		ListPrizes(ctx context.Context, activityID int64) ([]NftLottery, error)
		ListPrizesByLotteryID(ctx context.Context, activityID int64, lotteryID string) ([]NftLottery, error)
		Count(ctx context.Context, conditions []clause.Expression) (int64, error)
		GetRowsByCondition(ctx context.Context, conditions []clause.Expression) (*gorm.DB, *sql.Rows, error)
		ClearByCondition(ctx context.Context, conditions []clause.Expression) error
	}

	customNftLotteryModel struct {
		*defaultNftLotteryModel
	}
)

func (c customNftLotteryModel) WithSession(tx *gorm.DB) NftLotteryModel {
	newModel := *c.defaultNftLotteryModel
	c.defaultNftLotteryModel = &newModel
	c.conn = tx
	return c
}

func (m *defaultNftLotteryModel) ListPrizes(ctx context.Context, activityID int64) ([]NftLottery, error) {
	var resp []NftLottery
	err := m.conn.WithContext(ctx).Model(&NftLottery{}).Where("activity_id=? and prize_id!=0", activityID).Find(&resp).Error
	return resp, err
}

func (m *defaultNftLotteryModel) ListPrizesByLotteryID(ctx context.Context, activityID int64, lotteryID string) ([]NftLottery, error) {
	var resp []NftLottery
	err := m.conn.WithContext(ctx).Model(&NftLottery{}).Where("activity_id=? and lottery_id=?", activityID, lotteryID).Find(&resp).Error
	return resp, err
}

// NewNftLotteryModel returns a model for the database table.
func NewNftLotteryModel(conn *gorm.DB) NftLotteryModel {
	return &customNftLotteryModel{
		defaultNftLotteryModel: newNftLotteryModel(conn),
	}
}

func (m *defaultNftLotteryModel) customCacheKeys(data *NftLottery) []string {
	if data == nil {
		return []string{}
	}
	return []string{}
}

func (m *defaultNftLotteryModel) Count(ctx context.Context, conditions []clause.Expression) (int64, error) {
	var resp int64
	query := m.conn.WithContext(ctx).Model(&NftLottery{})
	for _, condition := range conditions {
		query = query.Where(condition)
	}
	err := query.Count(&resp).Error
	return resp, err
}

func (m *defaultNftLotteryModel) GetRowsByCondition(ctx context.Context, conditions []clause.Expression) (*gorm.DB, *sql.Rows, error) {
	db := m.conn
	query := db.WithContext(ctx).Model(&NftLottery{})
	for _, expr := range conditions {
		query = query.Where(expr)
	}
	rows, err := query.Rows()
	return db, rows, err
}

func (m *defaultNftLotteryModel) ClearByCondition(ctx context.Context, conditions []clause.Expression) error {
	query := m.conn.WithContext(ctx).Model(&NftLottery{})
	for _, expr := range conditions {
		query = query.Where(expr)
	}
	err := query.Delete(&NftLottery{}).Error
	return err
}

package nft_activity

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                  = gormcsql.InitField
	_ NftActivityModel = (*customNftActivityModel)(nil)
)

type (
	// NftActivityModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftActivityModel.
	NftActivityModel interface {
		nftActivityModel
		customNftActivityLogicModel
	}

	customNftActivityLogicModel interface {
		WithSession(tx *gorm.DB) NftActivityModel
	}

	customNftActivityModel struct {
		*defaultNftActivityModel
	}
)

func (c customNftActivityModel) WithSession(tx *gorm.DB) NftActivityModel {
	newModel := *c.defaultNftActivityModel
	c.defaultNftActivityModel = &newModel
	c.conn = tx
	return c
}

// NewNftActivityModel returns a model for the database table.
func NewNftActivityModel(conn *gorm.DB) NftActivityModel {
	return &customNftActivityModel{
		defaultNftActivityModel: newNftActivityModel(conn),
	}
}

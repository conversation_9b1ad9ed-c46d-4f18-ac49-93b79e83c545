// Code generated by goctl. DO NOT EDIT.

package share

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	shareConfigModel interface {
		Insert(ctx context.Context, data *ShareConfig) error

		FindOne(ctx context.Context, id int64) (*ShareConfig, error)
		FindOneBySource(ctx context.Context, source string) (*ShareConfig, error)
		Update(ctx context.Context, data *ShareConfig) error

		Delete(ctx context.Context, id int64) error
	}

	defaultShareConfigModel struct {
		conn  *gorm.DB
		table string
	}

	ShareConfig struct {
		Id             int64     `gorm:"column:id"`              // 活动id
		Title          string    `gorm:"column:title"`           // 标题
		Source         string    `gorm:"column:source"`          // 关联对应的场景
		BaseImage      string    `gorm:"column:base_image"`      // 底图，需要支持配置多个语言
		UnionLogo      string    `gorm:"column:union_logo"`      // 联合logo
		TipDesc        string    `gorm:"column:tip_desc"`        // 底图营销文案，仅作为后台文案展示
		TipKey         string    `gorm:"column:tip_key"`         // 海报底部营销文案翻译Key
		Status         int64     `gorm:"column:status"`          // 1:未生效 2：生效中
		EditInfo       string    `gorm:"column:edit_info"`       // 未生效的配置
		ApprovalStatus int64     `gorm:"column:approval_status"` // 审批状态，1:待审批 2：审批通过 3：审批驳回
		OpId           int64     `gorm:"column:op_id"`           // 操作人ID
		OpName         string    `gorm:"column:op_name"`         // 操作人姓名
		CreatedAt      time.Time `gorm:"column:created_at"`
		UpdatedAt      time.Time `gorm:"column:updated_at"`
	}
)

var QShareConfig ShareConfig

func init() {
	gormcsql.InitField(&QShareConfig)
}

func (ShareConfig) TableName() string {
	return "`share_config`"
}

func newShareConfigModel(conn *gorm.DB) *defaultShareConfigModel {
	return &defaultShareConfigModel{
		conn:  conn,
		table: "`share_config`",
	}
}

func (m *defaultShareConfigModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&ShareConfig{}, id).Error

	return err
}

func (m *defaultShareConfigModel) FindOne(ctx context.Context, id int64) (*ShareConfig, error) {
	var resp ShareConfig
	err := m.conn.WithContext(ctx).Model(&ShareConfig{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultShareConfigModel) FindOneBySource(ctx context.Context, source string) (*ShareConfig, error) {
	var resp ShareConfig
	err := m.conn.WithContext(ctx).Model(&ShareConfig{}).Where("`source` = ?", source).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err
}

func (m *defaultShareConfigModel) Insert(ctx context.Context, data *ShareConfig) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultShareConfigModel) Update(ctx context.Context, data *ShareConfig) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

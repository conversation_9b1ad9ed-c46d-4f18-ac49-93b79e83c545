package share

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"gateio_service_marketing_activity/internal/consts"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                  = gormcsql.InitField
	_ ShareConfigModel = (*customShareConfigModel)(nil)
)

type (
	// ShareConfigModel is an interface to be customized, add more methods here,
	// and implement the added methods in customShareConfigModel.
	ShareConfigModel interface {
		shareConfigModel
		customShareConfigLogicModel
	}

	customShareConfigLogicModel interface {
		WithSession(tx *gorm.DB) ShareConfigModel
		GetShareConfigBySource(ct context.Context, source string) (*ShareConfig, error)
	}

	customShareConfigModel struct {
		*defaultShareConfigModel
	}
)

func (c customShareConfigModel) WithSession(tx *gorm.DB) ShareConfigModel {
	newModel := *c.defaultShareConfigModel
	c.defaultShareConfigModel = &newModel
	c.conn = tx
	return c
}

func (m *defaultShareConfigModel) GetShareConfigBySource(ctx context.Context, source string) (*ShareConfig, error) {
	if len(source) == 0 {
		return nil, nil
	}
	var resp ShareConfig
	err := m.conn.WithContext(ctx).Model(&ShareConfig{}).Where("source=? and status = ?", source, consts.ShareStatusActive).Take(&resp).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &resp, err
}

// NewShareConfigModel returns a model for the database table.
func NewShareConfigModel(conn *gorm.DB) ShareConfigModel {
	return &customShareConfigModel{
		defaultShareConfigModel: newShareConfigModel(conn),
	}
}

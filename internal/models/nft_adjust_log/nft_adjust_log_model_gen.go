// Code generated by goctl. DO NOT EDIT.

package nft_adjust_log

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	nftAdjustLogModel interface {
		Insert(ctx context.Context, data *NftAdjustLog) error

		FindOne(ctx context.Context, id int64) (*NftAdjustLog, error)
		Update(ctx context.Context, data *NftAdjustLog) error

		Delete(ctx context.Context, id int64) error
	}

	defaultNftAdjustLogModel struct {
		conn  *gorm.DB
		table string
	}

	NftAdjustLog struct {
		Id           int64     `gorm:"column:id"`            // 数据ID
		Uid          int64     `gorm:"column:uid"`           // uid 中心化领取或购买的用户ID
		ActivityType int64     `gorm:"column:activity_type"` // 活动类型 1:红牛nft落地页
		CreatedAt    time.Time `gorm:"column:created_at"`    // 创建时间
		UpdatedAt    time.Time `gorm:"column:updated_at"`    // 更新时间
	}
)

var QNftAdjustLog NftAdjustLog

func init() {
	gormcsql.InitField(&QNftAdjustLog)
}

func (NftAdjustLog) TableName() string {
	return "`nft_adjust_log`"
}

func newNftAdjustLogModel(conn *gorm.DB) *defaultNftAdjustLogModel {
	return &defaultNftAdjustLogModel{
		conn:  conn,
		table: "`nft_adjust_log`",
	}
}

func (m *defaultNftAdjustLogModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&NftAdjustLog{}, id).Error

	return err
}

func (m *defaultNftAdjustLogModel) FindOne(ctx context.Context, id int64) (*NftAdjustLog, error) {
	var resp NftAdjustLog
	err := m.conn.WithContext(ctx).Model(&NftAdjustLog{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultNftAdjustLogModel) Insert(ctx context.Context, data *NftAdjustLog) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultNftAdjustLogModel) Update(ctx context.Context, data *NftAdjustLog) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}

package nft_adjust_log

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                   = gormcsql.InitField
	_ NftAdjustLogModel = (*customNftAdjustLogModel)(nil)
)

type (
	// NftAdjustLogModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNftAdjustLogModel.
	NftAdjustLogModel interface {
		nftAdjustLogModel
		customNftAdjustLogLogicModel
	}

	customNftAdjustLogLogicModel interface {
		WithSession(tx *gorm.DB) NftAdjustLogModel
	}

	customNftAdjustLogModel struct {
		*defaultNftAdjustLogModel
	}
)

func (c customNftAdjustLogModel) WithSession(tx *gorm.DB) NftAdjustLogModel {
	newModel := *c.defaultNftAdjustLogModel
	c.defaultNftAdjustLogModel = &newModel
	c.conn = tx
	return c
}

// NewNftAdjustLogModel returns a model for the database table.
func NewNftAdjustLogModel(conn *gorm.DB) NftAdjustLogModel {
	return &customNftAdjustLogModel{
		defaultNftAdjustLogModel: newNftAdjustLogModel(conn),
	}
}

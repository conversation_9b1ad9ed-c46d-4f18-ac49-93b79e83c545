package nft

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"fmt"
	"gateio_service_marketing_activity/internal/models/nft_mint_log"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"strconv"
	"strings"
	"time"
)

// FixStatusNoSync 修复状态未同步的数据
func FixStatusNoSync(ctx context.Context, svcCtx *svc.ServiceContext, taskReq *xxljob.TaskRequest) (msg string) {
	msg = ""
	// 查询出所有状态是初始化的nft
	logModel := nft_mint_log.NewNftMintLogModel(svcCtx.DBMarketingActivities)
	result, err := logModel.ListByFilter(ctx, 3, time.Now().Add(-time.Hour*2))
	if err != nil {
		msg = fmt.Sprintf("[FixStatusNoSync]ListByFilter err:%v", err)
		logc.Infof(ctx, msg)

		return
	}
	// 根据钱包区分不同的记录数据
	walletAddress2LogMap := make(map[string][]*nft_mint_log.NftMintLog)
	for _, item := range result {
		walletAddress2LogMap[item.WalletAddress] = append(walletAddress2LogMap[item.WalletAddress], item)
	}

	// 遍历单个钱包，并且每次执行完成，间隔50ms
	for walletAddress, mintLogs := range walletAddress2LogMap {
		nftInfos, err := service.GetRedBullNFTDetailList(svcCtx, walletAddress)
		if err != nil {
			msg = fmt.Sprintf("[FixStatusNoSync]GetRedBullNFTDetailList err:%v", err)
			logc.Infof(ctx, msg)
			continue
		}
		nftInfoMap := make(map[string]*service.UserNftInfo)
		for _, nftInfo := range nftInfos {
			splitPaths := strings.Split(nftInfo.TokenURL, "/")
			nftIDStr := splitPaths[len(splitPaths)-1]
			nftInfoMap[nftIDStr] = nftInfo
		}

		for _, mintLog := range mintLogs {
			if nftInfo, ok := nftInfoMap[strconv.FormatInt(mintLog.NftId, 10)]; ok {
				mintLog.MintStatus = 3
				mintLog.TokenId = nftInfo.TokenID
				mintLog.TokenUrl = nftInfo.TokenURL
			} else {
				mintLog.MintStatus = 4
				mintLog.MintStatusCause = "超时未同步状态"
			}
		}
		time.Sleep(time.Millisecond * 20)

	}

	// 更新单个钱包的数据
	for _, item := range result {
		logc.Infof(ctx, "[FixStatusNoSync]GetRedBullNFTDetailList result:%v\n", item)

		err = logModel.Update(ctx, item)
		if err != nil {
			msg = fmt.Sprintf("[FixStatusNoSync]Update err:%v", err)
			logc.Infof(ctx, msg)
			continue
		}
		time.Sleep(time.Millisecond * 10)
	}

	return
}

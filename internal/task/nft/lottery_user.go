package nft

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/dao"
	nftModel "gateio_service_marketing_activity/internal/models/db_nft"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/pkg/nft"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"strconv"
	"time"
)

// HandleLotteryUserByRound 根据轮次统计抽奖用户
func HandleLotteryUserByRound(ctx context.Context, svcCtx *svc.ServiceContext, taskReq *xxljob.TaskRequest) (msg string) {
	msg = ""
	// nftIds；startTime；endTime
	if taskReq.ExecutorParams == "" {
		msg = fmt.Sprintf("[HandleLotteryUserByRound] Please input params.")
		logc.Errorf(ctx, msg)
		return
	}
	var inputParams service.LotteryParams
	err := json.Unmarshal([]byte(taskReq.ExecutorParams), &inputParams)
	if err != nil {
		msg = fmt.Sprintf("[HandleLotteryUserByRound] Resolve input json params error.")
		logc.Errorf(ctx, msg)
		return
	}

	//startTime, _ := time.Parse("2006-01-02 15:04:05", inputParams.Stime)
	startTime := inputParams.Stime
	var endTime string
	if inputParams.Etime == "" {
		endTime = time.Now().Format("2006-01-02 15:04:05")
	} else {
		endTime = inputParams.Etime
		if err != nil {
			endTime = time.Now().Format("2006-01-02 15:04:05")
		}
	}
	msg = fmt.Sprintf("[HandleLotteryUserByRound] Time params: %v, start time params:%v, end time params:%v", taskReq.ExecutorParams, startTime, endTime)
	logc.Infof(ctx, msg)

	//tmpParamsArr := strings.Split(taskReq.ExecutorParams, ";")
	//if len(tmpParamsArr) == 0 {
	//	msg = fmt.Sprintf("Error start time and nft ids. %v", taskReq.ExecutorParams)
	//	logc.Errorf(ctx, msg)
	//	return
	//}

	// 解析NFT列表
	//nftParams := strings.Split(tmpParamsArr[0], ",")
	//var nftIds []int64
	//for _, numStr := range nftParams {
	//	n, err := strconv.ParseInt(numStr, 10, 64)
	//	if err != nil {
	//		msg = fmt.Sprintf("Error at parse nft ids . %v, error: %v", taskReq.ExecutorParams, err)
	//		logc.Errorf(ctx, msg)
	//		return
	//	}
	//	nftIds = append(nftIds, n)
	//}
	if len(inputParams.NftIds) == 0 {
		msg = fmt.Sprintf("[HandleLotteryUserByRound] Null config nft ids . %v", taskReq.ExecutorParams)
		logc.Errorf(ctx, msg)
		return
	}
	nftIds := inputParams.NftIds

	var nftGroupConfigList = make(map[int64]int64)
	nftList, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetListByNftIds(ctx, nftIds)
	if err != nil || len(nftList) == 0 {
		msg = fmt.Sprintf("[HandleLotteryUserByRound] Get config nft ids detail error . %v, error: %v", nftIds, err)
		logc.Errorf(ctx, msg)
		return
	}
	for _, tmpConfNft := range nftList {
		nftGroupConfigList[tmpConfNft.NftId] = tmpConfNft.ParentNftId
	}

	msg = fmt.Sprintf("[HandleLotteryUserByRound] Nft group config lists. %v", nftGroupConfigList)
	logc.Infof(ctx, msg)

	// 根据时间遍历用户goods表，然后判断是否集齐
	collectionId := svcCtx.Config.NftMintConf.CollectionId
	token := svcCtx.Config.NftMintConf.Token
	userLimit := 100
	lastUid := int64(0)

	currDay := time.Now().Format("2006-01-02")
	for {
		userListQueryConditions := []clause.Expression{
			gorm.Expr("collection_id = ?", collectionId),
			gorm.Expr("token = ?", token),
			gorm.Expr("own_uid > ?", lastUid),
			gorm.Expr("create_timest > ?", startTime),
			gorm.Expr("create_timest < ?", endTime),
		}

		msg = fmt.Sprintf("[HandleLotteryUserByRound] The lottery user start, from uid: %v", lastUid)
		logc.Infof(ctx, msg)
		nftAuctionGoodsModel := nftModel.NewNftAuctionGoodsModel(svcCtx.DBNft)
		lotteryUserLists, err := nftAuctionGoodsModel.GetUserListsGroupByOwn(ctx, userListQueryConditions, userLimit)
		if err != nil {
			msg = fmt.Sprintf("[HandleLotteryUserByRound] The lottery user list error:  %s", err)
			logc.Errorf(ctx, msg)
			break
		}
		if len(lotteryUserLists) == 0 {
			msg = fmt.Sprintf("[HandleLotteryUserByRound] The lottery user list is null: %v", lotteryUserLists)
			logc.Warnf(ctx, msg)
			break
		}

		nftService := nft.NewNft(ctx, svcCtx)
		for _, goodItem := range lotteryUserLists {
			lastUid = goodItem.OwnUid
			if lastUid == 20311312 { //过滤系统帐号
				continue
			}
			userQueryConditions := []clause.Expression{
				gorm.Expr("collection_id = ?", collectionId),
				gorm.Expr("token = ?", token),
				gorm.Expr("own_uid = ?", lastUid),
				gorm.Expr("create_timest >= ?", startTime),
				gorm.Expr("create_timest < ?", endTime),
			}
			userGoodsLists, userErr := nftService.QueryUserGoodsForActivity(userQueryConditions)
			if userErr != nil || len(userGoodsLists) == 0 {
				msg = fmt.Sprintf("[HandleLotteryUserByRound] The lottery user good list is null: %v , error: %s", userGoodsLists, userErr)
				logc.Infof(ctx, msg)
				continue
			}
			userNftMap := service.UserRoundLotteryNFT{
				NftGroupMap: make(map[int64]int64),
			}
			for _, tmpGood := range userGoodsLists {
				tmpUserNftId, _ := strconv.ParseInt(tmpGood.NftId, 10, 64)
				groupId, ok := nftGroupConfigList[tmpUserNftId]
				if !ok {
					continue
				}
				userNftMap.NftGroupMap[groupId] += 1
			}
			// 如果6个作品都有【至少集齐了1套】
			if len(userNftMap.NftGroupMap) >= 6 { // 普通每站的抽奖：6个作品，每个都有，不区分版本
				minGroup := int64(1)
				for _, v := range userNftMap.NftGroupMap {
					if minGroup == 1 || v < minGroup {
						minGroup = v
					}
				}
				lotteryModel := nft_activity.NewNftLotteryStatisticsModel(svcCtx.DBMarketingActivities)
				err := lotteryModel.Insert(ctx, &nft_activity.NftLotteryStatistics{
					UserId:      lastUid,
					LotteryType: 0,
					CountDay:    currDay,
					CountGroup:  minGroup,
				})
				if err != nil {
					msg = fmt.Sprintf("[HandleLotteryUserByRound] User count error: %v, uid:%v , day: %v, groups: %v", err, lastUid, currDay, minGroup)
					logc.Errorf(ctx, msg)
				} else {
					msg = fmt.Sprintf("[HandleLotteryUserByRound] Insert lottery user, uid:%v , day: %v, groups: %v", lastUid, currDay, minGroup)
					logc.Infof(ctx, msg)
				}
			}
		}
	}
	msg = "[HandleLotteryUserByRound] Finish count lottery round users"
	return
}

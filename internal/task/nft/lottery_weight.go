package nft

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"fmt"
	"gateio_service_marketing_activity/internal/dao"
	nftModel "gateio_service_marketing_activity/internal/models/db_nft"
	"gateio_service_marketing_activity/internal/pkg/nft"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"strconv"
	"time"
)

func HandleUserWeight(ctx context.Context, _ *xxljob.TaskRequest, svcCtx *svc.ServiceContext) (msg string) {
	now := time.Now()
	lotteryStartTime := now
	msg = ""

	// 1: 当前可以抽奖的活动
	activityList, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetLotteryActivity(ctx)
	if err != nil || len(activityList) == 0 {
		msg = fmt.Sprintf("The current activity info is null: %v , error: %s", activityList, err)
		logc.Errorf(ctx, msg)
		return
	}
	// 2: 活动配置的nft 列表
	var nftConfigList = make(map[int64]map[int64]bool)

	for _, tmpActivity := range activityList {
		tmpConfigNftList, nftConfigErr := nft.GetNftList(svcCtx, ctx, tmpActivity.ActivityId)
		if nftConfigErr != nil || len(tmpConfigNftList) == 0 {
			continue
		}
		if tmpActivity.StartTime.Before(lotteryStartTime) {
			lotteryStartTime = tmpActivity.StartTime
		}
		for _, tmpNftDetail := range tmpConfigNftList {
			// nft类型 1普通nft 2白银nft 3黄金nft 4特殊活动对应的NFT
			if tmpNftDetail.NftType == 4 {
				continue
			}
			if _, exists := nftConfigList[tmpNftDetail.NftType]; !exists {
				nftConfigList[tmpNftDetail.NftType] = make(map[int64]bool)
			}
			nftConfigList[tmpNftDetail.NftType][tmpNftDetail.NftId] = true
		}
	}
	if len(nftConfigList) == 0 {
		msg = fmt.Sprintf("The activity config nft info is null, activity: %v,  nftconfig:%v, error: %s", activityList, nftConfigList, err)
		logc.Errorf(ctx, msg)
		return
	}

	// 集齐一套需要的数量
	normalNum := len(nftConfigList[1]) // 普通 1 4 7 10 13 16
	silverNum := len(nftConfigList[2]) // 银色 2 5 8 11 14 17
	goldNum := len(nftConfigList[3])   // 金色 3 6 9 12 15 18

	// 3: 查询用户列表
	collectionId := svcCtx.Config.NftMintConf.CollectionId
	token := svcCtx.Config.NftMintConf.Token
	userLimit := 100
	lastUid := int64(0)

	for {
		userListQueryConditions := []clause.Expression{
			gorm.Expr("collection_id = ?", collectionId),
			gorm.Expr("token = ?", token),
			gorm.Expr("own_uid > ?", lastUid),
			gorm.Expr("create_timest > ?", lotteryStartTime),
			gorm.Expr("create_timest < ?", now),
		}

		nftAuctionGoodsModel := nftModel.NewNftAuctionGoodsModel(svcCtx.DBNft)
		lotteryUserLists, err := nftAuctionGoodsModel.GetUserListsGroupByOwn(ctx, userListQueryConditions, userLimit)
		if err != nil || len(lotteryUserLists) == 0 {
			msg = fmt.Sprintf("The current activity lottery user list is null: %v, activity:%v , error: %s", lotteryUserLists, activityList, err)
			logc.Infof(ctx, msg)
			break
		}
		// 4: 查询每个用户的nft集齐情况，判断是否集齐
		nftService := nft.NewNft(ctx, svcCtx)
		for _, goodItem := range lotteryUserLists {
			lastUid = goodItem.OwnUid
			userQueryConditions := []clause.Expression{
				gorm.Expr("collection_id = ?", collectionId),
				gorm.Expr("token = ?", token),
				gorm.Expr("own_uid = ?", lastUid),
				gorm.Expr("create_timest > ?", lotteryStartTime),
				gorm.Expr("create_timest < ?", now),
			}
			userGoodsLists, userErr := nftService.QueryUserGoodsForActivity(userQueryConditions)
			if userErr != nil || len(userGoodsLists) == 0 {
				continue
			}
			userNftMap := service.UserNFT{
				NormalCount:    0,
				NormalCountMap: make(map[int64]bool),
				HasFullNormal:  false,
				SilverCount:    0,
				SilverCountMap: make(map[int64]bool),
				HasFullSilver:  false,
				GoldCount:      0,
				GoldCountMap:   make(map[int64]bool),
				HasFullGold:    false,
			}

			for _, tmpGood := range userGoodsLists {
				tmpUserNftId, _ := strconv.ParseInt(tmpGood.NftId, 10, 64)
				for tmpNftType := int64(1); tmpNftType <= 3; tmpNftType++ {
					if _, exists := nftConfigList[tmpNftType][tmpUserNftId]; exists { //用户获得的nftId在本期抽奖活动配置【普通，金，银】
						switch tmpNftType {
						case 1:
							userNftMap.NormalCount++
							userNftMap.NormalCountMap[tmpUserNftId] = true
							if len(userNftMap.NormalCountMap) >= normalNum {
								userNftMap.HasFullNormal = true
							}
						case 2:
							userNftMap.SilverCount++
							userNftMap.SilverCountMap[tmpUserNftId] = true
							if len(userNftMap.NormalCountMap) >= silverNum {
								userNftMap.HasFullSilver = true
							}
						case 3:
							userNftMap.GoldCount++
							userNftMap.GoldCountMap[tmpUserNftId] = true
							if len(userNftMap.NormalCountMap) >= goldNum {
								userNftMap.HasFullGold = true
							}
						}
					}
				}
				userNftMap.NftIds = append(userNftMap.NftIds, tmpUserNftId)
			}
			// 5: 计算权重
			// userScore := calculateScore(userNftMap, normalNum, silverNum, goldNum)
			// 6: save to db
			// fmt.Printf("user:%v score:%v \n", lastUid, userScore)
			//weightModel := nft_activity.NewNftLotteryWeightModel(svcCtx.DBMarketingActivities)
			//nftIdsStr := ""
			//if len(userNftMap.NftIds) > 0 {
			//	nftStrArr := make([]string, len(userNftMap.NftIds))
			//	for i, v := range userNftMap.NftIds {
			//		nftStrArr[i] = strconv.FormatInt(v, 10)
			//	}
			//	nftIdsStr = strings.Join(nftStrArr, ",")
			//}
			//
			//err := weightModel.Insert(ctx, &nft_activity.NftLotteryWeight{
			//	UserId:    lastUid,
			//	NftIds:    nftIdsStr,
			//	WeightDay: now,
			//})
			//if err != nil {
			//	msg = fmt.Sprintf("User weight error: %v, uid:%v , day: %v, ids:%v", err, lastUid, now, nftIdsStr)
			//	logc.Errorf(ctx, msg)
			//}
		}
	}

	return "success"
}

// 计算分值 , 最新需求不在计算分值
func calculateScore(nft service.UserNFT, normalNeedNum int, silverNeedNum int, goldNeedNum int) int {
	score := 0

	// 普通 NFT：6张 + 一套加成：x1.5
	if nft.HasFullNormal {
		score += normalNeedNum * 1 * 3 / 2 // 1.5 倍
		score += (nft.NormalCount - normalNeedNum) * 1
	} else {
		score += nft.NormalCount * 1
	}

	// 银色 NFT：单张 x3，一套 x2.5 加成
	if nft.HasFullSilver {
		score += silverNeedNum * 3 * 5 / 2 // 2.5 倍
		score += (nft.SilverCount - silverNeedNum) * 3
	} else {
		score += nft.SilverCount * 3
	}

	// 金色 NFT：单张 x8，一套 x4 加成
	if nft.HasFullGold {
		score += goldNeedNum * 8 * 4
		score += (nft.GoldCount - goldNeedNum) * 8
	} else {
		score += nft.GoldCount * 8
	}

	return score
}

package nft

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/pkg/encrypted"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"strconv"
)

func HandleCexLotteryUser(ctx context.Context, svcCtx *svc.ServiceContext, taskReq *xxljob.TaskRequest) (msg string) {
	msg = "[HandleCexLotteryUser] start."
	// activityId
	if taskReq.ExecutorParams == "" {
		msg = fmt.Sprintf("[HandleCexLotteryUser] Please input activity id params.")
		logc.Errorf(ctx, msg)
		return
	}

	var inputParams service.DealCexUserParams
	err := json.Unmarshal([]byte(taskReq.ExecutorParams), &inputParams)
	if err != nil {
		msg = fmt.Sprintf("[HandleCexLotteryUser] Resolve input json params error:%v", err)
		logc.Errorf(ctx, msg)
		return
	}

	msg = fmt.Sprintf("[HandleCexLotteryUser] Input params: %v", taskReq.ExecutorParams)
	logc.Infof(ctx, msg)

	// 读集齐用户数据
	lotteryModel := nft_activity.NewNftLotteryStatisticsModel(svcCtx.DBMarketingActivities)
	usersQueryConditions := []clause.Expression{
		gorm.Expr("count_day = ?", inputParams.CountDay),
		gorm.Expr("lottery_type = ?", inputParams.LotteryType),
	}
	db, userRows, err := lotteryModel.GetRowsByCondition(ctx, usersQueryConditions)
	if err != nil {
		msg = fmt.Sprintf("[HandleCexLotteryUser] Db query cex user list error:%v", err)
		logc.Errorf(ctx, msg)
		return
	}

	defer userRows.Close()

	nftModel := nft_activity.NewNftLotteryModel(svcCtx.DBMarketingActivities)
	for userRows.Next() {
		var tmpItem nft_activity.NftLotteryStatistics
		err := db.ScanRows(userRows, &tmpItem)
		if err != nil {
			msg = fmt.Sprintf("[HandleCexLotteryUser] Scan cex user rows error:%v", err)
			logc.Errorf(ctx, msg)
			continue
		}
		//logc.Infof(ctx, "tmp data: %v", tmpItem)
		for i := int64(0); i < tmpItem.CountGroup; i++ {
			userId := strconv.FormatInt(tmpItem.UserId, 10)
			encryptedUserId, err := encrypted.NewEncrypted(ctx).GetEncryptedId(userId, "af6804a6b1ae6f0d5f217a33a0995217")
			if err != nil {
				err = fmt.Errorf("[HandleCexLotteryUser] 用户ID加密异常, uid:%v, encryptedUserId:%v", userId, encryptedUserId)
				return
			}
			tmpData := &nft_activity.NftLottery{
				ActivityId:  inputParams.ActivityId,
				LotteryId:   userId,
				EncryptedId: encryptedUserId,
			}
			err = nftModel.Insert(ctx, tmpData)
			if err != nil {
				msg = fmt.Sprintf("[HandleCexLotteryUser] Insert lottery table error: %v, data:%v ", err, tmpData)
				logc.Errorf(ctx, msg)
			} else {
				msg = fmt.Sprintf("[HandleCexLotteryUser] Insert lottery table success, data:%v", tmpData)
				logc.Infof(ctx, msg)
			}
		}
	}
	msg = "[HandleCexLotteryUser] end"
	logc.Infof(ctx, msg)
	return
}

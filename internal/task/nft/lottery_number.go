package nft

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// HandleLotteryNumber 处理抽奖号码
func HandleLotteryNumber(ctx context.Context, svcCtx *svc.ServiceContext, taskReq *xxljob.TaskRequest) (msg string) {
	msg = "[HandleLotteryNumber] start."
	// activityId
	if taskReq.ExecutorParams == "" {
		msg = fmt.Sprintf("[HandleLotteryNumber] Please input activity id params.")
		logc.Errorf(ctx, msg)
		return
	}
	var inputParams service.DealDexUserParams
	err := json.Unmarshal([]byte(taskReq.ExecutorParams), &inputParams)
	if err != nil {
		msg = fmt.Sprintf("[HandleLotteryNumber] Resolve input json params error ： %v.", err)
		logc.Errorf(ctx, msg)
		return
	}

	activityId := inputParams.ActivityId

	// 查询总数
	nftModel := nft_activity.NewNftLotteryModel(svcCtx.DBMarketingActivities)

	queryCondition := []clause.Expression{
		gorm.Expr("activity_id = ?", activityId),
	}
	recordCount, err := nftModel.Count(ctx, queryCondition)
	if err != nil || recordCount == 0 {
		msg = fmt.Sprintf("[HandleLotteryNumber] Get record count error or 0 ,error: %v,count:%v", err, recordCount)
		logc.Errorf(ctx, msg)
		return
	}

	initNumber := 0
	db, lotteryRows, err := nftModel.GetRowsByCondition(ctx, queryCondition)
	if err != nil {
		msg = fmt.Sprintf("[HandleLotteryNumber] Db query lottery user list error:%v", err)
		logc.Errorf(ctx, msg)
		return
	}

	defer lotteryRows.Close()

	// 分配抽奖号码
	for lotteryRows.Next() {
		var tmpItem nft_activity.NftLottery
		err := db.ScanRows(lotteryRows, &tmpItem)
		if err != nil {
			msg = fmt.Sprintf("[HandleLotteryNumber] Scan lottery user rows error:%v", err)
			logc.Errorf(ctx, msg)
			return
		}
		initNumber++
		err = db.Model(&tmpItem).Select("lottery_number").Update("lottery_number", initNumber).Error
		if err != nil {
			msg = fmt.Sprintf("[HandleLotteryNumber] Update lottery user rows error:%v, data:%v", err, tmpItem)
			logc.Errorf(ctx, msg)
			return
		} else {
			msg = fmt.Sprintf("[HandleLotteryNumber] Update lottery table lottery_number success, data:%v,number:%v", tmpItem, initNumber)
			logc.Infof(ctx, msg)
		}
	}

	msg = "[HandleLotteryNumber] end"
	logc.Infof(ctx, msg)
	return
}

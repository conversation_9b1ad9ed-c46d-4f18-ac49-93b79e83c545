package nft

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func ClearLotteryDataByActivityId(ctx context.Context, svcCtx *svc.ServiceContext, taskReq *xxljob.TaskRequest) (msg string) {
	msg = "[ClearLotteryDataByActivityId] Clear start!"
	logc.Infof(ctx, msg)

	if taskReq.ExecutorParams == "" {
		msg = fmt.Sprintf("[ClearLotteryDataByActivityId] Please input params.")
		logc.Errorf(ctx, msg)
		return
	}
	msg = fmt.Sprintf("[ClearLotteryDataByActivityId] Start clear lottery data %v", taskReq.ExecutorParams)
	logc.Infof(ctx, msg)

	var inputParams service.DealDexUserParams
	err := json.Unmarshal([]byte(taskReq.ExecutorParams), &inputParams)
	if err != nil {
		msg = fmt.Sprintf("[ClearLotteryDataByActivityId] Resolve input json params error ： %v.", err)
		logc.Errorf(ctx, msg)
		return
	}

	activityId := inputParams.ActivityId
	lotteryModel := nft_activity.NewNftLotteryModel(svcCtx.DBMarketingActivities)
	clearConditions := []clause.Expression{
		gorm.Expr("activity_id = ?", activityId),
	}
	err = lotteryModel.ClearByCondition(ctx, clearConditions)
	if err != nil {
		msg = fmt.Sprintf("[ClearLotteryDataByActivityId] Clear lottery data error : %v", err)
		logc.Errorf(ctx, msg)
		return
	}
	msg = "[ClearLotteryDataByActivityId] Clear success!"
	logc.Infof(ctx, msg)
	return
}

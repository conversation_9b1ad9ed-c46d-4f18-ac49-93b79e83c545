package nft

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"io"
	"os"
	"strconv"
)

// HandleDexLotteryUser 去中心化抽奖用户处理
func HandleDexLotteryUser(ctx context.Context, svcCtx *svc.ServiceContext, taskReq *xxljob.TaskRequest) (msg string) {
	msg = "[HandleDexLotteryUser] start."
	// activityId,fileUrl
	if taskReq.ExecutorParams == "" {
		msg = fmt.Sprintf("[HandleDexLotteryUser] Please input activity id params.")
		logc.Errorf(ctx, msg)
		return
	}
	var inputParams service.DealDexUserParams
	err := json.Unmarshal([]byte(taskReq.ExecutorParams), &inputParams)
	if err != nil {
		msg = fmt.Sprintf("[HandleDexLotteryUser] Resolve input json params error ： %v.", err)
		logc.Errorf(ctx, msg)
		return
	}

	activityId := inputParams.ActivityId
	//fileUrl := inputParams.FileUrl

	f, err := os.Open("../../../deploy/lottery/dex_user.csv")
	if err != nil {
		msg = fmt.Sprintf("[HandleDexLotteryUser] file error:%v.", err)
		logc.Errorf(ctx, msg)
		return
	}
	defer f.Close()

	lineNumber := 0

	reader := csv.NewReader(f)
	reader.FieldsPerRecord = -1 // 忽略字段数不一致的情况

	var batchData []*nft_activity.NftLottery
	dealNumber := 0
	for {
		var record []string
		record, err = reader.Read()
		if err == io.EOF {
			err = nil
			break
		}
		if err != nil {
			msg = fmt.Sprintf("[HandleDexLotteryUser] 读取CSV文件时出错:%v.", err)
			logc.Errorf(ctx, msg)
			return
		}
		lineNumber++
		if lineNumber == 1 { //表头
			continue
		}
		if len(record) == 0 {
			continue
		}

		// 解析 去中心化钱包地址
		dexAddress := record[0]
		if dexAddress == "" {
			msg = fmt.Sprintf("[HandleDexLotteryUser] 读取钱包地址为空. 第%v行, data:%v", lineNumber, record)
			logc.Warnf(ctx, msg)
			continue
		}
		// 解析集齐几套数量
		setCountStr := record[1]
		setCount, err := strconv.Atoi(setCountStr)
		if err != nil || setCount == 0 {
			msg = fmt.Sprintf("[HandleDexLotteryUser] 读取集齐数量为空，error:%v, 第%v行，data:%v", err, lineNumber, record)
			logc.Warnf(ctx, msg)
			continue
		}
		nftModel := nft_activity.NewNftLotteryModel(svcCtx.DBMarketingActivities)
		// 循环集齐数量插入抽奖表
		for i := 0; i < setCount; i++ {
			//tmpData := &nft_activity.NftLottery{
			//	ActivityId:  activityId,
			//	LotteryId:   dexAddress,
			//	EncryptedId: dexAddress,
			//}
			batchData = append(batchData, &nft_activity.NftLottery{
				ActivityId:  activityId,
				LotteryId:   dexAddress,
				EncryptedId: dexAddress,
			})
			dealNumber++
		}
		if len(batchData) > 2000 {
			err := nftModel.InsertBatch(ctx, batchData, 200)
			if err != nil {
				msg = fmt.Sprintf("[HandleDexLotteryUser] Insert lottery table error: %v, data:%v ", err, batchData)
				logc.Errorf(ctx, msg)
				return msg
			} else {
				//msg = fmt.Sprintf("[HandleDexLotteryUser] Insert lottery table success, data:%v", tmpData)
				//logc.Infof(ctx, msg)
			}
			batchData = batchData[:0]
		}
	}
	if len(batchData) > 0 {
		nftModel := nft_activity.NewNftLotteryModel(svcCtx.DBMarketingActivities)
		err := nftModel.InsertBatch(ctx, batchData, 500)
		if err != nil {
			msg = fmt.Sprintf("[HandleDexLotteryUser] Insert last lottery table error: %v, data:%v ", err, batchData)
			logc.Errorf(ctx, msg)
			return msg
		} else {
			//msg = fmt.Sprintf("[HandleDexLotteryUser] Insert lottery table success, data:%v", tmpData)
			//logc.Infof(ctx, msg)
		}
	}

	msg = fmt.Sprintf("[HandleDexLotteryUser] end， count: %v", dealNumber)
	logc.Infof(ctx, msg)
	return
}

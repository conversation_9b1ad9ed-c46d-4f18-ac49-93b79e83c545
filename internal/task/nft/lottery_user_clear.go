package nft

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func ClearLotteryUserByRound(ctx context.Context, svcCtx *svc.ServiceContext, taskReq *xxljob.TaskRequest) (msg string) {
	if taskReq.ExecutorParams == "" {
		msg = fmt.Sprintf("Please input params.")
		logc.Errorf(ctx, msg)
		return
	}
	msg = fmt.Sprintf("Start clear lottery data  %v", taskReq.ExecutorParams)
	logc.Infof(ctx, msg)

	var inputParams service.LotteryClearParams
	err := json.Unmarshal([]byte(taskReq.ExecutorParams), &inputParams)
	if err != nil {
		msg = fmt.Sprintf("Resolve clear input json params error:%v", err)
		logc.Errorf(ctx, msg)
		return
	}

	msg = fmt.Sprintf("Input clear params: %v, count day params:%v, lottery type params:%v", taskReq.ExecutorParams, inputParams.CountDay, inputParams.LotteryType)
	logc.Infof(ctx, msg)

	lotteryModel := nft_activity.NewNftLotteryStatisticsModel(svcCtx.DBMarketingActivities)
	clearConditions := []clause.Expression{
		gorm.Expr("count_day = ?", inputParams.CountDay),
		gorm.Expr("lottery_type = ?", inputParams.LotteryType),
	}
	err = lotteryModel.ClearByCondition(ctx, clearConditions)
	if err != nil {
		msg = fmt.Sprintf("Clear lottery data error : %v", err)
		logc.Errorf(ctx, msg)
		return
	}
	msg = "Clear success!"
	logc.Infof(ctx, msg)
	return
}

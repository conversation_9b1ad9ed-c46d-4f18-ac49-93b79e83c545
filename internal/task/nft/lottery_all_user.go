package nft

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"fmt"
	"gateio_service_marketing_activity/internal/dao"
	nftModel "gateio_service_marketing_activity/internal/models/db_nft"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/pkg/nft"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"strconv"
	"strings"
	"time"
)

// HandleLotteryUserFinial 统计终极大奖抽奖用户
func HandleLotteryUserFinial(ctx context.Context, svcCtx *svc.ServiceContext, taskReq *xxljob.TaskRequest) (msg string) {
	msg = ""
	// startTime
	if taskReq.ExecutorParams == "" {
		msg = fmt.Sprintf("Please input start time.")
		logc.Errorf(ctx, msg)
		return
	}
	tmpParamsArr := strings.Split(taskReq.ExecutorParams, ";")
	if len(tmpParamsArr) == 0 {
		msg = fmt.Sprintf("Error start time and end time. %v", taskReq.ExecutorParams)
		logc.Errorf(ctx, msg)
		return
	}
	startTime := tmpParamsArr[1]
	endTime := time.Now().String()
	// end time
	if len(tmpParamsArr) > 1 {
		endTime = tmpParamsArr[1]
	}
	msg = fmt.Sprintf("Time params: %v, start time params:%v, end time params:%v", taskReq.ExecutorParams, startTime, endTime)
	logc.Infof(ctx, msg)

	// 根据类型获取所有配置的24个nft列表
	typeIds := []int64{1, 2, 3}
	nftDetailList, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetListByNftIds(ctx, typeIds)
	if err != nil || len(nftDetailList) == 0 {
		msg = fmt.Sprintf("Get config nft ids detail error . %v, error: %v", nftDetailList, err)
		logc.Errorf(ctx, msg)
		return
	}
	// 根据版本分组
	var nftTypeGroupConfigList = make(map[int64]int64)
	for _, tmpConfNft := range nftDetailList {
		nftTypeGroupConfigList[tmpConfNft.NftId] = tmpConfNft.NftType
	}

	// 获取从活动开始到当前的所有nft goods表用户列表
	collectionId := svcCtx.Config.NftMintConf.CollectionId
	token := svcCtx.Config.NftMintConf.Token
	userLimit := 100
	lastUid := int64(0)

	currDay := time.Now().Format("2006-01-02")
	for {
		userListQueryConditions := []clause.Expression{
			gorm.Expr("collection_id = ?", collectionId),
			gorm.Expr("token = ?", token),
			gorm.Expr("own_uid > ?", lastUid),
			gorm.Expr("create_timest > ?", startTime),
			gorm.Expr("create_timest < ?", endTime),
		}

		nftAuctionGoodsModel := nftModel.NewNftAuctionGoodsModel(svcCtx.DBNft)
		lotteryUserLists, err := nftAuctionGoodsModel.GetUserListsGroupByOwn(ctx, userListQueryConditions, userLimit)
		if err != nil || len(lotteryUserLists) == 0 {
			msg = fmt.Sprintf("The all lottery user list is null: %v , error: %s", lotteryUserLists, err)
			logc.Errorf(ctx, msg)
			break
		}

		nftService := nft.NewNft(ctx, svcCtx)
		for _, goodItem := range lotteryUserLists {
			lastUid = goodItem.OwnUid
			userQueryConditions := []clause.Expression{
				gorm.Expr("collection_id = ?", collectionId),
				gorm.Expr("token = ?", token),
				gorm.Expr("own_uid = ?", lastUid),
				gorm.Expr("create_timest > ?", startTime),
				gorm.Expr("create_timest < ?", endTime),
			}
			userGoodsLists, userErr := nftService.QueryUserGoodsForActivity(userQueryConditions)
			if userErr != nil || len(userGoodsLists) == 0 {
				msg = fmt.Sprintf("The all lottery user good list is null: %v , error: %s", userGoodsLists, userErr)
				logc.Infof(ctx, msg)
				continue
			}
			userNftMap := service.UserLotteryNFT{
				NftTypeGroupMap: make(map[int64]map[int64]bool),
			}
			for _, tmpGood := range userGoodsLists {
				tmpUserNftId, _ := strconv.ParseInt(tmpGood.NftId, 10, 64)
				typeId, ok := nftTypeGroupConfigList[tmpUserNftId] //  nft类型 1普通nft 2白银nft 3黄金nft
				if !ok {
					continue
				}
				userNftMap.NftTypeGroupMap[typeId][tmpUserNftId] = true
				if len(userNftMap.NftTypeGroupMap[typeId]) >= 24 { // 用户集齐对应版本的24件作品，可以获得1次抽奖机会。
					lotteryModel := nft_activity.NewNftLotteryStatisticsModel(svcCtx.DBMarketingActivities)
					err := lotteryModel.Insert(ctx, &nft_activity.NftLotteryStatistics{
						UserId:      lastUid,
						LotteryType: typeId,
						CountDay:    currDay,
					})
					if err != nil {
						msg = fmt.Sprintf("User all count error: %v, uid:%v , day: %v", err, lastUid, currDay)
						logc.Errorf(ctx, msg)
					} else {
						msg = fmt.Sprintf("Insert all lottery user, uid:%v , day: %v", lastUid, currDay)
						logc.Infof(ctx, msg)
					}
					break
				}
			}
		}
	}
	msg = "Count lottery all user finish"
	return
}

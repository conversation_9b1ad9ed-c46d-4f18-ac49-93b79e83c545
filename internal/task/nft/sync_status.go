package nft

import (
	"context"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	modelNft "gateio_service_marketing_activity/internal/models/db_nft"
	"gateio_service_marketing_activity/internal/models/nft_mint_log"
	"gateio_service_marketing_activity/internal/svc"
	"time"

	pkgnft "gateio_service_marketing_activity/internal/pkg/nft"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
)

const (
	LOG_PREFIX = "CentralizationSyncStatus "
)

/**
 * 中心化 - 同步铸造状态
 */
func CentralizationSyncStatus(ctx context.Context, svcCtx *svc.ServiceContext, taskReq *xxljob.TaskRequest) (msg string) {
	logc.Infof(ctx, fmt.Sprintf("%s start", LOG_PREFIX))

	// 查询所有未铸造成功的数据
	logModel := nft_mint_log.NewNftMintLogModel(svcCtx.DBMarketingActivities)
	mintNftLogs, err := logModel.ListByFilterCenter(ctx, 3, time.Now().Add(-time.Hour*2), 1000)
	if err != nil {
		msg = fmt.Sprintf("%s ListByFilterCenter err:%v", LOG_PREFIX, err)
		logc.Infof(ctx, msg)
		return
	}
	midList := make([]int64, 0)
	for _, mintNftLog := range mintNftLogs {
		midList = append(midList, mintNftLog.Mid)
	}
	if len(midList) == 0 {
		logc.Infof(ctx, fmt.Sprintf("%s No need to sync status", LOG_PREFIX))
		return
	}

	// 查询已经铸造成功的NFT
	nftService := pkgnft.NewNft(ctx, svcCtx)
	statusMap, err := nftService.GetMintStatusByMids(midList)
	if err != nil {
		logc.Infof(ctx, fmt.Sprintf("%s GetMintStatusByMids err: %v", LOG_PREFIX, err))
		return
	}
	fmt.Println("statusMap", statusMap)

	// 更新状态
	mintLogModel := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities)
	for _, mintNftLog := range mintNftLogs {
		if newStatus, ok := statusMap[mintNftLog.Mid]; ok {
			// 如果是铸造请求，则不处理
			if newStatus == modelNft.StatusMintRequest {
				continue
			}

			// 更新铸造状态
			setStatus := nft_mint_log.MINT_STATUS_SUCCESS
			if newStatus == modelNft.StatusMintFail || newStatus == modelNft.StatusDepoDone || newStatus == modelNft.StatusMintCancel {
				setStatus = nft_mint_log.MINT_STATUS_FAILED
			}
			err = mintLogModel.UpdateOne(ctx,
				mintNftLog.Id,
				[]string{
					"mint_status",
					"updated_at",
				},
				&nft_mint_log.NftMintLog{
					MintStatus: int64(setStatus),
					UpdatedAt:  time.Now(),
				})
			if err != nil {
				logc.Infof(ctx, fmt.Sprintf("%s UpdateOne err: %v", LOG_PREFIX, err))
			}
			logc.Infof(ctx, fmt.Sprintf("%s UpdateOne success: %d %d", LOG_PREFIX, mintNftLog.Id, setStatus))

			// 删除中心化铸造记录的缓存
			mintLogKey := fmt.Sprintf(consts.NftUserMintLogKey, mintNftLog.Uid, mintNftLog.ActivityId)

			// 删除轮询nft铸造结果的缓存
			rollMintNfrKey := fmt.Sprintf(consts.NftRollMintLogKey, mintNftLog.ActivityId, mintNftLog.Uid, "", mintNftLog.OperationId)
			_, _ = svcCtx.Redis.Del(mintLogKey, rollMintNfrKey)
		}
	}

	logc.Infof(ctx, fmt.Sprintf("%s end", LOG_PREFIX))
	return
}

package vip_airdrop

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"errors"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/vip_airdrop"
	"gateio_service_marketing_activity/internal/svc"
	"gorm.io/gorm"
)

const (
	LOG_PREFIX = "VipAirdrop "
)

func SyncApplyTotal(ctx context.Context, svcCtx *svc.ServiceContext, taskReq *xxljob.TaskRequest) (msg string) {
	season := taskReq.ExecutorParams
	logc.Info(ctx, fmt.Sprintf("%s SyncApplyTotal start...", LOG_PREFIX))
	applyTotal, err := vip_airdrop.NewCcVipAirdropUserModel(svcCtx.DBMarketingActivities).
		GetTotalBySeason(ctx, season)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logc.Error(ctx, fmt.Sprintf("%s SyncApplyTotal GetTotalBySeason err:%v", LOG_PREFIX, err))
	}
	if err := svcCtx.Redis.Set(consts.VipAirdropApplyTotal(season), fmt.Sprintf("%d", applyTotal)); err != nil {
		logc.Error(ctx, fmt.Sprintf("%s SyncApplyTotal start", LOG_PREFIX))
	}
	logc.Info(ctx, fmt.Sprintf("%s SyncApplyTotal end...", LOG_PREFIX))
	return
}

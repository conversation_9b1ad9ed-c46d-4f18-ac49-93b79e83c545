package test

import (
	"flag"
	"fmt"
	"gateio_service_marketing_activity/internal/config"
	"gateio_service_marketing_activity/internal/svc"
	"syscall"

	"bitbucket.org/gatebackend/go-zero/core/conf"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"github.com/joho/godotenv"
)

var configFile = flag.String("f", "../../../etc/marketing_activity.yaml", "the config file")

// initTestContext 封装初始化和关闭的通用逻辑.
func initTestContext() (*svc.ServiceContext, error) {
	// 初始化本地配置
	_ = godotenv.Load("../../../.env")
	// 解析命令行参数
	flag.Parse()
	// 加载配置文件
	var c config.Config
	conf.MustLoad(*configFile, &c, conf.UseEnv())

	// 如果有Nacos配置，进行加载
	if len(c.Nacos.Ip) != 0 {
		cc, err := config.LoadConfigFromNacos(c.Nacos)
		if err != nil {
			logx.Must(err)
		}
		c = *cc
	}
	// 初始化ServiceContext
	ctx := svc.NewServiceContext(&c)
	return ctx, nil
}

// closeTestContext 负责关闭上下文及资源清理.
func closeTestContext(ctx *svc.ServiceContext) {
	defer ctx.Close()
	fmt.Println("Sending shutdown signal...")
	_ = syscall.Kill(syscall.Getpid(), syscall.SIGINT)
	fmt.Println("Application exited.")
}

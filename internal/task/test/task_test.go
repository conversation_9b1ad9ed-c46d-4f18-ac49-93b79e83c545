package test

import (
	"context"
	"gateio_service_marketing_activity/internal/pkg/nft"
	nftHandler "gateio_service_marketing_activity/internal/task/nft"
	"testing"

	"bitbucket.org/gatebackend/go-zero/job/xxljob"
)

func Test_Mint(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	nftObject := nft.NewNft(context.Background(), ctx)
	res, err := nftObject.Mint(1879042189, 1, 0.00000000, "USDT")

	t.Logf("新增mint记录：%v, 错误：%v", res, err)
}

func Test_GetUserGoods(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	nftObject := nft.NewNft(context.Background(), ctx)
	res, err := nftObject.GetUserGoods(2124433560, 0, "")

	t.Logf("用户goods记录：%v, 错误：%v", res, err)
}

func Test_HandleUserWeight(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	nftHandler.HandleUserWeight(context.Background(), &xxljob.TaskRequest{}, ctx)
	t.Log("finish")
}

func Test_HandleUserLotteryByRound(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	nftHandler.HandleLotteryUserByRound(context.Background(), ctx, &xxljob.TaskRequest{
		//ExecutorParams: "2025-05-25 00:00:00;1,2,3,4,5,6",
		ExecutorParams: `{
			"stime": "2025-05-01 00:00:00",
			"nft_ids": [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18],
			"etime": "2025-05-15 00:00:00"
		}`,
	})
	t.Log("finish")
}

func Test_HandleUserLottery(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	nftHandler.HandleLotteryUserFinial(context.Background(), ctx, &xxljob.TaskRequest{
		ExecutorParams: "2025-05-25 00:00:00",
	})
	t.Log("finish")
}

func Test_ClearUserLotteryByRound(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	nftHandler.ClearLotteryUserByRound(context.Background(), ctx, &xxljob.TaskRequest{
		//ExecutorParams: "2025-05-25 00:00:00;1,2,3,4,5,6",
		ExecutorParams: `{
			"count_day": "2025-06-03",
			"lottery_type": 0
		}`,
	})
	t.Log("finish")
}

func Test_FixStatusNoSync(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	nftHandler.FixStatusNoSync(context.Background(), ctx, &xxljob.TaskRequest{})
	t.Log("finish")
}

func Test_InsertGood(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	nftObject := nft.NewNft(context.Background(), ctx)

	for i := 0; i < 100; i++ {
		//rand.Seed(time.Now().UnixNano()) // 初始化随机种子
		//num := rand.Intn(16) + 1
		for n := 1; n < 19; n++ {
			nftObject.TestInsert(ctx, 1879212924, 0, int64(n))
		}
	}
}

func Test_Airdrop(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	nftObject := nft.NewNft(context.Background(), ctx)
	_, err = nftObject.Airdrop(1879042189, "2")

	t.Logf("空投nft错误：%v", err)
}

// 中心化 - 同步铸造状态
func Test_CentralizationSyncStatus(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	nftHandler.CentralizationSyncStatus(context.Background(), ctx, &xxljob.TaskRequest{})
	t.Log("finish")
}

func Test_DealDexUser(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	nftHandler.HandleDexLotteryUser(context.Background(), ctx, &xxljob.TaskRequest{
		ExecutorParams: `{
			"activity_id": 1
		}`,
	})
	t.Log("finish")
}

func Test_DealCexUser(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	//uid, _ := encrypted.NewEncrypted(context.Background()).GetDecryptedId("U1cEDAQGVgJRAAO0O0OO0O0O", "af6804a6b1ae6f0d5f217a33a0995217")
	//t.Log("uid is", uid)
	nftHandler.HandleCexLotteryUser(context.Background(), ctx, &xxljob.TaskRequest{
		ExecutorParams: `{
			"count_day": "2025-05-15",
			"lottery_type": 0,
			"activity_id": 1
		}`,
	})
	t.Log("finish")
}
func Test_DealLotteryNumber(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	nftHandler.HandleLotteryNumber(context.Background(), ctx, &xxljob.TaskRequest{
		ExecutorParams: `{
			"activity_id": 1
		}`,
	})
	t.Log("finish")
}

func Test_ClearLotteryDataByActivity(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	nftHandler.ClearLotteryDataByActivityId(context.Background(), ctx, &xxljob.TaskRequest{
		//ExecutorParams: "2025-05-25 00:00:00;1,2,3,4,5,6",
		ExecutorParams: `{
			"activity_id": 1
		}`,
	})
	t.Log("finish")
}

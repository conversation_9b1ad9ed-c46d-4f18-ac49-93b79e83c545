package download

//下载专项
import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/core/trace"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/datacenter"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/consts/download"
	"gateio_service_marketing_activity/internal/consts/kafka"
	"gateio_service_marketing_activity/internal/models/download_activity"
	"gateio_service_marketing_activity/internal/svc"
	"strconv"
	"time"

	"bitbucket.org/gatebackend/go-zero/job/xxljob"
)

const BEGIN = 1745920800 //活动的开始时间
const UserActivityEndTimeCacheKey = "marketing_activity_end_time_cache"
const UserRegisterCount = "marketing_activity_user_register_count"
const GoroutineNum = 1
const ExtraUserNum = 327

func DownloadTask(ctx context.Context, svcCtx *svc.ServiceContext, param *xxljob.TaskRequest) (msg string) {
	logc.Infof(ctx, "执行任务: %d", param.JobID)
	traceID := trace.TraceIDFromContext(ctx)
	//1 检测当前活动的生成逻辑
	dataModel := download_activity.NewDownloadActivityModel(svcCtx.DBMarketingActivities)
	invitationRelation := download_activity.NewDownloadInvitationRelationModel(svcCtx.DBMarketingActivities)
	var err error
	//lastActivityInfo, err := dataModel.GetLastActivityInfo(ctx) //获取最后一次的活动
	//if err != nil {
	//	logx.WithContext(ctx).Errorf("[DownloadTask]GetLastActivityInfo, err:%v", err)
	//	return
	//}
	//if lastActivityInfo.Id == 0 {
	//	lastActivityInfo.EndTime = BEGIN
	//	err = dataModel.Insert(ctx, &download_activity.DownloadActivity{StartTime: lastActivityInfo.EndTime + 1, EndTime: lastActivityInfo.EndTime + 15*24*3600, ExtraUserNum: ExtraUserNum})
	//	if err != nil {
	//		logx.WithContext(ctx).Errorf("[DownloadTask]insert new activity err: %v", err)
	//		return
	//	}
	//	// 如果数据库里面没有活动，那么就创建一条活动进去。这次先不执行
	//	return
	//}
	//if lastActivityInfo.EndTime-3600*24 < time.Now().Unix() {
	//	err = dataModel.Insert(ctx, &download_activity.DownloadActivity{StartTime: lastActivityInfo.EndTime + 1, EndTime: lastActivityInfo.EndTime + 15*24*3600})
	//	if err != nil {
	//		logx.WithContext(ctx).Errorf("[DownloadTask]Insert new task fail, %s", err)
	//	}
	//}
	// 检测注册任务进度
	invite := make(chan *InviteCheck, 10)
	app := make(chan *AppCheck, 10)
	register := make(chan *Register, 10)
	invite_over := make(chan struct{}, 1)
	app_over := make(chan struct{}, 1)
	register_over := make(chan struct{}, 1)
	// 检测app下载进度
	taskRecord := download_activity.NewDownloadTaskRecordModel(svcCtx.DBMarketingActivities)
	var last_tast_record_id int64
	//注册任务
	go func(InviteParam chan *InviteCheck, over chan struct{}) {
		for {
			inviteInfo, ok := <-InviteParam
			if !ok {
				over <- struct{}{}
				fmt.Println("register over")
				return
			}
			inviteInfo.UserTimeCheck(ctx).UserCheck(ctx).KycCheck(ctx)

			if inviteInfo.Change == download.CHANGE {
				fmt.Println(inviteInfo, "update")

				err = taskRecord.UpdateUserTaskByType(ctx, inviteInfo.Uid, inviteInfo.TaskId, inviteInfo.Status, inviteInfo.Progress)
				//更新逻辑
				if err != nil {
					logc.Info(ctx, fmt.Sprintf("traceID: %s,任务类型%s,%s", traceID, download.INVITEID, err.Error()))
				}

			}
		}

	}(invite, invite_over)

	go func(appParam chan *AppCheck, over chan struct{}) {

		for {

			appInfo, ok := <-appParam
			if !ok {
				over <- struct{}{}
				fmt.Println("app over")
				return
			}
			appInfo.UserTimeCheck(ctx).CheckAppLastTime(ctx).CheckUserMoney(ctx)
			fmt.Println(appInfo)
			if appInfo.Change == download.CHANGE {
				fmt.Println(appInfo, "update")
				//更新逻辑
				err = taskRecord.UpdateUserTaskByType(ctx, appInfo.Uid, appInfo.TaskId, appInfo.Status, appInfo.Progress)
				if err != nil {
					logc.Info(ctx, fmt.Sprintf("traceID: %s,任务类型%s,%s", traceID, download.APP, err.Error()))
				}
			}
		}
	}(app, app_over)
	//邀请任务
	go func(checkParam chan *Register, over chan struct{}) {
		for {
			check, ok := <-checkParam
			fmt.Println(check, ok)
			if !ok {
				over <- struct{}{}
				fmt.Println("invite over")
				return
			}
			count, err := invitationRelation.GetRefUserDownNum(ctx, check.Uid)
			fmt.Println("GetRefUserDownNum", count)
			if err != nil {
				return
			}
			check.UserTimeCheck(ctx).UserMoneyCheck(ctx)
			if check.Change == download.CHANGE {
				//更新逻辑
				err = invitationRelation.UpdateUserTaskByUid(ctx, check.Uid, check.Status, check.Progress)
				if err != nil {
					logc.Info(ctx, fmt.Sprintf("traceID: %s,任务类型%s,%s", traceID, download.REGISTER, err.Error()))

				}
			}
		}
	}(register, register_over)
	//app下载任务
	last_tast_record_id = 0
	activityTimes := make(map[int64]struct {
		StartTime int64
		EndTime   int64
	},
	)
	for {
		l, err := taskRecord.GetTaskRecordListDoing(ctx, last_tast_record_id)
		if err != nil {
			logc.Info(ctx, err.Error())
			break
		}
		if len(l) == 0 {
			close(invite)
			close(app)
			close(register)
			logc.Info(ctx, "close")
			break
		}

		for _, v := range l {
			timeStruct, ok := activityTimes[v.Aid]
			if !ok {
				//数据库拉去
				aidInfo, err := dataModel.FindOne(ctx, v.Aid)
				if err != nil {
					logc.Info(ctx, fmt.Sprintf("traceID: %s,任务类型%s,%s", traceID, download.REGISTER, err.Error()))
					return
				}
				activityTimes[v.Aid] = struct {
					StartTime int64
					EndTime   int64
				}{StartTime: aidInfo.StartTime, EndTime: aidInfo.EndTime}
				timeStruct = struct {
					StartTime int64
					EndTime   int64
				}{StartTime: aidInfo.StartTime, EndTime: aidInfo.EndTime}
			}
			last_tast_record_id = v.Id
			if v.TaskId == 1 {
				//注册
				invite <- &InviteCheck{Uid: v.Uid, TaskId: v.TaskId, Status: v.Status, Progress: v.Progress, StartTime: timeStruct.StartTime, EndTime: timeStruct.EndTime}
			} else if v.TaskId == 2 {
				app <- &AppCheck{Uid: v.Uid, TaskId: v.TaskId, Status: v.Status, Progress: v.Progress, StartTime: timeStruct.StartTime, EndTime: timeStruct.EndTime}
			} else if v.TaskId == 3 {
				//邀请任务特殊处理 循环邀请列表
				var invite_index int64 = 0
				for {
					invitationList, err := invitationRelation.GetRefUserByRefUidAndId(ctx, v.Uid, invite_index)
					if err != nil {
						break
					}
					if len(invitationList) == 0 {
						break
					}
					for _, l := range invitationList {
						logc.Info(ctx, l)
						register <- &Register{Uid: l.Uid, TaskId: v.TaskId, Status: l.Status, Progress: v.Progress, StartTime: timeStruct.StartTime, EndTime: timeStruct.EndTime}
						invite_index = l.Id
					}

				}

			}
		}

	}
	//检测邀友返佣进度
	<-register_over
	<-app_over
	<-invite_over

	logc.Info(ctx, fmt.Sprintf("traceID: %s", traceID))

	return fmt.Sprintf("traceID: %s", traceID)

}

// 数据中台查询用户指定日志的交易量
func getUserAppSourceBuy(ctx context.Context, userId int, date string) {
	dateClient := datacenter.Client{}
	req := datacenter.QueryExecRequest{ApiID: 999, FilterParams: map[string]interface{}{"user_id": userId, "date": date}}
	dateClient.QueryExec(ctx, &req)
}

// 邀请回到消息
func DownloadTaskRegisterCallBack(ctx context.Context, svcCtx *svc.ServiceContext, msgStr string) {
	logx.Infof("download，消息解析成功，收到任务系统回调消息：%s", msgStr)

	// 解析消息内容
	taskCallBackMsg := kafka.UserRegisterCallBackMsg{}
	err := json.Unmarshal([]byte(msgStr), &taskCallBackMsg)
	if err != nil {
		logx.Infof("download Unmarshal Msg err: %v", err)
		return
	}
	//获取邀请用户的活动
	endTimeStr, err := svcCtx.Redis.GetCtx(ctx, fmt.Sprintf("%s%d", UserActivityEndTimeCacheKey, taskCallBackMsg.RefUid))
	endTime, _ := strconv.ParseInt(endTimeStr, 10, 64)
	taskRecord := download_activity.NewDownloadTaskRecordModel(svcCtx.DBMarketingActivities)
	invitationRelation := download_activity.NewDownloadInvitationRelationModel(svcCtx.DBMarketingActivities)
	activity := download_activity.NewDownloadActivityModel(svcCtx.DBMarketingActivities)
	if err != nil {
		logx.Infof("redis get cache err: %v", err)
	} else {
		userTaskRectod, err := taskRecord.GetTaskRecordByUid(ctx, taskCallBackMsg.RefUid)
		if err != nil {
			logx.Infof("mysql get  err: %v", err)
		}

		activityInfo, err := activity.GetActivityById(ctx, userTaskRectod.Aid)
		if activityInfo.Id == 0 {
			return
		}
		endTime = activityInfo.EndTime

		svcCtx.Redis.SetexCtx(ctx, fmt.Sprintf("%s%d", UserActivityEndTimeCacheKey, taskCallBackMsg.Uid), strconv.FormatInt(endTime, 10), 3600)
	}
	timeInt, err := time.Parse(time.DateTime, taskCallBackMsg.Timest)
	if timeInt.Unix() > endTime {
		logx.Infof("用户已经结束活动")
		return
	}
	countStr, err := svcCtx.Redis.GetCtx(ctx, fmt.Sprintf("%s%d", UserRegisterCount, taskCallBackMsg.RefUid))
	count, _ := strconv.Atoi(countStr)
	if count >= 50 { // 邀请用户完成50人
		logx.Infof("用户已经完成邀请上限")
		return
	} else {
		count, err := taskRecord.GetTaskRecordCountByTypeRegister(ctx, taskCallBackMsg.RefUid)
		if err != nil {
			logx.Infof("mysql get  err: %v", err)
			return
		}
		if count >= 50 {
			svcCtx.Redis.SetexCtx(ctx, fmt.Sprintf("%s%d", UserRegisterCount, taskCallBackMsg.Uid), strconv.FormatInt(count, 64), 3600)
			return
		}
	}
	//写入数据
	_, _ = invitationRelation.InsertOne(ctx, taskCallBackMsg.Uid, taskCallBackMsg.RefUid, 1)
	logx.Infof("NftTaskCallBack Done，回调消息：%s", msgStr)
}

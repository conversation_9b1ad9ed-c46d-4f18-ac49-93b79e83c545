package download

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/usercenter"
	"context"
	"fmt"
	"time"
)

// 注册任务
type InviteCheck struct {
	Uid       int64
	TaskId    int64
	StartTime int64 `json:"start_time"` //开始时间
	EndTime   int64 `json:"end_time"`   //开始时间
	Status    int64 `json:"status"`     //0 未领取  1 进行中 2 完成  3 失败
	Progress  int64 `json:"progress"`
	Change    int64 `json:"change"`
}

const INVITE_PROCESS_INIT = 0 //初始状态
const INVITE_PROCESS_USER = 1 //用户注册验证通过
const INVITE_PROCESS_KYC = 2  //用户kyc通过

/*
*
状态机
*/
func (i *InviteCheck) UserTimeCheck(ctx context.Context) *InviteCheck {
	now := time.Now().Unix()
	//用户邀请的用户完成人数
	if i.EndTime < now {
		i.Status = 1
		i.Change = 1
	} else {
		if i.Status == 1 {
			i.Progress = INVITE_PROCESS_USER
		} else {

			i.Status = 3
			i.Change = 1
		}

	}
	return i
}

/*
*
检测进度0->1
*/
func (i *InviteCheck) UserCheck(ctx context.Context) *InviteCheck {
	if i.Progress == INVITE_PROCESS_USER && i.Status == 1 { //
		ret, _, err := getUserInfoByUserCenter(ctx, i.Uid)
		if err != nil {
			logc.Info(ctx, fmt.Sprintf("UserCheck: %s", err.Error()))
			return i
		}
		logc.Info(ctx, "UserCheck ret", i.Uid, ret, i.StartTime, i.EndTime)

		if ret >= i.StartTime && ret <= i.EndTime {
			i.Progress = INVITE_PROCESS_KYC
			i.Change = 1
		}
	}
	return i
}

/*
*
检测进度 1->2
*/
func (i *InviteCheck) KycCheck(ctx context.Context) *InviteCheck {

	if i.Progress == INVITE_PROCESS_KYC && i.Status == 1 {
		ret, err := getUserKycInfo(ctx, i.Uid)
		logc.Info(ctx, "KycCheck ret", i.Uid, ret)

		if err != nil {
			return i
		}
		if ret {
			i.Progress = INVITE_PROCESS_KYC
			i.Status = 2
			i.Change = 1
		}
	}

	return i
}

// 注册任务检测----------------------------------------//
/**
UserDetail struct 中RegTime注册时间
*/

func getUserInfoByUserCenter(ctx context.Context, userId int64) (int64, string, error) {
	userCenterClient := usercenter.NewClient()
	reqInfo, err := userCenterClient.GetUserList(ctx, &usercenter.GetUserListRequest{[]int64{userId}})

	if err != nil {
		return 0, "", err
	}
	if len(reqInfo.List) == 0 {
		return 0, "", err
	}

	t, err := time.Parse(time.DateTime, reqInfo.List[0].Timest)
	if err != nil {
		return 0, "", err
	}
	return t.Unix(), reqInfo.List[0].UserInfo.FromChannel, nil

}

/*
*
用户是否进行了注册和kyc
*/
func getUserKycInfo(ctx context.Context, userId int64) (bool, error) {
	userCenterClient := usercenter.NewClient()
	reqInfo, err := userCenterClient.GetUserKYCInfo(ctx, usercenter.GetUserKYCInfoRequest{userId})
	logc.Info(ctx, "getUserKycInfo", userId, reqInfo, err)
	if err != nil {
		return false, err
	}
	if reqInfo.Verified != 0 {
		return true, nil
	}
	return false, nil

}

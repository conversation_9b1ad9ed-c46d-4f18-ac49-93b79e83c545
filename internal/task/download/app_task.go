package download

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gateio/gateio-lib-base-go/environment"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/datacenter"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/usercenter"
	"context"
	"strconv"
	"time"
)

// app下载
type AppCheck struct {
	Uid       int64
	TaskId    int64
	StartTime int64 `json:"start_time"` //开始时间
	EndTime   int64 `json:"end_time"`   //开始时间
	Status    int64 `json:"status"`     //0 未领取  1 进行中 2 完成  3 失败
	Progress  int64 `json:"progress"`   //0初始胡  1 登录有效期 2 验证完成入金
	Change    int64 `json:"change"`
}

/*
*
有效期检测
*/

func (i *AppCheck) UserTimeCheck(ctx context.Context) *AppCheck {
	now := time.Now().Unix()
	if now >= i.EndTime {
		if i.Status == 2 {
			return i
		} else {
			i.Status = 3
			i.Change = 1
		}
	}
	return i
}

func (i *AppCheck) CheckAppLastTime(ctx context.Context) *AppCheck {
	if i.Status == 1 && i.Progress == 0 {
		lastTime := getAppLastTime(ctx, i.Uid)
		logc.Infof(ctx, "CheckAppLastTime:%v, i:%v", lastTime, i)
		if lastTime >= i.StartTime && lastTime <= i.EndTime {
			i.Progress = 1
			i.Change = 1
		}
	}
	return i
}
func (i *AppCheck) CheckUserMoney(ctx context.Context) *AppCheck {
	if i.Status == 1 && i.Progress == 1 {
		logc.Infof(ctx, "uid:%v,CheckUserMoney doing", i.Uid)
		ret, err := GetUserMoney(ctx, i.Uid, i.StartTime, i.EndTime)

		if err != nil {
			logc.Infof(ctx, "[AppCheck]GetUserMoney uid:%v err:%v", i.Uid, err.Error())
			return i
		}
		logc.Infof(ctx, "CheckUserMoney uid:%v ret:%v", i.Uid, ret)
		if ret {
			i.Progress = 2
			i.Status = 2
			i.Change = 1
		}
	}
	return i
}
func getAppLastTime(ctx context.Context, uid int64) int64 {
	uidStr := strconv.FormatInt(uid, 10)
	userCenterClient := usercenter.NewClient()
	req, err := userCenterClient.GetAppLastReportInfos(ctx, &usercenter.GetAppLastReportInfosRequest{Uids: uidStr})
	if err != nil {
		return 0
	}
	return req.Data[uidStr].CreateAt
}
func getAppid() int64 {
	if environment.IsLocal() {
		return 1916827281333309440
	}
	if environment.IsTest() {
		return 1916827281333309440
	}

	if environment.IsPre() {
		return 1917024423033192448
	}
	if environment.IsProd() {
		return 1917024423033192448
	}
	return 0
}

func GetUserMoney(ctx context.Context, uid, startTime, endTime int64) (bool, error) {
	dataClient := datacenter.NewClient()
	//现货数据
	ret, err := dataClient.QueryExec(ctx,
		&datacenter.QueryExecRequest{
			ApiID:        getAppid(),
			FilterParams: map[string]interface{}{"uid": uid, "start_time": time.Unix(startTime, 0).Format(time.DateOnly), "end_time": time.Unix(endTime, 0).Format(time.DateOnly)},
			OrderFields:  []string{},
			Page:         1,
			PageSize:     10})
	if err != nil {
		logc.Errorf(ctx, "[GetUserMoney]QueryExec,uid:%v, err:%v", uid, err.Error())
		return false, err
	}
	logc.Infof(ctx, "GetUserMoney ret uid:%v, ok", uid)

	retStructVal, ok := ret.List.([]interface{})
	if len(retStructVal) == 0 || !ok {
		logc.Infof(ctx, "[GetUserMoney]GetUserMoney uid:%v len(retStructVal) is 0", uid)
		return false, nil
	}

	retMap, ok := retStructVal[0].(map[string]interface{})

	if !ok {
		logc.Infof(ctx, "[GetUserMoney]GetUserMoney uid:%v retMap is not ok", uid)
		return false, nil
	}
	if ret.TotalCount != 0 && len(retMap) > 0 {
		contract_sum, ok := retMap["contract_sum"].(string)

		if ok && contract_sum != "" && contract_sum != "0.0000000000" {
			logc.Infof(ctx, "GetUserMoney uid:%v ret contract_sum  not 0", uid)

			return true, nil

		}
		spot_sum, ok := retMap["spot_sum"].(string)

		if ok && spot_sum != "" && spot_sum != "0.0000000000" {
			logc.Infof(ctx, "GetUserMoney uid:%v ret spot_sum not 0", uid)

			return true, nil

		}

	}

	return false, nil

}

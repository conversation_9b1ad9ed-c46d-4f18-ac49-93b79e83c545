package download

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"context"
	"time"
)

// 邀请任务
type Register struct {
	Uid       int64
	TaskId    int64
	StartTime int64 `json:"start_time"` //开始时间
	EndTime   int64 `json:"end_time"`   //开始时间
	Status    int64 `json:"status"`     //0 未领取  1 进行中 2 完成  3 失败
	Progress  int64 `json:"progress"`   //0初始化 1 完成入金
	Change    int64 `json:"Change"`     //0初始化  1 完成入金
}

/*
*
有效期检测
*/

func (i *Register) UserTimeCheck(ctx context.Context) *Register {
	now := time.Now().Unix()
	if now >= i.EndTime {
		if i.Status == 2 {
			return i
		} else {
			i.Status = 3
			i.Change = 1
		}

	}
	return i
}

func (i *Register) UserMoneyCheck(ctx context.Context) *Register {
	if i.Status == 1 {
		ret, err := GetUserMoney(ctx, i.Uid, i.StartTime, i.EndTime)
		if err != nil {
			logc.Error(ctx, "UserMoneyCheck err", err.Error())
			return i
		}
		if ret {
			i.Status = 2
			i.Change = 1
			i.Progress = 1
		}
	}
	return i

}

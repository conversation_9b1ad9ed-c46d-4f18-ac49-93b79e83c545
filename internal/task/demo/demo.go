package demo

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/trace"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/svc"

	"bitbucket.org/gatebackend/go-zero/job/xxljob"
)

func Demo(ctx context.Context, svcCtx *svc.ServiceContext, param *xxljob.TaskRequest) (msg string) {
	logc.Infof(ctx, "执行任务: %d", param.JobID)

	printJSON(param)
	traceID := trace.TraceIDFromContext(ctx)

	logc.Info(ctx, "test info msg")
	logc.Info(ctx, fmt.Sprintf("traceID: %s", traceID))
	logc.Error(ctx, "test error msg")

	return fmt.Sprintf("traceID: %s", traceID)
}

func printJSON(data interface{}) {
	b, _ := json.MarshalIndent(data, "", "  ")
	fmt.Println(string(b))
}

package nft

import (
	"context"
	"fmt"
	"time"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/help_invite_task"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"gorm.io/gorm"
)

const (
	LOG_PREFIX = "HelpGetCouponEndTimeCheck"
)

/**
 * 助力领券-剩余时间判断和奖励提醒
 */
func HelpEndTimeCheck(ctx context.Context, svcCtx *svc.ServiceContext, taskReq *xxljob.TaskRequest) (msg string) {
	logc.Infof(ctx, fmt.Sprintf("%s start", LOG_PREFIX))

	// 统计变量
	var (
		expiredCount      int
		reminder3Count    int
		reminder1Count    int
		rewardRemindCount int
		updateFailedCount int
	)

	// 查询HelpInviteTask数据库表，根据IsInviterTask=consts.IsInviterTask status=consts.HelpStatusInProgress查询记录
	tasks, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).FindInviterTaskByCreateTime(ctx, time.Now().Add(-time.Hour*72))
	if err != nil {
		msg = fmt.Sprintf("%s FindInviterTaskByCreateTime err:%v", LOG_PREFIX, err)
		logc.Infof(ctx, msg)
		return
	}

	if len(tasks) == 0 {
		logc.Infof(ctx, fmt.Sprintf("%s No tasks need to check", LOG_PREFIX))
		return
	}

	logc.Infof(ctx, fmt.Sprintf("%s Found %d tasks to check", LOG_PREFIX, len(tasks)))

	now := time.Now()
	for _, task := range tasks {
		// 计算剩余时间（秒）
		endTime := time.Unix(task.EndTime, 0) // Convert int64 to time.Time
		timeLeft := int64(endTime.Sub(now).Seconds())

		// 剩余时间小于0，发送超时站内信并修改状态为失败
		if timeLeft < 0 {
			// 发送超时站内信
			_, err = service.SendHeraldMessages(svcCtx, ctx, consts.HeraldTaskExpired, []string{"mobile"}, []int64{task.Uid}, "", []string{})
			if err != nil {
				logc.Infof(ctx, fmt.Sprintf("%s SendHeraldMessages task expired failed, taskId:%d, uid:%d, err:%v", LOG_PREFIX, task.Id, task.Uid, err))
			} else {
				logc.Infof(ctx, fmt.Sprintf("%s Task expired notification sent, taskId:%d, uid:%d", LOG_PREFIX, task.Id, task.Uid))
			}

			// 修改状态为失败
			updateTask := &help_invite_task.HelpInviteTask{
				Id:     task.Id,
				Status: consts.HelpStatusFailed,
			}
			_, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).UpdateHelpInviteTask(ctx, updateTask)
			if err != nil {
				logc.Infof(ctx, fmt.Sprintf("%s UpdateHelpInviteTask failed, taskId:%d, uid:%d, err:%v", LOG_PREFIX, task.Id, task.Uid, err))
				updateFailedCount++
			} else {
				logc.Infof(ctx, fmt.Sprintf("%s Task status updated to failed, taskId:%d, uid:%d", LOG_PREFIX, task.Id, task.Uid))
				expiredCount++
			}
			continue
		}

		// 剩余时间在72-71小时之间，发送提醒站内信
		if timeLeft >= 71*3600 && timeLeft <= 72*3600 {
			_, err = service.SendHeraldMessages(svcCtx, ctx, consts.HeraldTaskRemain3, []string{"mobile"}, []int64{task.Uid}, "", []string{})
			if err != nil {
				logc.Infof(ctx, fmt.Sprintf("%s SendHeraldMessages 72h reminder failed, taskId:%d, uid:%d, err:%v", LOG_PREFIX, task.Id, task.Uid, err))
			} else {
				logc.Infof(ctx, fmt.Sprintf("%s 72h reminder notification sent, taskId:%d, uid:%d", LOG_PREFIX, task.Id, task.Uid))
				reminder3Count++
			}
		}

		// 剩余时间在24-23小时之间，发送提醒站内信
		if timeLeft >= 23*3600 && timeLeft <= 24*3600 {
			_, err = service.SendHeraldMessages(svcCtx, ctx, consts.HeraldTaskRemain1, []string{"mobile"}, []int64{task.Uid}, "", []string{})
			if err != nil {
				logc.Infof(ctx, fmt.Sprintf("%s SendHeraldMessages 24h reminder failed, taskId:%d, uid:%d, err:%v", LOG_PREFIX, task.Id, task.Uid, err))
			} else {
				logc.Infof(ctx, fmt.Sprintf("%s 24h reminder notification sent, taskId:%d, uid:%d", LOG_PREFIX, task.Id, task.Uid))
				reminder1Count++
			}
		}
	}

	// 查询已完成但奖励未领取的任务（完成时间在72-73小时之间）
	finishTasks, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).FindFinishInviterTaskByFinishTime(ctx, time.Now().Add(-time.Hour*72), time.Now().Add(-time.Hour*73))
	if err != nil {
		logc.Infof(ctx, fmt.Sprintf("%s FindFinishInviterTaskByFinishTime err:%v", LOG_PREFIX, err))
	} else {
		logc.Infof(ctx, fmt.Sprintf("%s Found %d finished tasks to check for reward reminder", LOG_PREFIX, len(finishTasks)))

		for _, task := range finishTasks {
			// 检查用户是否已领取奖励
			prize, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).FindUserPrizeByTaskId(ctx, task.Id, task.Uid)
			if err != nil && err != gorm.ErrRecordNotFound {
				logc.Infof(ctx, fmt.Sprintf("%s FindUserPrizeByTaskId failed, taskId:%d, uid:%d, err:%v", LOG_PREFIX, task.Id, task.Uid, err))
				continue
			}

			// 如果奖励未领取，发送提醒
			if err == gorm.ErrRecordNotFound || prize == nil || prize.PrizeStatus == consts.HelpUserPrizeStatusInit || prize.PrizeStatus == consts.HelpUserPrizeStatusUngot || prize.PrizeStatus == consts.HelpUserPrizeStatusFailed {
				_, err = service.SendHeraldMessages(svcCtx, ctx, consts.HeraldRewardRemind, []string{"mobile"}, []int64{task.Uid}, "", []string{})
				if err != nil {
					logc.Infof(ctx, fmt.Sprintf("%s SendHeraldMessages reward reminder failed, taskId:%d, uid:%d, err:%v", LOG_PREFIX, task.Id, task.Uid, err))
				} else {
					logc.Infof(ctx, fmt.Sprintf("%s Reward reminder notification sent, taskId:%d, uid:%d", LOG_PREFIX, task.Id, task.Uid))
					rewardRemindCount++
				}
			}
		}
	}

	// 输出统计信息
	logc.Infof(ctx, fmt.Sprintf("%s end, processed %d tasks, expired:%d, 72h_reminder:%d, 24h_reminder:%d, reward_reminder:%d, update_failed:%d",
		LOG_PREFIX, len(tasks), expiredCount, reminder3Count, reminder1Count, rewardRemindCount, updateFailedCount))

	msg = fmt.Sprintf("%s completed successfully, processed %d tasks", LOG_PREFIX, len(tasks))
	return
}

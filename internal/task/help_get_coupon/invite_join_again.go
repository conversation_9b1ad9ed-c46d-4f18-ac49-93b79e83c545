package nft

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
)

const (
	LOG_PREFIX_INVITE = "HelpGetCoupon_InviteJoinAgain"
)

/**
 * 助力领券-邀请再次参与
 */
func InviteJoinAgain(ctx context.Context, svcCtx *svc.ServiceContext, taskReq *xxljob.TaskRequest) (msg string) {
	logc.Infof(ctx, fmt.Sprintf("%s start", LOG_PREFIX_INVITE))

	// 查询HelpUserJoin数据库表，查询条件是修改时间在前一个月和一个月零一天之间
	oneMonthAgo := time.Now().AddDate(0, -1, 0)
	oneMonthOneDayAgo := time.Now().AddDate(0, -1, -1)

	users, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).FindHelpUserJoinByTimeRange(ctx, oneMonthOneDayAgo, oneMonthAgo)
	if err != nil {
		msg = fmt.Sprintf("%s FindHelpUserJoinByTimeRange err:%v", LOG_PREFIX_INVITE, err)
		logc.Infof(ctx, msg)
		return
	}

	if len(users) == 0 {
		logc.Infof(ctx, fmt.Sprintf("%s No users need to invite", LOG_PREFIX_INVITE))
		return
	}

	logc.Infof(ctx, fmt.Sprintf("%s Found %d users to invite", LOG_PREFIX_INVITE, len(users)))

	// 统计变量
	var successCount int
	var failedCount int
	InviterRewardValue := service.GetTaskRewardDetail(svcCtx, ctx, consts.GetHelpInviterTaskId()).RewardNum
	InviterRewardValueStr := strconv.FormatInt(InviterRewardValue, 10)

	// 遍历用户，发送邀请再次参与的站内信
	for _, user := range users {
		_, err = service.SendHeraldMessages(svcCtx, ctx, consts.HeraldInviteJoinAgain, []string{"mobile"}, []int64{user.Uid}, "", []string{InviterRewardValueStr})
		if err != nil {
			logc.Infof(ctx, fmt.Sprintf("%s SendHeraldMessages failed for uid:%d, err:%v", LOG_PREFIX_INVITE, user.Uid, err))
			failedCount++
		} else {
			logc.Infof(ctx, fmt.Sprintf("%s Invite notification sent successfully for uid:%d", LOG_PREFIX_INVITE, user.Uid))
			successCount++
		}
	}

	// 输出统计信息
	logc.Infof(ctx, fmt.Sprintf("%s end, processed %d users, success:%d, failed:%d",
		LOG_PREFIX_INVITE, len(users), successCount, failedCount))

	msg = fmt.Sprintf("%s completed successfully, processed %d users", LOG_PREFIX_INVITE, len(users))
	return
}

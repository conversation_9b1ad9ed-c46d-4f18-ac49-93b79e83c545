package task

import (
	"context"

	"gateio_service_marketing_activity/internal/task/demo"
	help "gateio_service_marketing_activity/internal/task/help_get_coupon"
	"gateio_service_marketing_activity/internal/task/nft"
	"gateio_service_marketing_activity/internal/task/vip_airdrop"

	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/task/download"

	"bitbucket.org/gatebackend/go-zero/job/xxljob"
)

// TaskFunc 是离线任务的方法签名，在实现离线任务时，入参和出参要和此方法保持一致
type TaskFunc func(ctx context.Context, svcCtx *svc.ServiceContext, param *xxljob.TaskRequest) (msg string)

func wrapTask(f TaskFunc, svcCtx *svc.ServiceContext) xxljob.TaskFunc {
	return func(ctx context.Context, param *xxljob.TaskRequest) (msg string) {
		return f(ctx, svcCtx, param)
	}
}

// RegisterTasks 负责将离线任务注册到 agent 中
func RegisterTasks(server *xxljob.Server, svcCtx *svc.ServiceContext) {
	server.RegisterTask("demo", wrapTask(demo.Demo, svcCtx)) //demo测试用

	server.RegisterTask("download-task", wrapTask(download.DownloadTask, svcCtx)) // 下载活动

	server.RegisterTask("count_round_lottery_user", wrapTask(nft.HandleLotteryUserByRound, svcCtx))
	server.RegisterTask("count_all_lottery_user", wrapTask(nft.HandleLotteryUserFinial, svcCtx))
	server.RegisterTask("clear_lottery_user", wrapTask(nft.ClearLotteryUserByRound, svcCtx))
	server.RegisterTask("handle_cex_lottery_user", wrapTask(nft.HandleCexLotteryUser, svcCtx))                   // 处理中心化抽奖用户
	server.RegisterTask("handle_lottery_user_number", wrapTask(nft.HandleLotteryNumber, svcCtx))                 // 处理抽奖用户分配抽奖号码
	server.RegisterTask("clear_lottery_data_by_activity_id", wrapTask(nft.ClearLotteryDataByActivityId, svcCtx)) // 清理抽奖表按照活动ID

	server.RegisterTask("fix_status_no_sync", wrapTask(nft.FixStatusNoSync, svcCtx))                  // 修复状态未同步的数据
	server.RegisterTask("centralization_sync_status", wrapTask(nft.CentralizationSyncStatus, svcCtx)) // 中心化 - 同步铸造状态

	server.RegisterTask("help_end_time_check", wrapTask(help.HelpEndTimeCheck, svcCtx)) // 助力领券 - 剩余时间和领奖提醒
	server.RegisterTask("invite_join_again", wrapTask(help.InviteJoinAgain, svcCtx))    // 助力领券 - 剩余时间和领奖提醒
	server.RegisterTask("vip_airdrop_sync_apply_total", wrapTask(vip_airdrop.SyncApplyTotal, svcCtx)) // 刷新报名人数
}

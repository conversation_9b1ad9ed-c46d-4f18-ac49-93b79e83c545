package test

import (
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"
	"gateio_service_marketing_activity/internal/logic/inner/test"
	"gateio_service_marketing_activity/internal/svc"
)

// 测试接口
func GetHelloInnerHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := test.NewGetHelloInnerLogic(r.Context(), svcCtx)
		resp, err := l.GetHelloInner()
		if err != nil {
			// code-data 响应格式
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
		} else {
			// code-data 响应格式
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}

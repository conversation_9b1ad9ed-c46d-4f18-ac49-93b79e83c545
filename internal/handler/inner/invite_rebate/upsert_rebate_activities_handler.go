package invite_rebate

import (
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"
	"bitbucket.org/gatebackend/go-zero/rest/httpx"

	"gateio_service_marketing_activity/internal/logic/inner/invite_rebate"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

// 新增邀请反佣页活动
func UpsertRebateActivitiesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpsertRebateActivitiesReq
		if err := httpx.Parse(r, &req); err != nil {
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
			return
		}

		l := invite_rebate.NewUpsertRebateActivitiesLogic(r.Context(), svcCtx)
		resp, err := l.UpsertRebateActivities(&req)
		if err != nil {
			// code-data 响应格式
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
		} else {
			// code-data 响应格式
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}

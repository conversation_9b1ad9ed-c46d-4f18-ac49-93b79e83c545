package handler

import (
	"net/http"
	"time"

	innerinvite_rebate "gateio_service_marketing_activity/internal/handler/inner/invite_rebate"
	innertest "gateio_service_marketing_activity/internal/handler/inner/test"
	outeractivity "gateio_service_marketing_activity/internal/handler/outer/activity"
	"gateio_service_marketing_activity/internal/svc"

	"bitbucket.org/gatebackend/go-zero/rest"
)

func NewRegisterInnerHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				// 测试接口
				Method:  http.MethodGet,
				Path:    "/get_hello",
				Handler: innertest.GetHelloInnerHandler(serverCtx),
			},
			{
				// 测试接口
				Method:  http.MethodGet,
				Path:    "/get_hello2",
				Handler: innertest.GetHello2InnerHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/inner/v1/marketing-activity"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthAllowMiddleware},
			[]rest.Route{
				{
					// web3后端 NFT铸造验证
					Method:  http.MethodPost,
					Path:    "/webVerifyNftMint",
					Handler: outeractivity.WebVerifyNftMintHandler(serverCtx),
				},
				{
					// 接收web3 nft铸造结果
					Method:  http.MethodPost,
					Path:    "/receiveMintResult",
					Handler: outeractivity.ReceiveMintResultHandler(serverCtx),
				},
				{
					// 接收任务完成通知
					Method:  http.MethodPost,
					Path:    "/receiveSubTaskStatus",
					Handler: outeractivity.ReceiveSubTaskStatusHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/inner/v1/marketing-activity"),
		rest.WithTimeout(5000*time.Millisecond),
	)
	server.AddRoutes(
		[]rest.Route{
			{
				// 获取邀请反佣页活动列表
				Method:  http.MethodGet,
				Path:    "/getRebateActivities",
				Handler: innerinvite_rebate.GetRebateActivitiesHandler(serverCtx),
			},
			{
				// 新增邀请反佣页活动
				Method:  http.MethodPost,
				Path:    "/upsertRebateActivities",
				Handler: innerinvite_rebate.UpsertRebateActivitiesHandler(serverCtx),
			},
			{
				// 启用邀请反佣页活动
				Method:  http.MethodPost,
				Path:    "/onlineRebateActivities",
				Handler: innerinvite_rebate.OnlineRebateActivitiesHandler(serverCtx),
			},
			{
				// 禁用邀请反佣页活动
				Method:  http.MethodPost,
				Path:    "/offlineRebateActivities",
				Handler: innerinvite_rebate.OfflineRebateActivitiesHandler(serverCtx),
			},
			{
				// 删除邀请反佣页活动
				Method:  http.MethodPost,
				Path:    "/deleteRebateActivities",
				Handler: innerinvite_rebate.DeleteRebateActivitiesHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/inner/v1/marketing-activity"),
		rest.WithTimeout(5000*time.Millisecond),
	)

	//server.AddRoutes(
	//	rest.WithMiddlewares(
	//		[]rest.Middleware{serverCtx.AuthAllowMiddleware},
	//		[]rest.Route{
	//			{
	//				// 获取用户需要展示对应人群任务的接口
	//				Method:  http.MethodPost,
	//				Path:    "/getUserNeedShowTask",
	//				Handler: outercontract_transform.GetUserNeedShowTaskHandler(serverCtx),
	//			},
	//		}...,
	//	),
	//	rest.WithPrefix("/api/web/v1/marketing-activity"),
	//)

	//server.AddRoutes(
	//	rest.WithMiddlewares(
	//		[]rest.Middleware{serverCtx.AuthAllowMiddleware},
	//		[]rest.Route{
	//			{
	//				// 获取用户需要展示对应人群任务的接口
	//				Method:  http.MethodPost,
	//				Path:    "/userToDoContractTransformTask",
	//				Handler: outercontract_transform.UserToDoContractTransformTaskHandler(serverCtx),
	//			},
	//		}...,
	//	),
	//	rest.WithPrefix("/api/web/v1/marketing-activity"),
	//)
	//
	//server.AddRoutes(
	//	rest.WithMiddlewares(
	//		[]rest.Middleware{serverCtx.AuthAllowMiddleware},
	//		[]rest.Route{
	//			{
	//				// 签到接口
	//				Method:  http.MethodPost,
	//				Path:    "/actCheckIn",
	//				Handler: outeractivity.ActivityCheckInHandler(serverCtx),
	//			},
	//		}...,
	//	),
	//	rest.WithPrefix("/api/web/v1/marketing-activity"),
	//	rest.WithTimeout(5000*time.Millisecond),
	//)
}

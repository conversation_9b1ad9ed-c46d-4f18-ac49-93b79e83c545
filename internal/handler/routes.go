// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"
	"time"

	outerhelp_get_coupons "gateio_service_marketing_activity/internal/handler/outer/help_get_coupons"
	outerinvite_rebate "gateio_service_marketing_activity/internal/handler/outer/invite_rebate"

	sub_routers "gateio_service_marketing_activity/internal/handler/sub_routers"

	innertest "gateio_service_marketing_activity/internal/handler/inner/test"
	outeractivity "gateio_service_marketing_activity/internal/handler/outer/activity"
	outerbrand_partner "gateio_service_marketing_activity/internal/handler/outer/brand_partner"
	outershare "gateio_service_marketing_activity/internal/handler/outer/share"
	outertest "gateio_service_marketing_activity/internal/handler/outer/test"
	"gateio_service_marketing_activity/internal/svc"

	"bitbucket.org/gatebackend/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				// 测试接口
				Method:  http.MethodGet,
				Path:    "/get_hello",
				Handler: innertest.GetHelloInnerHandler(serverCtx),
			},
			{
				// 测试接口
				Method:  http.MethodGet,
				Path:    "/get_hello2",
				Handler: innertest.GetHello2InnerHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/inner/v1/marketing-activity"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthAllowMiddleware},
			[]rest.Route{
				{
					// web3后端 NFT铸造验证
					Method:  http.MethodPost,
					Path:    "/webVerifyNftMint",
					Handler: outeractivity.WebVerifyNftMintHandler(serverCtx),
				},
				{
					// 接收web3 nft铸造结果
					Method:  http.MethodPost,
					Path:    "/receiveMintResult",
					Handler: outeractivity.ReceiveMintResultHandler(serverCtx),
				},
				{
					// 接收任务完成通知
					Method:  http.MethodPost,
					Path:    "/receiveSubTaskStatus",
					Handler: outeractivity.ReceiveSubTaskStatusHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/inner/v1/marketing-activity"),
		rest.WithTimeout(5000*time.Millisecond),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 测试接口
				Method:  http.MethodGet,
				Path:    "/get_hello",
				Handler: outertest.GetHelloOuterHandler(serverCtx),
			},
			{
				// 测试接口
				Method:  http.MethodGet,
				Path:    "/get_hello2",
				Handler: outertest.GetHello2OuterHandler(serverCtx),
			},
			{
				// 测试接口
				Method:  http.MethodGet,
				Path:    "/test",
				Handler: outertest.GetTestOuterHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/web/v1/marketing-activity"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthAllowMiddleware},
			[]rest.Route{
				{
					// 签到接口
					Method:  http.MethodPost,
					Path:    "/actCheckIn",
					Handler: outeractivity.ActivityCheckInHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/web/v1/marketing-activity"),
		rest.WithTimeout(5000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthAllowMiddleware},
			[]rest.Route{
				{
					// 测试方法
					Method:  http.MethodPost,
					Path:    "/nftTest",
					Handler: outeractivity.NftTestHandler(serverCtx),
				},
				{
					// 页面初始化接口
					Method:  http.MethodPost,
					Path:    "/pageInit",
					Handler: outeractivity.PageInitHandler(serverCtx),
				},
				{
					// 活动列表查询
					Method:  http.MethodGet,
					Path:    "/nftActivityList",
					Handler: outeractivity.NftActivityListHandler(serverCtx),
				},
				{
					// 任务列表查询
					Method:  http.MethodGet,
					Path:    "/nftTaskList",
					Handler: outeractivity.NftTaskListHandler(serverCtx),
				},
				{
					// web3客户端Nft验证及初始化
					Method:  http.MethodPost,
					Path:    "/clientVerifyNftMint",
					Handler: outeractivity.ClientVerifyNftMintHandler(serverCtx),
				},
				{
					// 加密数据
					Method:  http.MethodPost,
					Path:    "/encryptData",
					Handler: outeractivity.EncryptDataHandler(serverCtx),
				},
				{
					// 助力弹框详情
					Method:  http.MethodPost,
					Path:    "/shareHelpDetail",
					Handler: outeractivity.ShareHelpDetailHandler(serverCtx),
				},
				{
					// 助力
					Method:  http.MethodPost,
					Path:    "/shareHelpRecord",
					Handler: outeractivity.ShareHelpRecordHandler(serverCtx),
				},
				{
					// NFT列表查询
					Method:  http.MethodGet,
					Path:    "/nftDetailList",
					Handler: outeractivity.NftDetailListHandler(serverCtx),
				},
				{
					// 铸造nft（中心化）
					Method:  http.MethodPost,
					Path:    "/casting",
					Handler: outeractivity.CastingHandler(serverCtx),
				},
				{
					// NFT铸造结果轮询接口
					Method:  http.MethodGet,
					Path:    "/rollNftMintResult",
					Handler: outeractivity.RollNftMintResultHandler(serverCtx),
				},
				{
					// nft收集进度查询
					Method:  http.MethodGet,
					Path:    "/collectionNftProgress",
					Handler: outeractivity.CollectionNftProgressHandler(serverCtx),
				},
				{
					// 完成任务触发
					Method:  http.MethodPost,
					Path:    "/taskTrigger",
					Handler: outeractivity.TaskTriggerHandler(serverCtx),
				},
				{
					// web3客户端防女巫验证
					Method:  http.MethodPost,
					Path:    "/captchaVerify",
					Handler: outeractivity.CaptchaVerifyHandler(serverCtx),
				},
				{
					// 惊喜加时-任务列表
					Method:  http.MethodGet,
					Path:    "/surpriseExtensionTaskList",
					Handler: outeractivity.SurpriseExtensionTaskListHandler(serverCtx),
				},
				{
					// 用户抽奖信息
					Method:  http.MethodGet,
					Path:    "/userNFTPrizeInfo",
					Handler: outeractivity.UserNftPrizeInfoHandler(serverCtx),
				},
				{
					// 所有中奖记录查询
					Method:  http.MethodGet,
					Path:    "/nftActivityPrizeInfo",
					Handler: outeractivity.NftActivityPrizeInfoHandler(serverCtx),
				},
				{
					// 兑奖
					Method:  http.MethodPost,
					Path:    "/claimPrize",
					Handler: outeractivity.ClaimPrizeHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/web/v1/marketing-activity"),
		rest.WithTimeout(5000*time.Millisecond),
	)
	sub_routers.DowanLoadRouters(server, serverCtx)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthAllowMiddleware},
			[]rest.Route{
				{
					// 获取配置
					Method:  http.MethodGet,
					Path:    "/share_config",
					Handler: outershare.ShareConfigHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/web/v1/marketing-activity"),
		rest.WithTimeout(5000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthAllowMiddleware},
			[]rest.Route{
				{
					// 获取配置
					Method:  http.MethodGet,
					Path:    "/share_config",
					Handler: outershare.ShareConfigHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/app/v1/marketing-activity"),
		rest.WithTimeout(5000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthAllowMiddleware},
			[]rest.Route{
				{
					// 详细展示（照片墙、视频、赛事）
					Method:  http.MethodGet,
					Path:    "/brand_partner/detail",
					Handler: outerbrand_partner.BrandPartnerDetailHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/web/v1/marketing-activity"),
		rest.WithTimeout(5000*time.Millisecond),
	)
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthAllowMiddleware},
			[]rest.Route{
				{
					// 获取助力领券奖励额
					Method:  http.MethodGet,
					Path:    "/helpGetReward",
					Handler: outerhelp_get_coupons.HelpGetRewardHandler(serverCtx),
				},
				{
					// 页面初始化接口
					Method:  http.MethodGet,
					Path:    "/helpPageInit",
					Handler: outerhelp_get_coupons.HelpPageInitHandler(serverCtx),
				},
				{
					// 用户报名
					Method:  http.MethodPost,
					Path:    "/helpSignup",
					Handler: outerhelp_get_coupons.HelpSignupHandler(serverCtx),
				},
				{
					// 邀请人领奖
					Method:  http.MethodPost,
					Path:    "/helpGetCoupon",
					Handler: outerhelp_get_coupons.HelpGetCouponHandler(serverCtx),
				},
				{
					// 查询被邀请人记录
					Method:  http.MethodGet,
					Path:    "/helpInviteeRecord",
					Handler: outerhelp_get_coupons.HelpInviteeRecordHandler(serverCtx),
				},
				{
					// 查询邀请人记录
					Method:  http.MethodGet,
					Path:    "/helpInviterRecord",
					Handler: outerhelp_get_coupons.HelpInviterRecordHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/web/v1/marketing-activity"),
		rest.WithTimeout(5000*time.Millisecond),
	)
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthAllowMiddleware},
			[]rest.Route{
				{
					// 分页查询活动列表
					Method:  http.MethodGet,
					Path:    "/getRebeteActivity",
					Handler: outerinvite_rebate.GetRebeteActivityHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/web/v1/marketing-activity"),
		rest.WithTimeout(5000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthAllowMiddleware},
			[]rest.Route{
				{
					// VipAirdrop报名
					Method:  http.MethodPost,
					Path:    "/vipAirdropApply",
					Handler: outeractivity.VipAirdropApplyHandler(serverCtx),
				},
				{
					// VipAirdrop任务列表
					Method:  http.MethodGet,
					Path:    "/vipAirdropTaskList",
					Handler: outeractivity.VipAirdropTaskListHandler(serverCtx),
				},
				{
					// VipAirdrop赛季信息
					Method:  http.MethodGet,
					Path:    "/vipAirdropSeasonInfo",
					Handler: outeractivity.VipAirdropSeasonInfoHandler(serverCtx),
				},
				{
					// VipAirdrop用户信息
					Method:  http.MethodGet,
					Path:    "/vipAirdropUserInfo",
					Handler: outeractivity.VipAirdropUserInfoHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/web/v1/marketing-activity"),
		rest.WithTimeout(5000*time.Millisecond),
	)
}

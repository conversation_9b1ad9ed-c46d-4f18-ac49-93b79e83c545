package activity

import (
	"net/http"

	"bitbucket.org/gateio/gateio-lib-base-go/response"

	"gateio_service_marketing_activity/internal/logic/outer/activity"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"

	"bitbucket.org/gatebackend/go-zero/rest/httpx"
)

func PageInitHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.PageInitReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := activity.NewPageInitLogic(r.Context(), svcCtx)
		resp, err := l.PageInit(&req, r)
		response.NewApiV1(r.Context(), w).Render(resp, err)
	}
}

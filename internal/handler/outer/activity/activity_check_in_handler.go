package activity

import (
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"bitbucket.org/gateio/gateio-lib-common-go/language"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"gateio_service_marketing_activity/internal/consts"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/rest/httpx"

	"gateio_service_marketing_activity/internal/logic/outer/activity"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

// 签到接口
func ActivityCheckInHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ActivityCheckInRequest
		if err := httpx.Parse(r, &req); err != nil {
			respCode := consts.ErrInvalidParam
			errMsg := language.GetLangDescByKey(consts.ErrorCodeKeyMap[respCode], requestools.GetUserLanguage(r))
			response.NewApiV1(r.Context(), w).Render(nil, errors.New(respCode, errMsg))
			return
		}

		l := activity.NewActivityCheckInLogic(r.Context(), svcCtx)
		resp, err := l.ActivityCheckIn(&req, r)

		response.NewApiV1(r.Context(), w).Render(resp, err)
	}
}

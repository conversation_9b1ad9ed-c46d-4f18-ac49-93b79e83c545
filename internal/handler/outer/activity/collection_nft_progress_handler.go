package activity

import (
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"net/http"
	"strconv"

	"gateio_service_marketing_activity/internal/logic/outer/activity"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

// nft收集进度查询
func CollectionNftProgressHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		r.ParseForm()
		nftTypeStr := r.FormValue("nft_type")
		uIDStr := r.FormValue("uid")
		walletAddress := r.FormValue("wallet_address")
		nftType, _ := strconv.Atoi(nftTypeStr)
		uid, _ := strconv.ParseInt(uIDStr, 10, 64)
		req := types.CollectionNftProgressReq{
			UID:           uid,
			WalletAddress: walletAddress,
			NftType:       nftType,
		}
		l := activity.NewCollectionNftProgressLogic(r.Context(), svcCtx)
		resp, err := l.CollectionNftProgress(&req, r)
		response.NewApiV1(r.Context(), w).Render(resp, err)
	}
}

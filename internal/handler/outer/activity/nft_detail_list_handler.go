package activity

import (
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"net/http"
	"strconv"

	"gateio_service_marketing_activity/internal/logic/outer/activity"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

// NFT列表查询
func NftDetailListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		r.ParseForm()
		activityIDStr := r.FormValue("activity_id")
		uIDStr := r.FormValue("uid")
		walletAddress := r.FormValue("wallet_address")
		isCentralizationStr := r.FormValue("is_centralization")
		activityID, _ := strconv.ParseInt(activityIDStr, 10, 64)
		uid, _ := strconv.ParseInt(uIDStr, 10, 64)
		isCentralization, _ := strconv.Atoi(isCentralizationStr)
		// 处理请求参数
		req := types.ActivityNftListReq{
			IsCentralization: isCentralization,
			ActivityID:       activityID,
			UID:              uid,
			WalletAddress:    walletAddress,
		}
		l := activity.NewNftDetailListLogic(r.Context(), svcCtx)
		resp, err := l.NftDetailList(&req, r)
		response.NewApiV1(r.Context(), w).Render(resp, err)
	}
}

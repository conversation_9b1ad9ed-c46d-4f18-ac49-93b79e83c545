package activity

import (
	"net/http"

	"bitbucket.org/gatebackend/go-zero/rest/httpx"
	"bitbucket.org/gateio/gateio-lib-base-go/response"

	"gateio_service_marketing_activity/internal/logic/outer/activity"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

// 活动列表查询
func NftActivityListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ActivityListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := activity.NewNftActivityListLogic(r.Context(), svcCtx)
		resp, err := l.NftActivityList(&req, r)
		response.NewApiV1(r.Context(), w).<PERSON><PERSON>(resp, err)
	}
}

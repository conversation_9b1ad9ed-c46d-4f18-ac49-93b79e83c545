package activity

import (
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"
	"bitbucket.org/gatebackend/go-zero/rest/httpx"

	"gateio_service_marketing_activity/internal/logic/outer/activity"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

// web3客户端防女巫验证
func CaptchaVerifyHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CaptchaVerifyReq
		if err := httpx.Parse(r, &req); err != nil {
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
			return
		}

		l := activity.NewCaptchaVerifyLogic(r.Context(), svcCtx)
		resp, err := l.CaptchaVerify(&req, r)
		response.NewApiV1(r.Context(), w).Ren<PERSON>(resp, err)
	}
}

package activity

import (
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"net/http"
	"strconv"

	"gateio_service_marketing_activity/internal/logic/outer/activity"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

// NFT铸造结果轮询接口
func RollNftMintResultHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		r.ParseForm()
		activityIDStr := r.FormValue("activity_id")
		uIDStr := r.FormValue("uid")
		walletAddress := r.FormValue("wallet_address")
		parentNftIdStr := r.FormValue("parent_nft_id")
		nftUserSubTaskIdStr := r.FormValue("nft_user_sub_task_id")
		operationIdStr := r.FormValue("operation_id")
		activityID, _ := strconv.ParseInt(activityIDStr, 10, 64)
		uid, _ := strconv.ParseInt(uIDStr, 10, 64)
		parentNftId, _ := strconv.ParseInt(parentNftIdStr, 10, 64)
		nftUserSubTaskId, _ := strconv.ParseInt(nftUserSubTaskIdStr, 10, 64)
		operationId, _ := strconv.ParseInt(operationIdStr, 10, 64)
		// 处理请求参数
		req := types.RollNftMintResultReq{
			ActivityID:       activityID,
			UID:              uid,
			WalletAddress:    walletAddress,
			ParentNftID:      parentNftId,
			NftUserSubTaskId: nftUserSubTaskId,
			OperationID:      operationId,
		}
		l := activity.NewRollNftMintResultLogic(r.Context(), svcCtx)
		resp, err := l.RollNftMintResult(&req, r)
		response.NewApiV1(r.Context(), w).Render(resp, err)
	}
}

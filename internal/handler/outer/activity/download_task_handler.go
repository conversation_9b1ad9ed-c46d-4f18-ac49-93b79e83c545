package activity

import (
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gatebackend/go-zero/rest/httpx"
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"bitbucket.org/gateio/gateio-lib-common-go/language"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/logic/outer/download_activity"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"net/http"
)

func GetDownloadActivityInfoHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := download_activity.NewDownloadActivity(r.Context(), svcCtx)
		resp, err := l.GetActivityInfo(r)
		response.NewApiV1(r.Context(), w).Render(resp, err)

	}
}

func ParticipateDownloadActivityHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := download_activity.NewDownloadActivity(r.Context(), svcCtx)
		var req types.ParticipateRequest
		if err := httpx.Parse(r, &req); err != nil {
			respCode := consts.ErrInvalidParam
			errMsg := language.GetLangDescByKey(consts.ErrorCodeKeyMap[respCode], requestools.GetUserLanguage(r))
			response.NewApiV1(r.Context(), w).Render(nil, errors.New(respCode, errMsg))
			return
		}
		resp, err := l.Participate(r, req.DownloadActivityCh)
		response.NewApiV1(r.Context(), w).Render(resp, err)

	}
}

func GetDownloadActivityUserNumsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetUserNumsRequest
		if err := httpx.Parse(r, &req); err != nil {
			respCode := consts.ErrInvalidParam
			errMsg := language.GetLangDescByKey(consts.ErrorCodeKeyMap[respCode], requestools.GetUserLanguage(r))
			response.NewApiV1(r.Context(), w).Render(nil, errors.New(respCode, errMsg))
			return
		}

		l := download_activity.NewDownloadActivity(r.Context(), svcCtx)

		resp, err := l.GetUserNums(&req, r)

		response.NewApiV1(r.Context(), w).Render(resp, err)
	}
}

func CanAutoParticipateDownloadActivityHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {

		l := download_activity.NewDownloadActivity(r.Context(), svcCtx)

		resp, err := l.CanAutoParticipate(r)

		response.NewApiV1(r.Context(), w).Render(resp, err)
	}
}

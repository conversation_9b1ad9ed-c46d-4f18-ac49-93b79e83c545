package activity

import (
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"
	"bitbucket.org/gatebackend/go-zero/rest/httpx"

	"gateio_service_marketing_activity/internal/logic/outer/activity"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

// web3客户端防女巫、IP验证
func ClientVerifyNftMintHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ClientVerifyNftMintReq
		if err := httpx.Parse(r, &req); err != nil {
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
			return
		}

		l := activity.NewClientVerifyNftMintLogic(r.Context(), svcCtx)
		resp, err := l.ClientVerifyNftMint(&req, r)
		response.NewApiV1(r.Context(), w).<PERSON><PERSON>(resp, err)
	}
}

package test

import (
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"
	"gateio_service_marketing_activity/internal/logic/outer/test"
	"gateio_service_marketing_activity/internal/svc"
)

// 测试接口
func GetHelloOuterHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := test.NewGetHelloOuterLogic(r.Context(), svcCtx)
		resp, err := l.GetHelloOuter()
		if err != nil {
			// code-data 响应格式
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
		} else {
			// code-data 响应格式
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}

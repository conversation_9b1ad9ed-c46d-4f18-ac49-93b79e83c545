package test

import (
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"
	"gateio_service_marketing_activity/internal/logic/outer/test"
	"gateio_service_marketing_activity/internal/svc"
)

// 测试接口
func GetHello2OuterHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := test.NewGetHello2OuterLogic(r.Context(), svcCtx)
		resp, err := l.GetHello2Outer()
		if err != nil {
			// code-data 响应格式
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
		} else {
			// code-data 响应格式
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}

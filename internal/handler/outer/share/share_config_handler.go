package share

import (
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"net/http"
	"strings"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"
	"gateio_service_marketing_activity/internal/logic/outer/share"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

// 获取配置
func ShareConfigHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		source := r.URL.Query().Get("source")
		if len(source) == 0 {
			xhttp.JsonErrResponseCtx(r.Context(), w, errors.New(-1, "invalid source"))
			return
		}
		req := types.GetShareConfigRequest{}

		path := r.URL.Path
		language := requestools.GetUserLanguage(r)
		req.Source = source
		req.Language = language
		req.Platform = share.PlatformAPP
		if strings.Contains(path, "/web/") {
			req.Platform = share.PlatformWEB
		}

		l := share.NewShareConfigLogic(r.Context(), svcCtx)
		resp, err := l.ShareConfig(&req)
		if err != nil {
			// code-data 响应格式
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
		} else {
			// code-data 响应格式
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}

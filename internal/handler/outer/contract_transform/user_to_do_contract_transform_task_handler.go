package contract_transform

/* import (
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/language"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"gateio_service_marketing_activity/internal/consts"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/rest/httpx"
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"gateio_service_marketing_activity/internal/logic/outer/contract_transform"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

func UserToDoContractTransformTaskHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UserToDoContractTransformTaskRequest
		if err := httpx.Parse(r, &req); err != nil {
			response.NewApiV1(r.Context(), w).Render(nil, err)
			return
		}

		// 从请求体当中获取用户信息
		userInfo := requestools.GetUserInfo(r)

		// 用户未登录
		if userInfo == nil {
			respCode := consts.ErrUserLogin
			errMsg := language.GetLangDescByKey(consts.ErrorCodeKeyMap[respCode], requestools.GetUserLanguage(r))
			response.NewApiV1(r.Context(), w).Render(nil, errors.New(respCode, errMsg))
			return
		}

		l := contract_transform.NewUserToDoContractTransformTaskLogic(r.Context(), svcCtx)
		resp, err := l.UserToDoContractTransformTask(&req, userInfo)

		response.NewApiV1(r.Context(), w).Render(resp, err)
	}
}
*/

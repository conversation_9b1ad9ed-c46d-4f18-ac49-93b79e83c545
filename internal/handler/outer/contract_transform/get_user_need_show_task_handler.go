package contract_transform

/* import (
	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"bitbucket.org/gateio/gateio-lib-common-go/language"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/logic/outer/contract_transform"
	"gateio_service_marketing_activity/internal/svc"
	"net/http"
)

func GetUserNeedShowTaskHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 从请求体当中获取用户信息
		userInfo := requestools.GetUserInfo(r)

		// 用户未登录
		if userInfo == nil {
			respCode := consts.ErrUserLogin
			errMsg := language.GetLangDescByKey(consts.ErrorCodeKeyMap[respCode], requestools.GetUserLanguage(r))
			response.NewApiV1(r.Context(), w).Render(nil, errors.New(respCode, errMsg))
			return
		}

		l := contract_transform.NewGetUserNeedShowTaskLogic(r.Context(), svcCtx)
		resp, err := l.GetUserNeedShowTask(r, userInfo)

		response.NewApiV1(r.Context(), w).Render(resp, err)
	}
}
*/

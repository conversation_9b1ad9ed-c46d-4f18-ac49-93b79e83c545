package help_get_coupons

import (
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"

	"gateio_service_marketing_activity/internal/logic/outer/help_get_coupons"
	"gateio_service_marketing_activity/internal/svc"
)

// 页面初始化接口
func HelpPageInitHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := help_get_coupons.NewHelpPageInitLogic(r.Context(), svcCtx)
		resp, err := l.HelpPageInit(r)
		if err != nil {
			// code-data 响应格式
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
		} else {
			// code-data 响应格式
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}

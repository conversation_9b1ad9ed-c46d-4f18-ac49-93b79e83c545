package help_get_coupons

import (
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"
	"bitbucket.org/gatebackend/go-zero/rest/httpx"

	"gateio_service_marketing_activity/internal/logic/outer/help_get_coupons"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

// 查询被邀请人记录
func HelpInviteeRecordHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.HelpInviteeRecordReq
		if err := httpx.Parse(r, &req); err != nil {
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
			return
		}

		l := help_get_coupons.NewHelpInviteeRecordLogic(r.Context(), svcCtx)
		resp, err := l.HelpInviteeRecord(r, &req)
		if err != nil {
			// code-data 响应格式
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
		} else {
			// code-data 响应格式
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}

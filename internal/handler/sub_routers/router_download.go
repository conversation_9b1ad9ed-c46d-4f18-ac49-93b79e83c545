package sub_routers

import (
	"bitbucket.org/gatebackend/go-zero/rest"
	outeractivity "gateio_service_marketing_activity/internal/handler/outer/activity"
	"gateio_service_marketing_activity/internal/svc"
	"net/http"
	"time"
)

/*
*
下载专项也专用路由配置
*/
func DowanLoadRouters(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthAllowMiddleware},
			[]rest.Route{
				{
					// 获取下载活动信息的接口
					Method:  http.MethodGet,
					Path:    "/get-download-activity-info",
					Handler: outeractivity.GetDownloadActivityInfoHandler(serverCtx),
				},
				{
					// 参与下载活动的接口
					Method:  http.MethodPost,
					Path:    "/particiapte-download-activity",
					Handler: outeractivity.ParticipateDownloadActivityHandler(serverCtx),
				},
				{
					// 获取某一期下载活动参与人数的接口
					Method:  http.MethodGet,
					Path:    "/get-download-activity-user-num",
					Handler: outeractivity.GetDownloadActivityUserNumsHandler(serverCtx),
				},
				{
					// 判断用户是否能自动参与
					Method:  http.MethodGet,
					Path:    "/can-auto-participate",
					Handler: outeractivity.CanAutoParticipateDownloadActivityHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/web/v1/marketing-activity"),
		rest.WithTimeout(5000*time.Millisecond),
	)
}

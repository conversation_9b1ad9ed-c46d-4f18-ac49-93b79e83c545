package config

import (
	"fmt"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/stores/redis"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"

	"bitbucket.org/gatebackend/go-zero/core/conf"
	"bitbucket.org/gatebackend/go-zero/core/service"
	"bitbucket.org/gatebackend/go-zero/rest"
	"bitbucket.org/gatebackend/gorm-zero/gormc/config/mysql"

	"bitbucket.org/gateio/gateio-lib-base-go/gconfig"
	"bitbucket.org/gateio/gateio-lib-base-go/gconfig/config"
)

type Config struct {
	// Nacos 配置, 由 lib-base-go/gconfig 来管理, 项目启动时会从环境变量中加载
	// nacos 的连接信息, 之后从 nacos server 中拉取配置并加载到 Config 对象上.
	Nacos            gconfig.GateNacosConfig `json:",optional,omitempty"`
	Basic            service.ServiceConf     `json:",optional,omitempty"`
	OuterServiceConf OuterServiceConf        `json:",optional,omitempty"`
	InnerServiceConf InnerServiceConf        `json:",optional,omitempty"`

	// 使用的 Mysql, Redis 等配置
	MysqlMarketingActivities mysql.Conf `json:",optional,omitempty"` // MarketingActivities数据库配置
	MysqlCompetitionCenter   mysql.Conf `json:",optional,omitempty"` // 赛事数据库配置
	MysqlActivities          mysql.Conf `json:",optional,omitempty"` // Activities数据库配置
	// 增加wbe3任务进度查询访问地址
	CallWebTaskServiceUrl string `json:",optional,omitempty"`
	// 增加wbe3用户收集nft的信息访问地址
	CallWebWalletNftServiceUrl string `json:",optional,omitempty"`

	MysqlNft         mysql.Conf `json:",optional,omitempty"` // nft mysql database
	MysqlFeeSettings mysql.Conf `json:",optional,omitempty"`
	// Redis配置
	Redis    redis.RedisConf `json:",optional,omitempty"`
	NftRedis redis.RedisConf `json:",optional,omitempty"`
	// XXXjob配置
	XXLJob xxljob.Conf `json:",optional,omitempty"`
	// Kafka相关配置
	KafkaConf *KafkaConf `json:",optional,omitempty"`
	// 增加风控调用连接
	RiskServiceUrl    string `json:",optional,omitempty"`
	RiskGatewayAppId  string `json:",optional,omitempty"`
	MailHeraldMailUrl string `json:",optional,omitempty"`
	// Nft mint
	NftMintConf MintConfig `json:",optional,omitempty"`
	// 现货引擎
	SpotEngine SpotEngine `json:",optional,omitempty"`
	// 上报打点Kafka相关配置
	UploadKafkaConf *KafkaConf `json:",optional,omitempty"`
}

type MintConfig struct {
	CollectionId int64  `json:",optional,default=0"`
	Token        string `json:",default=REDBULLNFT,options=REDBULLNFTTEST|REDBULLNFT"`
	OfficialUid  int64  `json:",optional,default=0"`
}

type SpotEngine struct {
	Key    string `json:"key,omitempty"`
	Secret string `json:"secret,omitempty"`
}

func (c *Config) UnmarshalJSON(src []byte) error {
	return conf.LoadFromJsonBytes(src, c)
}

type OuterServiceConf struct {
	rest.RestConf

	// 在下面添加外部服务需要使用的配置, 并删除该注释
}

type InnerServiceConf struct {
	rest.RestConf

	// 在下面添加外部服务需要使用的配置, 并删除该注释
}

type KafkaConf struct {
	Brokers       []string      `json:",omitempty"`
	FetchMaxBytes int           `json:",omitempty"`
	FetchMinBytes int           `json:",omitempty"`
	FetchMaxWait  time.Duration `json:",omitempty"`
}

const gconfigKey = "default"

func LoadConfigFromNacos(config gconfig.GateNacosConfig) (*Config, error) {
	err := gconfig.AddNacosConf(config)
	if err != nil {
		return nil, fmt.Errorf("load config from nacos: %w", err)
	}
	return GetConfig()
}

func GetConfig() (*Config, error) {
	var configContent Config
	err := gconfig.Scan(gconfigKey, &configContent)
	if err != nil {
		return nil, err
	}
	return standardize(&configContent), err
}

func GetConfigItem(key string) config.Value {
	return gconfig.Get(fmt.Sprintf("%s.%s", gconfigKey, key))
}

func standardize(config *Config) *Config {
	if len(config.OuterServiceConf.Name) == 0 {
		config.OuterServiceConf.Name = config.Basic.Name + "-outer" // for log, trace, metrics...
	}
	if len(config.InnerServiceConf.Name) == 0 {
		config.OuterServiceConf.Name = config.Basic.Name + "-inner"
	}
	if config.OuterServiceConf.Host == "" {
		config.OuterServiceConf.Host = "0.0.0.0"
	}
	if config.InnerServiceConf.Host == "" {
		config.InnerServiceConf.Host = "0.0.0.0"
	}
	if config.OuterServiceConf.Port == 0 {
		config.OuterServiceConf.Port = 3460
	}
	if config.InnerServiceConf.Port == 0 {
		config.InnerServiceConf.Port = 9580
	}
	if len(config.InnerServiceConf.Telemetry.Endpoint) > 0 {
		fmt.Println("[WARNING]telemetry 应该通过 Basic.Telemetry 来配置, 而不是 InnerServiceConf.Telemetry")
	}
	if len(config.OuterServiceConf.Telemetry.Endpoint) > 0 {
		fmt.Println("[WARNING]telemetry 应该通过 Basic.Telemetry 来配置, 而不是 OuterServiceConf.Telemetry")
	}
	config.OuterServiceConf.Telemetry.Disabled = true
	config.InnerServiceConf.Telemetry.Disabled = true
	return config
}

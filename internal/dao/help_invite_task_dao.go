package dao

import (
	"context"
	"errors"
	"time"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/help_invite_task"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
)

func (d *MarketingActivityMysqlDBUtil) FindLatestTaskByUid(ctx context.Context, uid int64) (*help_invite_task.HelpInviteTask, error) {
	resp := &help_invite_task.HelpInviteTask{}
	err := d.DB.WithContext(ctx).Model(&help_invite_task.HelpInviteTask{}).
		Where("business_type = ?", consts.BusinessTypeHelp).
		Where("is_inviter_task = ?", consts.IsInviterTask).
		Where("uid = ?", uid).
		Order("id desc").
		Take(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (d *MarketingActivityMysqlDBUtil) FindTaskByRefTaskId(ctx context.Context, refTaskId int64) ([]*help_invite_task.HelpInviteTask, error) {
	resp := []*help_invite_task.HelpInviteTask{}
	err := d.DB.WithContext(ctx).Model(&help_invite_task.HelpInviteTask{}).
		Where("business_type = ?", consts.BusinessTypeHelp).
		Where("is_inviter_task = ?", consts.IsInviteeTask).
		Where("ref_invite_task_id = ?", refTaskId).
		Find(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (d *MarketingActivityMysqlDBUtil) FindInviteeTaskByUid(ctx context.Context, uid int64) (*help_invite_task.HelpInviteTask, error) {
	resp := &help_invite_task.HelpInviteTask{}
	err := d.DB.WithContext(ctx).Model(&help_invite_task.HelpInviteTask{}).
		Where("business_type = ?", consts.BusinessTypeHelp).
		Where("uid = ?", uid).
		Where("is_inviter_task = ?", consts.IsInviteeTask).
		Take(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (d *MarketingActivityMysqlDBUtil) FindInviteTaskByTaskBusinessId(ctx context.Context, taskBusinessId string) (*help_invite_task.HelpInviteTask, error) {
	resp := &help_invite_task.HelpInviteTask{}
	err := d.DB.WithContext(ctx).Model(&help_invite_task.HelpInviteTask{}).
		Where("business_type = ?", consts.BusinessTypeHelp).
		Where("task_business_id = ?", taskBusinessId).
		Take(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (d *MarketingActivityMysqlDBUtil) FindTaskById(ctx context.Context, id int64) (*help_invite_task.HelpInviteTask, error) {
	resp := &help_invite_task.HelpInviteTask{}
	err := d.DB.WithContext(ctx).Model(&help_invite_task.HelpInviteTask{}).
		Where("id = ?", id).
		Take(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// 分页查询邀请人任务记录
func (d *MarketingActivityMysqlDBUtil) FindInviterTasksByPage(ctx context.Context, uid int64, progressStatus, page, perPage int) ([]*help_invite_task.HelpInviteTask, int64, error) {
	var (
		tasks []*help_invite_task.HelpInviteTask
		total int64
	)
	db := d.DB.WithContext(ctx).Model(&help_invite_task.HelpInviteTask{}).
		Where("business_type = ?", consts.BusinessTypeHelp).
		Where("is_inviter_task = ?", consts.IsInviterTask).
		Where("uid = ?", uid)

	if progressStatus == consts.HelpProcessing {
		db = db.Where("status = ?", consts.HelpStatusInProgress)
	} else if progressStatus == consts.HelpProcessFinish {
		db = db.Where("status != ?", consts.HelpStatusInProgress)
	}
	db.Count(&total)
	err := db.Order("created_at desc").Limit(perPage).Offset((page - 1) * perPage).Find(&tasks).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, 0, err
	}
	return tasks, total, nil
}

func (d *MarketingActivityMysqlDBUtil) InsertHelpInviteTask(ctx context.Context, task *help_invite_task.HelpInviteTask) (int64, error) {
	err := d.DB.WithContext(ctx).Model(&help_invite_task.HelpInviteTask{}).Create(task).Error
	if err != nil {
		return 0, err
	}
	return task.Id, err
}

func (d *MarketingActivityMysqlDBUtil) UpdateHelpInviteTask(ctx context.Context, data *help_invite_task.HelpInviteTask) (bool, error) {
	if data.Id == 0 {
		return false, errors.New("invalid params")
	}
	err := d.DB.WithContext(ctx).Model(&help_invite_task.HelpInviteTask{}).
		Where("id = ?", data.Id).Updates(data).Error
	if err != nil {
		return false, err
	}
	return true, nil
}

func (d *MarketingActivityMysqlDBUtil) FindInviterTaskByUidAndCreateTime(ctx context.Context, uid int64, createdAt time.Time) ([]*help_invite_task.HelpInviteTask, error) {
	resp := []*help_invite_task.HelpInviteTask{}
	err := d.DB.WithContext(ctx).Model(&help_invite_task.HelpInviteTask{}).
		Where("business_type = ?", consts.BusinessTypeHelp).
		Where("is_inviter_task = ?", consts.IsInviterTask).
		Where("uid = ?", uid).
		Where("created_at > ?", createdAt).
		Order("id asc").
		Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

func (d *MarketingActivityMysqlDBUtil) FindInviterTaskByCreateTime(ctx context.Context, createdAt time.Time) ([]*help_invite_task.HelpInviteTask, error) {
	resp := []*help_invite_task.HelpInviteTask{}
	err := d.DB.WithContext(ctx).Model(&help_invite_task.HelpInviteTask{}).
		Where("business_type = ?", consts.BusinessTypeHelp).
		Where("is_inviter_task = ?", consts.IsInviterTask).
		Where("status = ?", consts.HelpStatusInProgress).
		Where("created_at > ?", createdAt).
		Order("id asc").
		Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

func (d *MarketingActivityMysqlDBUtil) FindFinishInviterTaskByFinishTime(ctx context.Context, remindTime1 time.Time, remindTime2 time.Time) ([]*help_invite_task.HelpInviteTask, error) {
	resp := []*help_invite_task.HelpInviteTask{}
	err := d.DB.WithContext(ctx).Model(&help_invite_task.HelpInviteTask{}).
		Where("business_type = ?", consts.BusinessTypeHelp).
		Where("is_inviter_task = ?", consts.IsInviterTask).
		Where("status = ?", consts.HelpStatusSuccess).
		Where("finish_time < ?", remindTime1.Unix()).
		Where("finish_time > ?", remindTime2.Unix()).
		Order("id asc").
		Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

package dao

import (
	"context"
	"errors"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/invite_rebate_activity"
	"gateio_service_marketing_activity/internal/types"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
)

func (d *MarketingActivityMysqlDBUtil) QueryRebateActivitiesByParam(ctx context.Context, req *types.GetRebateActivitiesReq) ([]*invite_rebate_activity.InviteRebateActivity, error) {
	resp := []*invite_rebate_activity.InviteRebateActivity{}

	query := d.DB.WithContext(ctx).Model(&invite_rebate_activity.InviteRebateActivity{})
	if req.ConfigStatus != 0 {
		query = query.Where("config_status = ?", req.ConfigStatus)
	}
	if req.Weight != 0 {
		query = query.Where("weight = ?", req.Weight)
	}
	if req.ActivityId != 0 {
		query = query.Where("activity_id = ?", req.ActivityId)
	}
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	if errors.Is(err, gormc.ErrNotFound) {
		return resp, nil
	}
	return resp, nil
}

func (d *MarketingActivityMysqlDBUtil) QueryRebateActivitiesForUser(ctx context.Context) ([]*invite_rebate_activity.InviteRebateActivity, error) {
	resp := []*invite_rebate_activity.InviteRebateActivity{}

	query := d.DB.WithContext(ctx).Model(&invite_rebate_activity.InviteRebateActivity{}).
		Where("config_status = ?", consts.InviteRebateOnline)
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	if errors.Is(err, gormc.ErrNotFound) {
		return resp, nil
	}
	return resp, nil
}

func (d *MarketingActivityMysqlDBUtil) InsertRebateActivities(ctx context.Context, data *invite_rebate_activity.InviteRebateActivity) (int64, error) {
	err := d.DB.WithContext(ctx).Model(&invite_rebate_activity.InviteRebateActivity{}).Create(data).Error
	return data.Id, err
}

func (d *MarketingActivityMysqlDBUtil) UpdateRebateActivities(ctx context.Context, data *invite_rebate_activity.InviteRebateActivity) (bool, error) {
	if data.Id == 0 {
		return false, errors.New("invalid params")
	}
	err := d.DB.WithContext(ctx).Model(&invite_rebate_activity.InviteRebateActivity{}).
		Where("id = ?", data.Id).Updates(data).Error
	if err != nil {
		return false, err
	}
	return true, nil
}

func (d *MarketingActivityMysqlDBUtil) DeleteRebateActivity(ctx context.Context, id int64) error {
	err := d.DB.WithContext(ctx).Delete(&invite_rebate_activity.InviteRebateActivity{}, id).Error
	return err
}

func (d *MarketingActivityMysqlDBUtil) FindRebateActivitiesByWeight(ctx context.Context, weight int) (bool, error) {
	resp := &invite_rebate_activity.InviteRebateActivity{}
	err := d.DB.WithContext(ctx).Model(&invite_rebate_activity.InviteRebateActivity{}).
		Where("weight = ?", weight).
		Where("config_status = ?", consts.InviteRebateOnline).
		Take(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return false, err
	}
	if errors.Is(err, gormc.ErrNotFound) {
		return false, nil
	}
	return true, nil
}

func (d *MarketingActivityMysqlDBUtil) FindRebateActivitiesByActivityId(ctx context.Context, activityId int64) (bool, error) {
	resp := &invite_rebate_activity.InviteRebateActivity{}
	err := d.DB.WithContext(ctx).Model(&invite_rebate_activity.InviteRebateActivity{}).
		Where("activity_id = ?", activityId).
		Take(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return false, err
	}
	if errors.Is(err, gormc.ErrNotFound) {
		return false, nil
	}
	return true, nil
}

func (d *MarketingActivityMysqlDBUtil) FindRebateActivitiesById(ctx context.Context, id int64) (*invite_rebate_activity.InviteRebateActivity, error) {
	resp := &invite_rebate_activity.InviteRebateActivity{}
	err := d.DB.WithContext(ctx).Model(&invite_rebate_activity.InviteRebateActivity{}).
		Where("id = ?", id).
		Take(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

package dao

import "gorm.io/gorm"

// 封装数据库连接
type MarketingActivityMysqlDBUtil struct {
	DB *gorm.DB
}

var marketingActivityMysqlDBUtil *MarketingActivityMysqlDBUtil

func NewMarketingActivityMysqlDBUtil(db *gorm.DB) *MarketingActivityMysqlDBUtil {
	if marketingActivityMysqlDBUtil == nil {
		marketingActivityMysqlDBUtil = &MarketingActivityMysqlDBUtil{
			DB: db,
		}
	}
	return marketingActivityMysqlDBUtil
}

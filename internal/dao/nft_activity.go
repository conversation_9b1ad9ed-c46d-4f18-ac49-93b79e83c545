package dao

import (
	"bitbucket.org/gateio/gateio-lib-base-go/environment"
	"context"
	"errors"
	"gateio_service_marketing_activity/internal/models/nft_activity"
	"gateio_service_marketing_activity/internal/models/nft_activity_pre"
	"time"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
	"gorm.io/gorm"
)

/**
 * 活动相关查询
 * @注意：end_time目前不包含惊喜加时阶段，需要考虑这种情况
 */

/**
 * 惊喜加时阶段，时间覆盖整体结束时间
 */
func (d *MarketingActivityMysqlDBUtil) handleSurpriseExtension(activity *nft_activity.NftActivity) {
	if activity == nil || activity.Id == 0 {
		return
	}

	// 没有惊喜加时阶段，则跳过
	if activity.SurpriseExtensionStartTime.IsZero() || activity.SurpriseExtensionEndTime.IsZero() {
		return
	}

	// 如果本身结束时间，没有结束，则不做处理
	if activity.EndTime.After(time.Now()) {
		return
	}

	// 惊喜加时阶段开始，则覆盖end_time
	if activity.SurpriseExtensionStartTime.Before(time.Now()) && activity.SurpriseExtensionEndTime.After(time.Now()) {
		activity.EndTime = activity.SurpriseExtensionEndTime
	}
}

/*
 * 查询活动列表
 * @param baseType 基础类型
 * @param activityType 活动类型
 * @param isCentralized 是否中心化（true:中心化，false:非中心化）
 * @return 活动列表
 * @return 错误
 */
func (d *MarketingActivityMysqlDBUtil) GetNftActivityList(ctx context.Context, baseType, activityType int, isCentralized bool) ([]*nft_activity.NftActivity, error) {
	resp := []*nft_activity.NftActivity{}
	var query *gorm.DB
	if environment.IsPre() {
		query = d.DB.WithContext(ctx).Model(nft_activity_pre.NftActivityPre{})
	} else {
		query = d.DB.WithContext(ctx).Model(nft_activity.NftActivity{})
	}

	// 根据条件动态构建查询
	if baseType > 0 {
		query = query.Where("`base_type` = ?", baseType)
	}
	if activityType > 0 {
		query = query.Where("`activity_type` = ?", activityType)
	}
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	// 如果有惊喜加时阶段，并且开始，则覆盖整体结束时间
	if isCentralized {
		for _, activity := range resp {
			d.handleSurpriseExtension(activity)
		}
	}

	return resp, nil
}

// 查询指定活动
func (d *MarketingActivityMysqlDBUtil) GetNftActivity(ctx context.Context, activityID int64, isCentralized bool) (*nft_activity.NftActivity, error) {
	resp := nft_activity.NftActivity{}
	var query *gorm.DB
	if environment.IsPre() {
		query = d.DB.WithContext(ctx).Model(nft_activity_pre.NftActivityPre{})
	} else {
		query = d.DB.WithContext(ctx).Model(nft_activity.NftActivity{})
	}
	// 根据条件动态构建查询
	if activityID > 0 {
		query = query.Where("`activity_id` = ?", activityID)
	}
	err := query.First(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}

	// 如果有惊喜加时阶段，并且开始，则覆盖整体结束时间
	if isCentralized {
		d.handleSurpriseExtension(&resp)
	}

	return &resp, nil
}

/**
 * 获取正在进行的活动
 */
func (d *MarketingActivityMysqlDBUtil) GetRunningActivitie(ctx context.Context, baseType, activityType int, isCentralized bool) (*nft_activity.NftActivity, error) {
	var runningActivity *nft_activity.NftActivity
	var query *gorm.DB
	if environment.IsPre() {
		query = d.DB.WithContext(ctx).Model(nft_activity_pre.NftActivityPre{})
	} else {
		query = d.DB.WithContext(ctx).Model(nft_activity.NftActivity{})
	}

	// 根据条件动态构建查询
	if baseType > 0 {
		query = query.Where("`base_type` = ?", baseType)
	}
	if activityType > 0 {
		query = query.Where("`activity_type` = ?", activityType)
	}

	query.Where("start_time < ? AND end_time > ?", time.Now(), time.Now())
	err := query.Order("id desc").First(&runningActivity).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}

	// 查不到数据时，上面返回空结构体
	if err == gorm.ErrRecordNotFound {
		runningActivity = nil
	}

	// 如果有惊喜加时阶段，并且开始，则覆盖整体结束时间
	if isCentralized {
		// 如果查询结果为空，则查询加时阶段是否有活动
		if runningActivity == nil {
			tmpRunningActivity, err := d.GetRunningSurpriseExtensionTask(ctx, baseType, activityType)
			if err != nil {
				return nil, err
			}
			runningActivity = tmpRunningActivity
		}

		// 尝试使用加时阶段结束时间，覆盖整体结束时间
		d.handleSurpriseExtension(runningActivity)
	}

	return runningActivity, nil
}

/**
 * 获取正在进行的惊喜加时任务
 */
func (d *MarketingActivityMysqlDBUtil) GetRunningSurpriseExtensionTask(ctx context.Context, baseType, activityType int) (*nft_activity.NftActivity, error) {
	var runningActivity nft_activity.NftActivity
	var query *gorm.DB
	if environment.IsPre() {
		query = d.DB.WithContext(ctx).Model(nft_activity_pre.NftActivityPre{})
	} else {
		query = d.DB.WithContext(ctx).Model(nft_activity.NftActivity{})
	}

	// 根据条件动态构建查询
	if baseType > 0 {
		query = query.Where("`base_type` = ?", baseType)
	}
	if activityType > 0 {
		query = query.Where("`activity_type` = ?", activityType)
	}

	query.Where("surprise_extension_start_time < ? AND surprise_extension_end_time > ?", time.Now(), time.Now())
	err := query.Order("id desc").First(&runningActivity).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &runningActivity, nil
}

// GetLotteryActivity 获取正在抽奖中的活动列表
func (d *MarketingActivityMysqlDBUtil) GetLotteryActivity(ctx context.Context) ([]*nft_activity.NftActivity, error) {
	var runningActivity []*nft_activity.NftActivity
	var query *gorm.DB
	if environment.IsPre() {
		query = d.DB.WithContext(ctx).Model(nft_activity_pre.NftActivityPre{})
	} else {
		query = d.DB.WithContext(ctx).Model(nft_activity.NftActivity{})
	}

	query.Where("start_time < ? AND draw_end_time > ?", time.Now(), time.Now())
	err := query.Order("id desc").Scan(&runningActivity).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	return runningActivity, nil
}

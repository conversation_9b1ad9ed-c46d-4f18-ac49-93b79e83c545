package dao

import (
	"context"
	"fmt"
	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"

	"github.com/spf13/cast"
)

const (
	CACHE_NOT_KYC = -1
	CACHE_KYC     = 1
)

/**
 * 判断用户完成kyc情况
 */
func CheckUserIsKyc(ctx context.Context, svcCtx *svc.ServiceContext, userId int64) (bool, error) {
	// 尝试从缓存中获取用户KYC状态
	rdsKey := fmt.Sprintf("user:kyc:%d", userId)
	cachedKycStatus, _ := svcCtx.Redis.Get(rdsKey)
	status := cast.ToInt(cachedKycStatus)

	if status == CACHE_KYC {
		return true, nil
	} else if status == CACHE_NOT_KYC {
		return false, nil
	}

	userKycInfo, err := service.NewUserCenterCall(ctx).GetUserKYCInfo(userId)
	if err != nil {
		return false, nil
	}

	// 获取KYC状态并设置到缓存中
	isKyc := consts.IsKyc(userKycInfo.Verified)
	svcCtx.Redis.Setex(rdsKey, cast.ToString(map[bool]int{true: CACHE_KYC, false: CACHE_NOT_KYC}[isKyc]), 60)
	return isKyc, nil
}

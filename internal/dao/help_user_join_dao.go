package dao

import (
	"context"
	"errors"
	"time"

	"gateio_service_marketing_activity/internal/models/help_user_join"
)

func (d *MarketingActivityMysqlDBUtil) InsertHelpUserJoin(ctx context.Context, task *help_user_join.HelpUserJoin) (int64, error) {
	err := d.DB.WithContext(ctx).Model(&help_user_join.HelpUserJoin{}).Create(task).Error
	return task.Id, err
}

func (d *MarketingActivityMysqlDBUtil) UpdateHelpUserJoin(ctx context.Context, data *help_user_join.HelpUserJoin) (bool, error) {
	if data.Id == 0 {
		return false, errors.New("invalid params")
	}
	err := d.DB.WithContext(ctx).Model(&help_user_join.HelpUserJoin{}).
		Where("id = ?", data.Id).Updates(data).Error
	if err != nil {
		return false, err
	}
	return true, nil
}

func (d *MarketingActivityMysqlDBUtil) FindHelpUserJoinByUid(ctx context.Context, uid int64) (*help_user_join.HelpUserJoin, error) {
	var resp help_user_join.HelpUserJoin
	err := d.DB.WithContext(ctx).Model(&help_user_join.HelpUserJoin{}).
		Where("uid = ?", uid).
		Take(&resp).Error
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

func (d *MarketingActivityMysqlDBUtil) FindHelpUserJoinByTimeRange(ctx context.Context, startTime, endTime time.Time) ([]*help_user_join.HelpUserJoin, error) {
	var resp []*help_user_join.HelpUserJoin
	err := d.DB.WithContext(ctx).Model(&help_user_join.HelpUserJoin{}).
		Where("updated_at >= ? AND updated_at <= ?", startTime, endTime).
		Order("updated_at desc").
		Find(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

package dao

import (
	"context"
	"gateio_service_marketing_activity/internal/models/contract_transform_model"
)

// 根据条件查询合约定向转化活动-用户任务状态记录
func (d *MarketingActivityMysqlDBUtil) GetWelfareConfigByName(ctx context.Context, userId int64, taskId int64) (*contract_transform_model.ContractTransformUserTask, error) {
	resp := &contract_transform_model.ContractTransformUserTask{}
	query := d.DB.WithContext(ctx).Model(contract_transform_model.ContractTransformUserTask{})
	// 根据条件动态构建查询
	if userId > 0 {
		query = query.Where("`user_id` = ?", userId)
	}
	if taskId > 0 {
		query = query.Where("`task_id` = ?", taskId)
	}
	err := query.First(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

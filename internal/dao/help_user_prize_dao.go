package dao

import (
	"context"
	"errors"

	"gateio_service_marketing_activity/internal/models/help_user_prize"
)

func (d *MarketingActivityMysqlDBUtil) FindUserPrizeByTaskId(ctx context.Context, taskId int64, uid int64) (*help_user_prize.HelpUserPrize, error) {
	resp := &help_user_prize.HelpUserPrize{}
	err := d.DB.WithContext(ctx).Model(&help_user_prize.HelpUserPrize{}).
		Where("task_id = ?", taskId).
		Where("uid = ?", uid).
		Take(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (d *MarketingActivityMysqlDBUtil) InsertHelpUserPrize(ctx context.Context, prize *help_user_prize.HelpUserPrize) (int64, error) {
	err := d.DB.WithContext(ctx).Model(&help_user_prize.HelpUserPrize{}).Create(prize).Error
	return prize.Id, err
}

func (d *MarketingActivityMysqlDBUtil) UpdateHelpUserPrize(ctx context.Context, data *help_user_prize.HelpUserPrize) (bool, error) {
	if data.Id == 0 {
		return false, errors.New("invalid params")
	}
	err := d.DB.WithContext(ctx).Model(&help_user_prize.HelpUserPrize{}).
		Where("id = ?", data.Id).Updates(data).Error
	if err != nil {
		return false, err
	}
	return true, nil
}

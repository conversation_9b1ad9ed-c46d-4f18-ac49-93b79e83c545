package dao

import (
	"context"
	"time"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/cc_competitions"
)

func (d *CompetitionsMysqlDBUtil) QueryCompetitionByParam(ctx context.Context, ids []int64, competitionName string, status int) ([]*cc_competitions.CcCompetitions, error) {
	resp := []*cc_competitions.CcCompetitions{}
	query := d.DB.WithContext(ctx).Model(&cc_competitions.CcCompetitions{})
	query = query.Where("id IN ?", ids)
	if competitionName != "" {
		query = query.Where("competition_name LIKE ?", "%"+competitionName+"%")
	}
	current := time.Now().Unix()
	if status != 0 {
		if status == consts.InviteRebateUnstart {
			query = query.Where("start_at > ?", current)
		} else if status == consts.InviteRebateProcessing {
			query = query.Where("start_at <= ? AND end_at >= ?", current, current)
		} else if status == consts.InviteRebateEnd {
			query = query.Where("end_at < ?", current)
		}
	}
	query = query.Where("status = ?", 1)
	err := query.Find(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (d *CompetitionsMysqlDBUtil) QueryCompetitionToC(ctx context.Context, ids []int64) ([]*cc_competitions.CcCompetitions, error) {
	resp := []*cc_competitions.CcCompetitions{}
	query := d.DB.WithContext(ctx).Model(&cc_competitions.CcCompetitions{}).
		Where("id IN ?", ids).
		Where("status = ?", 1)

	err := query.Find(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (d *CompetitionsMysqlDBUtil) GetCompetitionById(ctx context.Context, id int64) (*cc_competitions.CcCompetitions, error) {
	resp := &cc_competitions.CcCompetitions{}
	err := d.DB.WithContext(ctx).Model(&cc_competitions.CcCompetitions{}).
		Where("id = ?", id).
		Take(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

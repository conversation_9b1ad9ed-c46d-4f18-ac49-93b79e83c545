package dao

import (
	"context"
	"errors"
	"gateio_service_marketing_activity/internal/models/nft_adjust_log"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
)

func (d *MarketingActivityMysqlDBUtil) GetUserAdjust(ctx context.Context, uid int) (*nft_adjust_log.NftAdjustLog, error) {
	var resp nft_adjust_log.NftAdjustLog
	err := d.DB.WithContext(ctx).Model(&nft_adjust_log.NftAdjustLog{}).Where("activity_type = 1").
		Where("`uid` = ?", uid).Take(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return &resp, nil

}

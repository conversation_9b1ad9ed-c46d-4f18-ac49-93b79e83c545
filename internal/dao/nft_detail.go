package dao

import (
	"bitbucket.org/gateio/gateio-lib-base-go/environment"
	"context"
	"errors"
	"gateio_service_marketing_activity/internal/models/nft_activity_detail"
	"gateio_service_marketing_activity/internal/models/nft_detail"
	"gorm.io/gorm"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
)

/**
 * NFT详情相关查询
 */

// 查询NFT列表  isHighGrade 是否查询高级nft 1:是
func (d *MarketingActivityMysqlDBUtil) GetNftList(ctx context.Context, activityId, parentNftId int64, nftType int, isHighGrade int) ([]*nft_detail.NftDetail, error) {
	resp := []*nft_detail.NftDetail{}

	var query *gorm.DB
	if environment.IsPre() {
		query = d.DB.WithContext(ctx).Model(nft_activity_detail.NftDetailPre{})
	} else {
		query = d.DB.WithContext(ctx).Model(nft_detail.NftDetail{})
	}
	// 根据条件动态构建查询
	if activityId > 0 {
		query = query.Where("`activity_id` = ?", activityId)
	}
	if parentNftId > 0 {
		query = query.Where("`parent_nft_id` = ?", parentNftId)
	}
	if nftType > 0 {
		query = query.Where("`nft_type` = ?", nftType)
	}
	if isHighGrade == 1 {
		query = query.Where("`nft_type` = ? or `nft_type` = ?", 2, 3)
	}
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// 查询NFT列表 根据活动ID和nftId列表
func (d *MarketingActivityMysqlDBUtil) GetNftListByNftIds(ctx context.Context, activityId int64, nftIds []int64) ([]*nft_detail.NftDetail, error) {
	resp := []*nft_detail.NftDetail{}
	var query *gorm.DB
	if environment.IsPre() {
		query = d.DB.WithContext(ctx).Model(nft_activity_detail.NftDetailPre{})
	} else {
		query = d.DB.WithContext(ctx).Model(nft_detail.NftDetail{})
	}
	// 根据条件动态构建查询
	query = query.Where("`activity_id` = ?", activityId)
	query = query.Where("`nft_id` IN ?", nftIds)
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// 查询单条NFT详情
func (d *MarketingActivityMysqlDBUtil) GetNftDetailById(ctx context.Context, activityId int64, nftId int64) (*nft_detail.NftDetail, error) {
	resp := &nft_detail.NftDetail{}
	var query *gorm.DB
	if environment.IsPre() {
		query = d.DB.WithContext(ctx).Model(nft_activity_detail.NftDetailPre{})
	} else {
		query = d.DB.WithContext(ctx).Model(nft_detail.NftDetail{})
	}
	query = query.Where("`activity_id` = ? and `nft_id` = ?", activityId, nftId)
	err := query.First(resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// 查询NFT列表 根据活动ID和parentNftId列表
func (d *MarketingActivityMysqlDBUtil) GetNftListByParentNftIds(ctx context.Context, activityId int64, parentNftIds []int64, nftType int64) ([]*nft_detail.NftDetail, error) {
	resp := []*nft_detail.NftDetail{}
	var query *gorm.DB
	if environment.IsPre() {
		query = d.DB.WithContext(ctx).Model(nft_activity_detail.NftDetailPre{})
	} else {
		query = d.DB.WithContext(ctx).Model(nft_detail.NftDetail{})
	}

	// 根据条件动态构建查询
	if activityId > 0 {
		query = query.Where("`activity_id` = ?", activityId)
	}

	if len(parentNftIds) > 0 {
		query = query.Where("`parent_nft_id` IN ?", parentNftIds)
	}

	if nftType > 0 {
		query = query.Where("`nft_type` = ?", nftType)
	}

	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// GetListByNftIds 查询nftId列表
func (d *MarketingActivityMysqlDBUtil) GetListByNftIds(ctx context.Context, nftIds []int64) ([]*nft_detail.NftDetail, error) {
	var resp []*nft_detail.NftDetail
	var query *gorm.DB
	if environment.IsPre() {
		query = d.DB.WithContext(ctx).Model(nft_activity_detail.NftDetailPre{})
	} else {
		query = d.DB.WithContext(ctx).Model(nft_detail.NftDetail{})
	}
	query = query.Where("`nft_id` IN ?", nftIds)
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// GetListByNftTypeIds 根据类型查询nftId列表
func (d *MarketingActivityMysqlDBUtil) GetListByNftTypeIds(ctx context.Context, typeIds []int64) ([]*nft_detail.NftDetail, error) {
	var resp []*nft_detail.NftDetail
	var query *gorm.DB
	if environment.IsPre() {
		query = d.DB.WithContext(ctx).Model(nft_activity_detail.NftDetailPre{})
	} else {
		query = d.DB.WithContext(ctx).Model(nft_detail.NftDetail{})
	}
	query = query.Where("`nft_type` IN ?", typeIds)
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

package dao

import (
	"context"
	"encoding/json"
	"errors"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/mm_white_list"
	"gateio_service_marketing_activity/internal/svc"

	"bitbucket.org/gatebackend/go-zero/core/logc"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
)

const (
	cacheExpireSeconds = 600 // 缓存过期时间：10分钟s
)

// GetMmUidAry 获取市商的用户列表
func (d *FeeSettingMysqlDBUtil) GetMmUidAry(svcCtx *svc.ServiceContext, ctx context.Context) ([]int64, error) {
	// 1. 尝试从缓存获取
	cacheKey := consts.FeeSeettingMmWhiteListKey
	cacheData, err := svcCtx.Redis.Get(cacheKey)
	if err != nil {
		logc.Errorf(ctx, "GetMmUidAry: get cache failed, key=%s, err=%v", cacheKey, err)
	}

	// 2. 缓存命中，直接返回
	if cacheData != "" {
		var uidList []int64
		if err := json.Unmarshal([]byte(cacheData), &uidList); err != nil {
			logc.Errorf(ctx, "GetMmUidAry: unmarshal cache data failed, err=%v", err)
		} else {
			return uidList, nil
		}
	}

	// 3. 缓存未命中，从数据库获取
	mmWhiteList := make([]*mm_white_list.MmWhiteListNew, 0)
	err = d.DB.WithContext(ctx).
		Model(&mm_white_list.MmWhiteListNew{}).
		Select("uid").
		Group("uid").
		Find(&mmWhiteList).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		logc.Errorf(ctx, "GetMmUidAry: query database failed, err=%v", err)
		return nil, err
	}

	// 4. 转换数据格式
	uidList := make([]int64, len(mmWhiteList))
	for i, mm := range mmWhiteList {
		uidList[i] = mm.Uid
	}

	// 5. 写入缓存
	if valByte, err := json.Marshal(uidList); err != nil {
		logc.Errorf(ctx, "GetMmUidAry: marshal data failed, err=%v", err)
	} else {
		if err := svcCtx.Redis.Setex(cacheKey, string(valByte), cacheExpireSeconds); err != nil {
			logc.Errorf(ctx, "GetMmUidAry: set cache failed, key=%s, err=%v", cacheKey, err)
		}
	}

	return uidList, nil
}

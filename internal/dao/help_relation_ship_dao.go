package dao

import (
	"context"
	"errors"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/models/help_relation_ship"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
)

func (d *MarketingActivityMysqlDBUtil) GetUserInfo(ctx context.Context, uid int64) (*help_relation_ship.HelpRelationShip, error) {
	resp := &help_relation_ship.HelpRelationShip{}
	err := d.DB.WithContext(ctx).Model(&help_relation_ship.HelpRelationShip{}).
		Where("business_type = ?", consts.BusinessTypeHelp).
		Where("uid = ?", uid).
		Take(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

func (d *MarketingActivityMysqlDBUtil) InsertHelpRelationShip(ctx context.Context, relation *help_relation_ship.HelpRelationShip) (int64, error) {
	err := d.DB.WithContext(ctx).Model(&help_relation_ship.HelpRelationShip{}).Create(relation).Error
	return relation.Id, err
}

// 分页查询被邀请人记录
func (d *MarketingActivityMysqlDBUtil) FindInviteeRecordsByRefUid(ctx context.Context, uid int64, page, perPage int) ([]*help_relation_ship.HelpRelationShip, int64, error) {
	var (
		list  []*help_relation_ship.HelpRelationShip
		total int64
	)
	db := d.DB.WithContext(ctx).Model(&help_relation_ship.HelpRelationShip{}).
		Where("business_type = ?", consts.BusinessTypeHelp).
		Where("ref_uid = ?", uid)

	db.Count(&total)
	err := db.Order("register_time desc").Limit(perPage).Offset((page - 1) * perPage).Find(&list).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, 0, err
	}
	return list, total, nil
}

package dao

import (
	"context"
	"errors"
	model_nft "gateio_service_marketing_activity/internal/models/nft"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
)

// 修改子任务完成状态
func (d *MarketingActivityMysqlDBUtil) UpdateSubTaskStatus(ctx context.Context, subTaskId int64, updateMap map[string]interface{}) error {
	err := d.DB.WithContext(ctx).Model(model_nft.NftUserSubTask{}).Where("id = ?", subTaskId).Updates(updateMap).Error
	if err != nil {
		return err
	}
	return nil
}

// 查询NFT列表
func (d *MarketingActivityMysqlDBUtil) GetNftSubTaskList(ctx context.Context, activityId, userId int64, walletAddress string) ([]*model_nft.NftUserSubTask, error) {
	resp := []*model_nft.NftUserSubTask{}
	query := d.DB.WithContext(ctx).Model(model_nft.NftUserSubTask{})
	// 根据条件动态构建查询
	if activityId > 0 {
		query = query.Where("`activity_id` = ?", activityId)
	}
	if userId > 0 {
		query = query.Where("`user_id` = ?", userId)
	}
	if walletAddress != "" {
		query = query.Where("`wallet_address` = ?", walletAddress)
	}
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

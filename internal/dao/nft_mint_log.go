package dao

import (
	"context"
	"errors"
	"gateio_service_marketing_activity/internal/models/nft_mint_log"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
)

/**
 * 铸造日志相关查询
 */

// 查询用户领取记录
func (d *MarketingActivityMysqlDBUtil) GetNftMintLogList(ctx context.Context, uid int64, walletAddress string, parentNftId int64, mintStatus int, activityId, operationId int64) ([]*nft_mint_log.NftMintLog, error) {
	resp := []*nft_mint_log.NftMintLog{}
	//将失败的铸造记录排除
	query := d.DB.WithContext(ctx).Model(nft_mint_log.NftMintLog{}).Where("mint_status < 4")
	// 根据条件动态构建查询
	if uid > 0 {
		query = query.Where("`uid` = ?", uid)
	}
	if walletAddress != "" {
		query = query.Where("`wallet_address` = ?", walletAddress)
	}
	if parentNftId > 0 {
		query = query.Where("`parent_nft_id` = ?", parentNftId)
	}
	if mintStatus > 0 {
		query = query.Where("`mint_status` = ?", mintStatus)
	}
	if activityId > 0 {
		query = query.Where("`activity_id` = ?", activityId)
	}
	if operationId > 0 {
		query = query.Where("`operation_id` = ?", operationId)
	}
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// 查询用户领取记录
func (d *MarketingActivityMysqlDBUtil) GetNftMintLogListByNftId(ctx context.Context, uid int64, walletAddress string, parentNftId int64, mintStatus int, activityId, operationId int64, nftId int64) ([]*nft_mint_log.NftMintLog, error) {
	resp := []*nft_mint_log.NftMintLog{}
	//将失败的铸造记录排除
	query := d.DB.WithContext(ctx).Model(nft_mint_log.NftMintLog{}).Where("mint_status < 4")
	// 根据条件动态构建查询
	if uid > 0 {
		query = query.Where("`uid` = ?", uid)
	}
	if walletAddress != "" {
		query = query.Where("`wallet_address` = ?", walletAddress)
	}
	if parentNftId > 0 {
		query = query.Where("`parent_nft_id` = ?", parentNftId)
	}
	if mintStatus > 0 {
		query = query.Where("`mint_status` = ?", mintStatus)
	}
	if activityId > 0 {
		query = query.Where("`activity_id` = ?", activityId)
	}
	if operationId > 0 {
		query = query.Where("`operation_id` = ?", operationId)
	}
	if nftId > 0 {
		query = query.Where("`nft_id` = ?", nftId)
	}
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// 根据操作ID查询铸造记录
func (d *MarketingActivityMysqlDBUtil) GetNftMintLogListByOperation(ctx context.Context, activityId int64, uid int64, walletAddress string, operationId int64) ([]*nft_mint_log.NftMintLog, error) {
	resp := []*nft_mint_log.NftMintLog{}
	//将失败的铸造记录排除
	query := d.DB.WithContext(ctx).Model(nft_mint_log.NftMintLog{}).
		Where("`activity_id` = ?", activityId).
		Where("`operation_id` = ?", operationId)
	if uid > 0 {
		query = query.Where("`uid` = ?", uid)
	}
	if walletAddress != "" {
		query = query.Where("`wallet_address` = ?", walletAddress)
	}
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// ClientVerifyNftMint接口专用函数 查询出用户进行中和已完成的领取记录
func (d *MarketingActivityMysqlDBUtil) ClientVerifyNftMintGetLog(ctx context.Context, walletAddress string, parentNftId, activityId int64) ([]*nft_mint_log.NftMintLog, error) {
	resp := []*nft_mint_log.NftMintLog{}
	//将失败的铸造记录排除
	query := d.DB.WithContext(ctx).Model(nft_mint_log.NftMintLog{}).Where("mint_status < 4").
		Where("mint_status > 1").Where("`wallet_address` = ?", walletAddress).Where("activity_id = ?", activityId)
	if parentNftId > 0 {
		query = query.Where("`parent_nft_id` = ?", parentNftId)
	}
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// 查询最近的一条记录
func (d *MarketingActivityMysqlDBUtil) GetLastNftMintLog(ctx context.Context, uid int64, walletAddress string, parentNftId, activityId, nftUserSubTaskId int64) (*nft_mint_log.NftMintLog, error) {
	resp := &nft_mint_log.NftMintLog{}
	//将失败的铸造记录排除
	query := d.DB.WithContext(ctx).Model(nft_mint_log.NftMintLog{}).Select([]string{"operation_id"})
	// 根据条件动态构建查询
	if uid > 0 {
		query = query.Where("`uid` = ?", uid)
	}
	if walletAddress != "" {
		query = query.Where("`wallet_address` = ?", walletAddress)
	}
	if parentNftId > 0 {
		query = query.Where("`parent_nft_id` = ?", parentNftId)
	}
	//一下两个个条件是必须使用的条件，如果改为可选条件会造成查询到的操作ID错误
	query = query.Where("`activity_id` = ?", activityId)
	query = query.Where("`nft_user_sub_task_id` = ?", nftUserSubTaskId)
	err := query.Order("id desc").First(resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

type GetNftMintLogParam struct {
	Uid              int64  // 用户ID
	WalletAddress    string // 钱包地址
	ActivityId       int64  // 活动ID
	ParentNftId      int64  // 父NFT ID
	NftUserSubTaskId int64  // 用户子任务ID
}

func (d *MarketingActivityMysqlDBUtil) GetNftMintLogByParam(ctx context.Context, params *GetNftMintLogParam) (*nft_mint_log.NftMintLog, error) {
	resp := &nft_mint_log.NftMintLog{}
	//将失败的铸造记录排除
	query := d.DB.WithContext(ctx).Model(nft_mint_log.NftMintLog{}).Select([]string{"operation_id"}).Where("mint_status < 4")

	// 根据条件动态构建查询
	if params.Uid > 0 {
		query = query.Where("`uid` = ?", params.Uid)
	}
	if len(params.WalletAddress) > 0 {
		query = query.Where("`wallet_address` = ?", params.WalletAddress)
	}
	if params.ParentNftId > 0 {
		query = query.Where("`parent_nft_id` = ?", params.ParentNftId)
	}
	if params.ActivityId > 0 {
		query = query.Where("`activity_id` = ?", params.ActivityId)
	}
	if params.NftUserSubTaskId > 0 {
		query = query.Where("`nft_user_sub_task_id` = ?", params.NftUserSubTaskId)
	}
	err := query.Order("id desc").First(resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// 批量插入记录
func (d *MarketingActivityMysqlDBUtil) InsertNftMintLogs(ctx context.Context, nftMintLogs []*nft_mint_log.NftMintLog) error {
	tx := d.DB.Begin()
	for _, nftMintLog := range nftMintLogs {
		err := tx.WithContext(ctx).Save(nftMintLog).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	return nil
}

// 根据条件修改nft领取记录状态
func (d *MarketingActivityMysqlDBUtil) UpdateNftMintLogMintStatus(ctx context.Context, walletAddress string, uid, parentNftId, operationId, activityId int64, updateMap map[string]interface{}) error {
	query := d.DB.WithContext(ctx).Model(nft_mint_log.NftMintLog{}).Where("parent_nft_id = ?", parentNftId).
		Where("operation_id = ?", operationId).Where("activity_id = ?", activityId)
	if uid > 0 {
		query = query.Where("`uid` = ?", uid)
	}
	if walletAddress != "" {
		query = query.Where("`wallet_address` = ?", walletAddress)
	}
	err := query.Updates(updateMap).Error
	if err != nil {
		return err
	}
	return nil
}

// 批量更新nft领取记录状态
func (d *MarketingActivityMysqlDBUtil) UpdateNftMintLogs(ctx context.Context, logs []*nft_mint_log.NftMintLog) error {
	tx := d.DB.Begin()
	updateMap := map[string]interface{}{}
	for _, nftMintLog := range logs {
		updateMap["nft_id"] = nftMintLog.NftId
		updateMap["token_url"] = nftMintLog.TokenUrl
		updateMap["token_id"] = nftMintLog.TokenId
		updateMap["mint_status"] = nftMintLog.MintStatus
		err := tx.WithContext(ctx).WithContext(ctx).Model(nft_mint_log.NftMintLog{}).
			Where("id = ?", nftMintLog.Id).Updates(updateMap).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	return nil
}

/**
 * 根据子任务ID，查询铸造记录
 */
func (d *MarketingActivityMysqlDBUtil) GetNftMintLogBySubTaskID(ctx context.Context, nftUserSubTaskId int64) (*nft_mint_log.NftMintLog, error) {
	resp := &nft_mint_log.NftMintLog{}
	query := d.DB.WithContext(ctx).Model(nft_mint_log.NftMintLog{}).Where("`nft_user_sub_task_id` = ?", nftUserSubTaskId)
	err := query.First(resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	if errors.Is(err, gormc.ErrNotFound) {
		return nil, nil
	}
	return resp, nil
}

/**
 * 根据任务ID、nftId获取任务记录
 */
func (d *MarketingActivityMysqlDBUtil) GetNftMintLogByUidAndNftId(ctx context.Context, userId int64, activityId int64, nftId int64) (*nft_mint_log.NftMintLog, error) {
	resp := &nft_mint_log.NftMintLog{}
	query := d.DB.WithContext(ctx).Model(nft_mint_log.NftMintLog{}).Where("`uid` = ? and `activity_id` = ? and `nft_id` = ?", userId, activityId, nftId)
	err := query.First(resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	if errors.Is(err, gormc.ErrNotFound) {
		return nil, nil
	}
	return resp, nil
}

/**
 * 更新数据
 */
func (d *MarketingActivityMysqlDBUtil) UpdateOne(ctx context.Context, id int64, selectFields []string, data *nft_mint_log.NftMintLog) error {
	if len(selectFields) == 0 {
		return errors.New("selectFields is empty")
	}
	return d.DB.WithContext(ctx).Model(nft_mint_log.NftMintLog{}).Where("id = ?", id).Updates(data).Error
}

/**
 * 根据子任务ID，批量查询铸造记录
 */
func (d *MarketingActivityMysqlDBUtil) GetNftMintLogsBySubTaskIDsMap(ctx context.Context, nftUserSubTaskIds []int64) (map[int64]*nft_mint_log.NftMintLog, error) {
	list := []*nft_mint_log.NftMintLog{}
	query := d.DB.WithContext(ctx).Model(nft_mint_log.NftMintLog{}).Where("`nft_user_sub_task_id` IN ? and `mint_status` IN ?", nftUserSubTaskIds,
		[]int64{nft_mint_log.MINT_STATUS_IN_PROGRESS, nft_mint_log.MINT_STATUS_SUCCESS})
	err := query.Find(&list).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	respMap := make(map[int64]*nft_mint_log.NftMintLog, len(list))
	for _, item := range list {
		respMap[item.NftUserSubTaskId] = item
	}
	return respMap, nil
}

package service

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall"
	"context"
	"encoding/json"
)

// UserCenterClient 继承 ServiceCallClient
type ValidationCenterClient struct {
	*servicecall.Client
}

// NewClient 创建一个新的 userCenterClient 实例
func NewValidationCenterClient() *ValidationCenterClient {
	return &ValidationCenterClient{
		Client: servicecall.NewServiceCallClient("validation"),
	}
}

// GetUserInfo 获取用户详情信息
// 接口文档地址: https://app.apifox.com/link/project/2898814/apis/api-121760705
func (uc *ValidationCenterClient) CaptchaVerify(ctx context.Context, req *CaptchaVerifyReq) (int, error) {
	resp, respErr := uc.CallInnerApiAndParseBody(ctx, "POST", "/api/inner/v1/validation/captcha_verify", map[string]interface{}{
		"captcha_type":       req.CaptchaType,
		"device_type":        req.DeviceType,
		"client_ip":          req.ClientIP,
		"scene":              req.Scene,
		"captcha_id":         req.CaptchaID,
		"captcha_output":     req.CaptchaOutput,
		"gen_time":           req.GenTime,
		"lot_number":         req.LotNumber,
		"pass_token":         req.PassToken,
		"captcha_account_id": req.CaptchaAccountID,
		"user_agent":         req.UserAgent,
	})
	if respErr != nil {
		return -1, respErr
	}
	respByte, _ := json.Marshal(resp)
	logx.Infof("CaptchaVerify resp data is:%s", string(respByte))
	if resp == nil || resp.Code != 0 {
		//防女巫未通过，打印返回信息
		respByte, _ := json.Marshal(resp)
		logx.Infof("/api/inner/v1/validation/captcha_verify return data is:%s", string(respByte))
	}
	if resp == nil {
		return -1, nil
	}

	return resp.Code, nil
}

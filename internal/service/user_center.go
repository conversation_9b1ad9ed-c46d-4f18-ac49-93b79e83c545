package service

import (
	"context"
	"strconv"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/usercenter"
)

type UserCenterCall struct {
	logx.Logger
	ctx context.Context
}

func NewUserCenterCall(ctx context.Context) *UserCenterCall {
	return &UserCenterCall{Logger: logx.WithContext(ctx), ctx: ctx}
}

func (uc *UserCenterCall) TgUserRegisterInfo(tgUID int64) (*usercenter.GetThirdPartyUserRegisterInfoResponse, error) {
	req := usercenter.GetThirdPartyUserRegisterInfoRequest{
		ThirdPartyUID: strconv.FormatInt(tgUID, 10),
		Typ:           "telegram",
	}
	registerInfo, err := usercenter.NewClient().GetThirdPartyUserRegisterInfo(uc.ctx, &req)
	if err != nil {
		uc.Logger.Errorf("get user info failed, err: %v", err)
		return nil, err
	}

	return registerInfo, nil
}

/**
 * 获取kyc详情
 */
func (uc *UserCenterCall) GetUserKYCInfo(userId int64) (*usercenter.UserKYCInfoResponse, error) {
	req := usercenter.GetUserKYCInfoRequest{
		UserID: userId,
	}
	userKycInfo, err := usercenter.NewClient().GetUserKYCInfo(uc.ctx, req)
	if err != nil {
		uc.Logger.Errorf("GetUserKYCInfo failed, err: %v", err)
		return nil, err
	}
	uc.Logger.Info("GetUserKYCInfo success, userInfo: %v", userKycInfo)
	return userKycInfo, nil
}

/**
 * 获取用户信息
 */
func (uc *UserCenterCall) GetUserInfo(userId int64) (*usercenter.UserInfoResponse, error) {
	req := usercenter.GetUserInfoRequest{
		UserID: userId,
	}
	userInfo, err := usercenter.NewClient().GetUserInfo(uc.ctx, &req)
	if err != nil {
		uc.Logger.Errorf("GetUserInfo failed, err: %v", err)
		return nil, err
	}
	uc.Logger.Info("GetUserInfo success, userInfo: %v", userInfo)
	return userInfo, nil
}

/**
 * 批量获取用户信息
 */
func (uc *UserCenterCall) GetUserMap(userIds []int64) (map[int64]*usercenter.UserDetail, error) {
	rspMap := make(map[int64]*usercenter.UserDetail)

	// 分批次处理用户 ID
	for i := 0; i < len(userIds); i += 100 {
		end := i + 100
		if end > len(userIds) {
			end = len(userIds)
		}
		req := usercenter.GetUserListRequest{
			UID: userIds[i:end],
		}
		userResp, err := usercenter.NewClient().GetUserList(uc.ctx, &req)
		if err != nil {
			uc.Logger.Errorf("GetUserList failed, err: %v", err)
			return nil, err
		}

		for _, user := range userResp.List {
			rspMap[user.UID] = &user
		}
	}
	return rspMap, nil
}

func (uc *UserCenterCall) GetAppLastTime(uid int64) (int64, error) {
	uidStr := strconv.FormatInt(uid, 10)
	userCenterClient := usercenter.NewClient()
	req, err := userCenterClient.GetAppLastReportInfos(uc.ctx, &usercenter.GetAppLastReportInfosRequest{Uids: uidStr})
	if err != nil {
		return 0, err
	}
	return req.Data[uidStr].CreateAt, nil
}

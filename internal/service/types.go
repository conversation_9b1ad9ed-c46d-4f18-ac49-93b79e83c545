package service

type CaptchaVerifyReq struct {
	CaptchaType      int    `json:"captcha_type"`
	DeviceType       int    `json:"device_type"`
	ClientIP         string `json:"client_ip"`
	Scene            string `json:"scene"`
	CaptchaID        string `json:"captcha_id"`
	CaptchaOutput    string `json:"captcha_output"`
	GenTime          int    `json:"gen_time"`
	LotNumber        string `json:"lot_number"`
	PassToken        string `json:"pass_token"`
	CaptchaAccountID string `json:"captcha_account_id"`
	UserAgent        string `json:"user_agent"`
}

type UserRoundLotteryNFT struct {
	NftGroupMap map[int64]int64 // 按parent_id分组, 每轮抽奖要6组集齐
}

type UserNFT struct {
	NormalCount    int            // 普通 NFT 数量
	NormalCountMap map[int64]bool // 某个nftID 是否已经拥有
	HasFullNormal  bool           // 是否集齐一套

	SilverCount    int
	SilverCountMap map[int64]bool // 某个nftID 是否已经拥有
	HasFullSilver  bool

	GoldCount    int
	GoldCountMap map[int64]bool // 某个版本的nftID 是否已经拥有
	HasFullGold  bool
	NftIds       []int64
}

type UserLotteryNFT struct {
	NftTypeGroupMap map[int64]map[int64]bool // 按普通，金，银分组，每个分组如果nft id的数量大于24 获得抽奖机会
}

type LotteryParams struct {
	Stime  string  `json:"stime"`
	NftIds []int64 `json:"nft_ids"`
	Etime  string  `json:"etime"`
}

type LotteryClearParams struct {
	CountDay    string `json:"count_day"`
	LotteryType int    `json:"lottery_type"`
}

type DealDexUserParams struct {
	ActivityId int64 `json:"activity_id"`
}

type DealCexUserParams struct {
	ActivityId  int64  `json:"activity_id"`
	CountDay    string `json:"count_day"`
	LotteryType int    `json:"lottery_type"`
}

type TaskCenterReward struct {
	Mark                string `json:"mark"`                   // 任务中心的任务mark
	MinNum              int64  `json:"min_num"`                // 完成任务的最小值
	RewardType          int64  `json:"reward_type"`            // 奖励类型
	RewardNum           int64  `json:"reward_num"`             // 奖励数量
	ExtraRewardType     int64  `json:"extra_reward_type"`      // 额外奖励类型(选填)
	ExtraRewardNum      int64  `json:"extra_reward_num"`       // 额外奖励数量(选填)
	RewardCouponId      int64  `json:"reward_coupon_id"`       // 正常奖励卡劵ID
	RewardSource        string `json:"reward_source"`          // 正常奖励source
	ExtraRewardCouponId int64  `json:"extra_reward_coupon_id"` // 额外奖励卡劵ID
	ExtraRewardSource   string `json:"extra_reward_source"`    // 额外奖励卡劵ID
	Status              int64  `json:"status"`                 // 奖励状态
}

type TaskCenterRewardDetail struct {
	Mark                string `json:"mark"`                   // 任务中心的任务mark
	MinNum              int64  `json:"min_num"`                // 完成任务的最小值
	RewardType          int64  `json:"reward_type"`            // 奖励类型
	RewardNum           int64  `json:"reward_num"`             // 奖励数量
	ExtraRewardType     int64  `json:"extra_reward_type"`      // 额外奖励类型(选填)
	ExtraRewardNum      int64  `json:"extra_reward_num"`       // 额外奖励数量(选填)
	RewardCouponId      int64  `json:"reward_coupon_id"`       // 正常奖励卡劵ID
	RewardSource        string `json:"reward_source"`          // 正常奖励source
	ExtraRewardCouponId int64  `json:"extra_reward_coupon_id"` // 额外奖励卡劵ID
	ExtraRewardSource   string `json:"extra_reward_source"`    // 额外奖励卡劵ID
	Status              int64  `json:"status"`                 // 奖励状态
	RewardName          string `json:"reward_name"`            // 奖励名称
	ExtraRewardName     string `json:"extra_reward_name"`      // 额外奖励名称
}

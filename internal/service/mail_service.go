package service

import (
	"context"
	"fmt"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/service_client"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
)

func SendHeraldMessages(svcCtx *svc.ServiceContext, ctx context.Context, sendId string, methods []string, users []int64, url string, templateArgs []string) (bool, error) {
	// 用户ID转字符串
	userStrs := make([]string, len(users))
	for i, u := range users {
		userStrs[i] = fmt.Sprintf("%d", u)
	}

	params := types.HeraldMessageParams{
		App:          consts.HeraldMessageApp,
		Id:           sendId,
		Methods:      methods,
		TemplateArgs: templateArgs,
		Users:        userStrs,
		Extras: map[string]string{
			"url":     url,
			"web_url": url,
		},
	}

	return service_client.SendHeraldMessages(svcCtx, &params)
}

package service

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bytes"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/svc"
	"io/ioutil"
	"net/http"
)

// GetWebTaskStatus请求的结构体
type MediaVerifyReq struct {
	WalletAddress string `json:"wallet_address"` // 钱包地址
	MediaID       string `json:"media_id"`       // 媒体 ID
	Media         string `json:"media"`          // 媒体类型，如 telegram
	MediaType     string `json:"media_type"`     // 媒体操作类型，如 join
}

// GetWebTaskStatus返回值 JSON 数据的结构体
type GetWebTaskStatusResp struct {
	Status  int    `json:"status"`  // 状态码，1 通常表示成功
	Message string `json:"message"` // 消息内容，用于描述操作结果
	Data    string `json:"data"`    // 数据字段，此处为空字符串，可根据实际情况返回具体数据
}

func GetWebTaskStatus(svcCtx *svc.ServiceContext, walletAddress, mediaID, media, mediaType string) (int, error) {
	// 构造请求体数据
	requestBody := MediaVerifyReq{
		WalletAddress: walletAddress,
		MediaID:       mediaID,
		Media:         media,
		MediaType:     mediaType,
	}
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		logx.Infof("GetWebTaskStatus json.Marshal  is err,err is:%v", err)
		return 0, err
	}

	// 创建请求
	url := fmt.Sprintf("%s/outside/media_verify", svcCtx.CallWebTaskServiceUrl)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		logx.Infof("GetWebTaskStatus http.NewRequest  is err,err is:%v", err)
		return 0, err
	}
	req.Header.Set("Content-Type", "application/json") // 设置请求头为 JSON 格式

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logx.Infof("GetWebTaskStatus client.Do is err,err is:%v", err)
		return 0, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logx.Infof("GetWebTaskStatus ioutil.ReadAll is err,err is:%v", err)
		return 0, err
	}
	dataResp := &GetWebTaskStatusResp{}
	_ = json.Unmarshal(body, dataResp)
	// 打印响应
	logx.Infof("GetWebTaskStatus bady is:%s", string(body))
	return dataResp.Status, nil
}

// 获取用户NFT资产请求参数
type ListReq struct {
	WalletAddress string `json:"wallet_address"`
}

// 获取用户NFT资产返回值
type ListResp struct {
	Status  int            `json:"status"`  // 状态码，1 通常表示成功
	Message string         `json:"message"` // 消息内容，用于描述操作结果
	Data    []*UserNftInfo `json:"data"`
}

type UserNftInfo struct {
	ChainName         string `json:"chain_name"`
	AccountAddress    string `json:"account_address"`
	ContractAddress   string `json:"contract_address"`
	ContractShortName string `json:"contract_short_name"`
	ContractName      string `json:"contract_name"`
	TokenID           string `json:"token_id"`
	TokenBalance      string `json:"token_balance"`
	TokenType         string `json:"token_type"`
	TokenName         string `json:"token_name"`
	TokenBaseURL      string `json:"token_base_url"`
	TokenURL          string `json:"token_url"`
	IsApprove         string `json:"is_approve"`
}

func GetRedBullList(svcCtx *svc.ServiceContext, walletAddress string) ([]string, error) {
	// 构造请求体数据
	requestBody := ListReq{
		WalletAddress: walletAddress,
	}
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		logx.Infof("GetRedBullList json.Marshal  is err,err is:%v", err)
		return nil, err
	}

	// 创建请求
	url := fmt.Sprintf("%s/v5/defi/nft/redbull/list", svcCtx.CallWebWalletNftServiceUrl)
	logx.Infof("url:%s", url)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		logx.Infof("GetRedBullList http.NewRequest  is err,err is:%v", err)
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json") // 设置请求头为 JSON 格式

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logx.Infof("GetRedBullList client.Do is err,err is:%v", err)
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logx.Infof("GetRedBullList ioutil.ReadAll is err,err is:%v", err)
		return nil, err
	}
	dataResp := &ListResp{}
	err = json.Unmarshal(body, dataResp)
	if err != nil {
		logx.Infof("GetRedBullList json.Unmarshal(body, dataResp) is err:%v,%s", err, string(body))
		return nil, err
	}
	// 打印响应
	logx.Infof("GetRedBullList params is:%s, bady is:%s", walletAddress, string(body))
	list := []string{}
	if dataResp != nil {
		list = make([]string, 0, len(dataResp.Data))
		for _, data := range dataResp.Data {
			if data.TokenBalance != "0" {
				list = append(list, data.TokenURL)
			}
		}
	}
	return list, nil
}

func GetRedBullNFTDetailList(svcCtx *svc.ServiceContext, walletAddress string) ([]*UserNftInfo, error) {
	// 构造请求体数据
	requestBody := ListReq{
		WalletAddress: walletAddress,
	}
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		logx.Infof("GetRedBullList json.Marshal  is err,err is:%v", err)
		return nil, err
	}

	// 创建请求
	url := fmt.Sprintf("%s/v5/defi/nft/redbull/list", svcCtx.CallWebWalletNftServiceUrl)
	logx.Infof("url:%s", url)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		logx.Infof("GetRedBullList http.NewRequest  is err,err is:%v", err)
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json") // 设置请求头为 JSON 格式

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logx.Infof("GetRedBullList client.Do is err,err is:%v", err)
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logx.Infof("GetRedBullList ioutil.ReadAll is err,err is:%v", err)
		return nil, err
	}
	dataResp := &ListResp{}
	err = json.Unmarshal(body, dataResp)
	if err != nil {
		logx.Infof("GetRedBullList json.Unmarshal(body, dataResp) is err:%v,%s", err, string(body))
		return nil, err
	}
	// 打印响应
	logx.Infof("GetRedBullList params is:%s, bady is:%s", walletAddress, string(body))
	list := []*UserNftInfo{}
	if dataResp != nil {
		list = make([]*UserNftInfo, 0, len(dataResp.Data))
		for _, data := range dataResp.Data {
			list = append(list, data)
		}
	}
	return list, nil
}

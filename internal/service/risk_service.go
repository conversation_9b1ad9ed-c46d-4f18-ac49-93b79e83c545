package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"gateio_service_marketing_activity/internal/service_client"

	"gateio_service_marketing_activity/internal/svc"

	"gateio_service_marketing_activity/internal/consts"

	"bitbucket.org/gatebackend/go-zero/core/logx"
)

// 判断用户是否是风控用户 true:命中风控 false:不是风控用户
func GetUserRiskData(svcCtx *svc.ServiceContext, ctx context.Context, uid int, constId, eventCode string, data map[string]interface{}, r *http.Request) (bool, error) {
	riskResp := &service_client.CheckRiskResponse{}
	redisVal, _ := svcCtx.Redis.Get(fmt.Sprintf(consts.HelpGetCouponRiskKey, uid, constId, eventCode))
	if redisVal != "" {
		_ = json.Unmarshal([]byte(redisVal), riskResp)
	} else {
		riskReq := &service_client.CheckRiskReq{
			AppId:     svcCtx.RiskGatewayAppId,
			EventCode: eventCode, // 仿照php代码写死
			Flag:      "",
			Data:      data,
		}
		resp, err := service_client.InnerCheckRisk(svcCtx, riskReq)
		if err != nil {
			byteVal, _ := json.Marshal(riskReq)
			logx.Errorf("GetUserRiskData InnerCheckRisk is err,params is:%s ，err is：%v", string(byteVal), err)
			return false, err
		}
		riskResp = resp
		byteVal, _ := json.Marshal(riskResp)
		cacheTtl := 30 * 60
		if eventCode == consts.RiskEventCodeInviteRisk {
			cacheTtl = 60 * 60 * 4
		}
		_ = svcCtx.Redis.Setex(fmt.Sprintf(consts.HelpGetCouponRiskKey, uid, constId), string(byteVal), cacheTtl)
	}
	// 命中风控
	if riskResp != nil && (riskResp.Result.RiskLevel == "REJECT" || riskResp.Result.RiskLevel == "REVIEW") {
		// 命中风控直接返回
		return true, nil
	}
	return false, nil
}

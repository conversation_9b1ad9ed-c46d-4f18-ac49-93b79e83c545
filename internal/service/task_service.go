package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/service_client"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"
	"gateio_service_marketing_activity/internal/utils"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
)

// 获取任务中的奖励详细信息
func GetTaskCenterRewardDetail(svcCtx *svc.ServiceContext, ctx context.Context, taskInfo task.Task) []*TaskCenterRewardDetail {
	rewards := make([]*TaskCenterRewardDetail, 0, 20)
	for _, rule := range taskInfo.RuleInfo {
		if rule.Mark == "recharge" {
			for _, detail := range rule.ConditionDetail {
				if detail.Mark == "accumulated_amount" {
					rewards = append(rewards, getReward(rule.Mark, detail.Min, 0, "", 0))
				}
			}
		} else {
			for _, condition := range rule.Conditions {
				rewardNum := int64(0)

				rewardCouponId := int64(0)
				rewardSource := ""

				p := map[string]interface{}{}
				_ = json.Unmarshal([]byte(condition.PrizeInfo), &p)

				if condition.PrizeType == consts.PrizeTypeCoupon {
					if val, ok := p["coupon_id"]; ok {
						if couponID, ok := val.(float64); ok {
							rewardCouponId = int64(couponID)
						} else {
							continue
						}
					} else {
						continue
					}
					if val, ok := p["coupon_source"]; ok {
						if source, ok := val.(string); ok {
							rewardSource = source
						} else {
							continue
						}
					} else {
						continue
					}
					if rewardCouponId == int64(0) || rewardSource == "" {
						continue
					}
					couponInfo, _ := GetCouponInfo(svcCtx, ctx, rewardCouponId, rewardSource)
					rewardNum, _ = strconv.ParseInt(couponInfo.Amount, 10, 64)
				}
				rewards = append(rewards, getReward(rule.Mark, condition.Min, rewardCouponId, rewardSource, rewardNum))
			}
		}
	}
	return rewards
}

func getReward(mark string, min int64, couponId int64, source string, rewardNum int64) *TaskCenterRewardDetail {
	return &TaskCenterRewardDetail{
		Mark:           mark,
		MinNum:         min,
		RewardCouponId: couponId,
		RewardSource:   source,
		RewardNum:      rewardNum,
	}
}

func GetEffectiveTime(item task.ListItem) string {
	// 将秒数转换为日期时间
	startTime := time.Unix(item.StartTime, 0).Format("2006-01-02 15:04:05")
	endTime := time.Unix(item.EndTime, 0).Format("2006-01-02 15:04:05")

	if item.EffectiveTimeType == 1 {
		// 固定时间范围，任务开始时间 - 结束时间
		return fmt.Sprintf("%s - %s", startTime, endTime)
	} else if item.EffectiveTimeType == 2 {
		// 领取后，天数：claim_days
		return fmt.Sprintf("领取后 %d 天", item.ClaimDays)
	}
	return ""
}

func GetTaskRewardDetail(svcCtx *svc.ServiceContext, ctx context.Context, taskId int64) (detail TaskCenterRewardDetail) {
	// todo: 加缓存
	taskCenterCall := service_client.NewTaskCenterCall(ctx)
	taskRes, err := taskCenterCall.BatchTaskAll(0, consts.HelpTaskBusinessType, "", utils.IntListToStr([]int64{taskId}))
	if err != nil {
		logx.Errorf("获取邀请人任务详情失败: %v", err)
		return TaskCenterRewardDetail{}
	}
	if len(taskRes.List) > 0 {
		task := taskRes.List[0]
		rewardDetailList := GetTaskCenterRewardDetail(svcCtx, ctx, task)
		return *rewardDetailList[0]
	}
	return TaskCenterRewardDetail{}
}

func GetTaskContionMinNum(svcCtx *svc.ServiceContext, ctx context.Context, taskId int64) (accumulated, spot, future int, couponId int64, source string) {
	// 针对taskId加缓存
	h := &types.HelpTaskDetail{}
	redisKey := consts.HelpTaskConditionKey(taskId)
	redisVal, _ := svcCtx.Redis.Get(redisKey)
	if redisVal != "" {
		_ = json.Unmarshal([]byte(redisVal), &h)
		return h.Accumulated, h.Spot, h.Future, h.CouponId, h.Source
	}

	taskCenterCall := service_client.NewTaskCenterCall(ctx)
	taskRes, err := taskCenterCall.BatchTaskAll(0, consts.HelpTaskBusinessType, "", utils.IntListToStr([]int64{taskId}))
	if err != nil {
		logx.Errorf("获取邀请人任务详情失败: %v", err)
		return
	}
	task := taskRes.List[0]
	rewardDetail := GetTaskCenterRewardDetail(svcCtx, ctx, task)
	for _, detail := range rewardDetail {
		if detail.Mark == "recharge" {
			accumulated = int(detail.MinNum)
		} else if detail.Mark == "spot_transaction" {
			spot = int(detail.MinNum)
			couponId = detail.RewardCouponId
			source = detail.RewardSource
		} else if detail.Mark == "futures_transaction" {
			future = int(detail.MinNum)
		}
	}
	// 缓存十分钟
	detail := &types.HelpTaskDetail{
		Accumulated: accumulated,
		Spot:        spot,
		Future:      future,
		CouponId:    couponId,
		Source:      source,
	}
	byteVal, err := json.Marshal(detail)
	if err == nil {
		_ = svcCtx.Redis.Setex(consts.HelpTaskConditionKey(taskId), string(byteVal), 60*10)
	}
	return accumulated, spot, future, couponId, source
}

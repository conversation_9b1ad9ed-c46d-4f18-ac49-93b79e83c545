package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/svc"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/coupon"
)

type SendCouponFormatResp struct {
	Code       int    `json:"code"`
	CouponCode int    `json:"coupon_code"`
	Message    string `json:"message"`
	IsReturn   int    `json:"is_return"`
}

// 获取卡劵信息
func GetCouponInfo(svcCtx *svc.ServiceContext, ctx context.Context, couponId int64, source string) (*coupon.CouponInfoResponse, error) {
	couponInfo := &coupon.CouponInfoResponse{}
	redisVal, _ := svcCtx.Redis.Get(fmt.Sprintf(consts.CouponInfoAll, couponId, source))
	if redisVal != "" {
		_ = json.Unmarshal([]byte(redisVal), couponInfo)
		return couponInfo, nil
	} else {
		// 获取卡劵详情
		req := coupon.CouponInfoRequest{
			CouponID: couponId,
			Source:   source,
		}
		resp, err := coupon.NewClient().CouponInfo(ctx, &req)
		if err != nil {
			logx.Errorf("service GetCouponInfo CouponInfo failed,req is:%v, err: %v", req, err)
			return couponInfo, err
		}
		if resp.ID > 0 {
			byteVal, err := json.Marshal(resp)
			if err == nil {
				// 卡劵创建之后不会改动，缓存一天
				_ = svcCtx.Redis.Setex(fmt.Sprintf(consts.CouponInfoAll, couponId, source), string(byteVal), 86400)
			}
			couponInfo = resp
		}
		return couponInfo, nil
	}
}

type CouponService struct {
	svcCtx *svc.ServiceContext
	logx.Logger
	ctx context.Context
}

func NewCouponService(svcCtx *svc.ServiceContext) *CouponService {
	return &CouponService{
		svcCtx: svcCtx,
		Logger: logx.WithContext(context.Background()),
		ctx:    context.Background(),
	}
}

// SendCouponById 封装发券请求
func (s *CouponService) SendCouponById(uid int64, couponID int64, source string, amount string, bizId int64) (*coupon.SendCouponByIdResponse, error) {
	// 实际调用 coupon service gRPC
	grpcReq := &coupon.SendCouponByIdRequest{
		UserID:    uid,
		CouponID:  couponID,
		Source:    source,
		OnlyID:    fmt.Sprintf("%d_%d", uid, time.Now().UnixMilli()), // 使用 UID 和毫秒时间戳保证唯一性
		RequestID: fmt.Sprintf("%s_%d", source, bizId),               // 使用业务源和业务ID保证幂等
		Amount:    amount,
	}

	resp, err := coupon.NewClient().SendCouponById(s.ctx, grpcReq)
	if err != nil {
		s.Logger.Errorf("SendCouponById to coupon service failed, req: %+v, err: %v", grpcReq, err)
		return nil, err
	}

	s.Logger.Infof("SendCouponById to coupon service success, req: %+v, resp: %+v", grpcReq, resp)
	return resp, nil
}

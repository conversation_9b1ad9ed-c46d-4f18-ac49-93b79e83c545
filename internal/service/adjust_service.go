package service

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall"
	"context"
	"encoding/json"
)

// AdjustCenterClient 继承 ServiceCallClient
type AdjustCenterClient struct {
	*servicecall.Client
}

// NewClient 创建一个新的 AdjustCenterClient 实例
func NewAdjustCenterClient() *AdjustCenterClient {
	return &AdjustCenterClient{
		Client: servicecall.NewServiceCallClient("adjust"),
	}
}

type SaveUserRecordReq struct {
	UserId      int64  `json:"user_id"`
	AccountId   int64  `json:"account_id"`
	ReceiveTime string `json:"receive_time"`
	Channel     string `json:"channel"`
}

// 接口文档地址: https://app.apifox.com/project/3580661/apis/api-*********
func (uc *AdjustCenterClient) SaveUserRecord(ctx context.Context, req *SaveUserRecordReq) (int, error) {
	resp, respErr := uc.CallInnerApiAndParseBody(ctx, "POST", "/api/inner/v1/adjust/save_user_record", map[string]interface{}{
		"user_id":      req.UserId,
		"activity_id":  req.AccountId,
		"receive_time": req.ReceiveTime,
		"channel":      req.Channel,
	})
	if respErr != nil {
		return -1, respErr
	}
	respByte, _ := json.Marshal(resp)
	logx.Infof("SaveUserRecord resp data is:%s", string(respByte))
	if resp == nil || resp.Code != 0 {
		//上报归因未通过，打印返回信息
		respByte, _ := json.Marshal(resp)
		logx.Infof("/api/inner/v1/adjust/save_user_record return data is:%s", string(respByte))
	}
	if resp == nil {
		return -1, nil
	}

	return resp.Code, nil
}

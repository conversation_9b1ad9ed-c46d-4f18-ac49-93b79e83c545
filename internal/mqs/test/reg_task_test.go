package test

import (
	"context"
	"gateio_service_marketing_activity/internal/mqs/kafka"
	"testing"
)

func TestRegTaskCallBack(t *testing.T) {
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(svcCtx)
	kafka.RegTaskCallBack(context.Background(), svcCtx, `{"uid": 2124321838, "ref_uid": 2124321837}`)
}

func TestNftTaskCallBack(t *testing.T) {
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(svcCtx)
	msgStr := `
{
  "business_type": 15,
  "list": [
    {
      "business_id": "1633",
      "status": 3
    }
  ]
}
	`
	kafka.NftTaskCallBack(context.Background(), svcCtx, msgStr)
}

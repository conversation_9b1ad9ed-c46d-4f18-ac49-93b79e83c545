package kafka

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/consts/kafka"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/help_invite_task"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
)

func HelpTaskRegCallBack(ctx context.Context, svcCtx *svc.ServiceContext, msgStr string) {
	logc.Infof(ctx, "助力领券 收到注册消息：%s", msgStr)

	// 解析消息内容
	taskCallBackMsg := kafka.TaskCallBackRegMsg{}
	err := json.Unmarshal([]byte(msgStr), &taskCallBackMsg)
	if err != nil {
		logc.Infof(ctx, "[Help-RegTaskCallBack]help register Unmarshal Msg err: %v", err)
		return
	}

	// 只处理助力领券消息
	if taskCallBackMsg.BusinessType != consts.HelpTaskBusinessType {
		return
	}

	item := taskCallBackMsg.List
	// 处理消息

	uid := item.BeInvitedUserID

	// 从消息里获取还是
	businessIdStr := item.BusinessID
	businessId, err := strconv.ParseInt(businessIdStr, 10, 64)
	if err != nil {
		logx.WithContext(ctx).Errorf("[Help-RegTaskCallBack] 转换 BusinessId 失败: %v", err)
		return
	}
	refTask, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).FindTaskById(ctx, businessId)
	if err != nil {
		logx.WithContext(ctx).Errorf("[Help-RegTaskCallBack] FindLatestTaskByUid failed, uid: %d, err: %v", uid, err)
		return
	}
	if refTask == nil {
		logx.WithContext(ctx).Errorf("[Help-RegTaskCallBack] refTask is nil, businessId: %d", businessId)
		return
	}

	// 创建被邀请人任务
	accumulated, _, _, _, _ := service.GetTaskContionMinNum(svcCtx, ctx, consts.GetHelpInviteeDepositTaskId())
	_, spot, _, couponId, source := service.GetTaskContionMinNum(svcCtx, ctx, consts.GetHelpInviteeTradeTaskId())

	steps := []*types.Step{
		{
			Label:              consts.TaskTypeSignup,
			Completed:          true, // 默认未完成
			TaskConditionValue: 0,    // 默认条件值
		},
		{
			TaskId:             consts.GetHelpInviteeDepositTaskId(),
			Label:              consts.TaskTypeDeposit,
			Completed:          false,       // 默认未完成
			TaskConditionValue: accumulated, // 默认条件值
		},
		{
			TaskId:             consts.GetHelpInviteeTradeTaskId(),
			Label:              consts.TaskTypeTrade,
			Completed:          false, // 默认未完成
			TaskConditionValue: spot,  // 默认条件值
		},
	}
	reqByte, _ := json.Marshal(steps)

	businessIds := []string{}
	for _, info := range item.InviteInfo {
		businessIds = append(businessIds, info.BusinessID)
	}
	taskBusinessId := strings.Join(businessIds, ",")

	helpInviteTask := &help_invite_task.HelpInviteTask{
		TaskId:          0,
		Uid:             uid,
		RefInviteTaskId: refTask.Id,
		TaskType:        consts.TaskTypeOnce,
		IsInviterTask:   consts.IsInviteeTask,
		TaskBusinessId:  taskBusinessId,
		Status:          consts.TaskStatusInitial,
		ExtraData:       sql.NullString{String: string(reqByte), Valid: true},
		ProcessNum:      0,
		Valid:           0,
		PrizeId:         couponId,
		PrizeExtraInfo:  sql.NullString{String: source, Valid: source != ""},
		PrizeType:       consts.PrizeTypeCoupon,
		BusinessType:    consts.BusinessTypeHelp,
		EndTime:         refTask.EndTime,
	}
	_, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).InsertHelpInviteTask(ctx, helpInviteTask)
	if err != nil {
		logx.WithContext(ctx).Errorf("[Help-RegTaskCallBack]inviteRecordModel.Insert err: %v", err)
		return
	}
	logx.WithContext(ctx).Infof("[Help-RegTaskCallBack] 被邀请人任务创建成功")

	userResp, err := service.NewUserCenterCall(ctx).GetUserInfo(uid)
	if err != nil || userResp == nil {
		logx.WithContext(ctx).Errorf("[Help-RegTaskCallBack] Help-GetUserInfo uid:%v, GetUserInfo err: %v", uid, err)
		return
	}

	// 发站内信 给邀请人
	_, err = service.SendHeraldMessages(svcCtx, ctx, consts.HeraldRefereeRegister, []string{"mobile"}, []int64{item.UserID}, "", []string{userResp.Nickname})
	if err != nil {
		logc.Infof(ctx, fmt.Sprintf("[Help-RegTaskCallBack] SendHeraldMessages failed, uid:%d, err:%v", uid, err))
	} else {
		logc.Infof(ctx, fmt.Sprintf("[Help-RegTaskCallBack] SendHeraldMessages success, uid:%d, err:%v", uid, err))
	}

	depositRewardValue := service.GetTaskRewardDetail(svcCtx, ctx, consts.GetHelpInviteeDepositTaskId()).RewardNum
	tradeRewardValue := service.GetTaskRewardDetail(svcCtx, ctx, consts.GetHelpInviteeTradeTaskId()).RewardNum
	inviteeRewardValue := depositRewardValue + tradeRewardValue
	inviteeRewardValueStr := strconv.FormatInt(inviteeRewardValue, 10)

	spotStr := strconv.Itoa(spot)
	accumulatedStr := strconv.Itoa(accumulated)
	// 发站内信 给被邀请人
	_, err = service.SendHeraldMessages(svcCtx, ctx, consts.HeraldTaskRemind, []string{"mobile"}, []int64{uid}, "", []string{spotStr, accumulatedStr, inviteeRewardValueStr})
	if err != nil {
		logc.Infof(ctx, fmt.Sprintf("[Help-RegTaskCallBack] SendHeraldMessages failed, uid:%d, err:%v", uid, err))
	} else {
		logc.Infof(ctx, fmt.Sprintf("[Help-RegTaskCallBack] SendHeraldMessages success, uid:%d, err:%v", uid, err))
	}
}

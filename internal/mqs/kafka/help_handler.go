package kafka

import (
	"context"
	"time"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/consts/activity"
	"gateio_service_marketing_activity/internal/svc"

	"bitbucket.org/gatebackend/go-zero/core/logx"
)

func SubmitHelpTaskRecord(ctx context.Context, svcCtx *svc.ServiceContext, uid, businessId, taskID int64) error {
	// 把业务的任务上报给任务系统
	getTaskTime := time.Now().Unix()
	kafkaProducer, err := NewKafkaProducer(svcCtx.UploadKafkaConf)
	if err != nil {
		logx.WithContext(ctx).Errorf("[RegTaskCallBack]submitTaskRecord failed, step: kafka.NewKafkaProducer, UserId : %d, BusinessId: %d, err: %v", uid, businessId, err)
		return err
	}
	defer kafkaProducer.Close()

	err = kafkaProducer.TaskRecordReceiveProducer(ctx, uid, taskID, consts.HelpTaskBusinessType, businessId, getTaskTime, 0)
	logx.Infof("[RegTaskCallBack]submitTaskRecord, step: 上报任务系统的消息：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d", uid, businessId, taskID, activity.NftTaskBusinessType)
	if err != nil {
		logx.Infof("[RegTaskCallBack]submitTaskRecord failed, step: Producer.TaskRecordReceiveProducer, UserId : %d, BusinessId: %d, err: %v", uid, businessId, err)
		return err
	}
	return nil
}

package kafka

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/risk"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/consts/kafka"
	"gateio_service_marketing_activity/internal/utils"
	"time"
)

// AdjustUserRecordMsg 给任务系统上报任务的消息体
type AdjustUserRecordMsg struct {
	UserId      int64  `json:"user_id"`
	ReceiveTime string `json:"receive_time"`
	ActivityId  int64  `json:"activity_id"`
	Channel     string `json:"channel"`
}

// AdjustUserRecordProducer 活动报名数据上报及活动信息同步
func (p *KafkaProducer) AdjustUserRecordProducer(ctx context.Context, uid, aid, receiveTime int64, channel string) error {
	// topic主题
	topicName := kafka.AdjustUserRecordTopic

	// 发送的消息内容
	msg := AdjustUserRecordMsg{
		UserId:      uid,
		ReceiveTime: time.Unix(receiveTime, 0).Format(time.DateTime),
		ActivityId:  aid,
		Channel:     channel,
	}

	// 压缩消息体
	msgByte, err := json.Marshal(msg)
	if err != nil {
		logx.Infof("AdjustUserRecordProducer failed, step: json.Marshal, Topic : %s, Msg: %s, err: %v", topicName, string(msgByte), err)
		return fmt.Errorf("kafka消息体序列化失败")
	}

	// 发送消息
	_, err = p.BaseProduceMsg(ctx, topicName, string(msgByte), "", 3)
	if err != nil {
		logx.Infof("AdjustUserRecordProducer failed, step: p.BaseProduceMsg, Topic : %s, Msg: %s, err: %v", topicName, string(msgByte), err)
		// 上报任务失败，加入到补偿队列
		msgMap := utils.StructToMap(msg)
		param := &risk.ResetKafkaMessageRequest{
			Topic:   topicName,
			Message: msgMap,
			Key:     "",
		}
		err = risk.NewClient().ResetKafkaMessage(ctx, param)
		if err != nil {
			logx.Errorf("AdjustUserRecordProducer failed, step: ResetKafkaMessage, param : %v, err: %v", param, err)
			return err
		}
	}
	return nil
}

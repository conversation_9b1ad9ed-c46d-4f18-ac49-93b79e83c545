package kafka

import (
	"context"
	"gateio_service_marketing_activity/internal/config"
	"gateio_service_marketing_activity/internal/consts/kafka"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/task/download"
	"gateio_service_marketing_activity/internal/utils"
	"sync"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"github.com/twmb/franz-go/pkg/kgo"
)

// KafkaConsumer 表示一个 Kafka 消费者
type KafkaConsumer struct {
	client *kgo.Client
	ctx    context.Context
	cancel context.CancelFunc
	wg     *sync.WaitGroup
	logx.Logger
	groupId string
	topic   []string
	svcCtx  *svc.ServiceContext
}

// StartAllConsumer 启动所有kafka消费者
func StartAllConsumer(svcCtx *svc.ServiceContext, c *config.KafkaConf) {
	// 启动合约定向转化活动--任务系统回调消费者
	var wg sync.WaitGroup

	// 定义不同消费组的 topic 映射
	consumerGroups := map[string][]string{
		kafka.ContractTransformTaskAckGroup: {
			kafka.GrowthTaskCallTopic,
		},
		kafka.DownLoadUserRegisterGroup: {
			kafka.UserCenterRegisterTopic,
		},
		kafka.VipAirdropTaskGroup: {
			kafka.GrowthTaskCallTopic,
		},
		kafka.HelpTaskRegisterGroup: {
			kafka.TaskInviteJoinTopic,
		},
		kafka.HelpTaskFinishGroup: {
			kafka.GrowthTaskCallTopic,
		},
		kafka.HelpUserRegisterGroup: {
			kafka.UserCenterRegisterTopic,
		},

		// 可以添加更多消费组和对应的 topic
		// "other_group": {
		//     "other_topic1",
		//     "other_topic2",
		// },
	}

	// 为每个消费组创建对应的消费者
	for groupId, topics := range consumerGroups {
		consumer := NewKafkaConsumer(svcCtx, c, &wg, topics, groupId)
		if consumer != nil {
			consumer.Start()
		}
	}
	go func() {
		wg.Wait()
		logx.Info("All kafka consumers have completed")
	}()
}

// NewKafkaConsumer 创建一个新的 Kafka 消费者客户端
func NewKafkaConsumer(svcCtx *svc.ServiceContext, c *config.KafkaConf,
	wg *sync.WaitGroup, topics []string, groupId string) *KafkaConsumer {
	// 获取对应环境正确的topic名称
	topicList := make([]string, 0, len(topics))
	for _, topic := range topics {
		topicTemp := utils.UnifyTopic(topic)
		topicList = append(topicList, topicTemp)
	}

	ctx, cancel := context.WithCancel(context.Background())
	client, err := kgo.NewClient(
		kgo.SeedBrokers(c.Brokers...),
		kgo.ConsumerGroup(groupId),
		kgo.ConsumeTopics(topicList...),
		kgo.FetchMaxBytes(int32(c.FetchMaxBytes)),         // 每次抓取最大字节数
		kgo.FetchMinBytes(int32(c.FetchMinBytes)),         // 每次抓取最小字节数
		kgo.FetchMaxWait(c.FetchMaxWait),                  // 抓取最大等待时间
		kgo.ConsumeResetOffset(kgo.NewOffset().AtStart()), // 从最早的偏移量开始消费
		kgo.DisableAutoCommit(),                           // 禁用自动提交偏移量
	)
	if err != nil {
		cancel()
		logx.Infof("初始化kafka消费者失败, topic:%v, groupId:%s ", topics, groupId)
		return nil
	}
	logx.Infof("初始化kafka消费者成功, topic:%v, groupId:%s ", topics, groupId)
	return &KafkaConsumer{
		client:  client,
		ctx:     ctx,
		cancel:  cancel,
		wg:      wg,
		Logger:  logx.WithContext(ctx),
		groupId: groupId,
		topic:   topicList,
		svcCtx:  svcCtx,
	}
}

func (c *KafkaConsumer) Start() {
	c.wg.Add(1)
	go c.consumeMessages()
}

// ConsumeMessages 开始消费 Kafka 消息
func (c *KafkaConsumer) consumeMessages() {
	defer func() {
		c.cancel()
		c.wg.Done()
		c.Close()
	}()

	for {
		select {
		case <-c.ctx.Done():
			c.Logger.Infof("consumeMessages kafka消费者停止")
			return
		default:
			fetches := c.client.PollFetches(c.ctx)
			if c.ctx.Err() != nil {
				logx.Infof("consumeMessages 轮训获取kafka数据失败, err:%v ", c.ctx.Err())
				break
			}

			if errs := fetches.Errors(); len(errs) > 0 {
				for _, err := range errs {
					logx.Infof("consumeMessages 从主题 %s 分区 %d 拉取消息时出错: %v", err.Topic, err.Partition, err.Err)
				}
				continue
			}

			records := fetches.Records()
			if len(records) == 0 {
				logx.Infof("consumeMessages 从消费者获取到的消息数据为空，主题：%v", c.topic)
				continue
			}

			//c.Logger.Infof("consumeMessages 消费者组 %s, 本次收到 %d 条消息", c.groupId, len(records))
			for _, record := range records {
				msgStr := string(record.Value)
				c.Logger.Infof("consumeMessages 消费者收到消息: %s, MsgNum:%d, Topic:%s, GroupId:%s ", msgStr, len(record.Value), record.Topic, c.groupId)
				//record.Topic // 从这判断Topic,然后根据不同主题，做不同方法处理
				if record.Topic == utils.UnifyTopic(kafka.GrowthTaskCallTopic) && c.groupId == kafka.ContractTransformTaskAckGroup {
					//c.Logger.Infof("consumeMessages 收到合约定向转化任务回调消息: %s, MsgNum:%d, Topic:%s, GroupId:%s", msgStr, len(record.Value), record.Topic, c.groupId)
					// 合约定向转化任务接受回调消息
					//contract_transform.ContractTransformTaskCallBack(c.ctx, c.svcCtx, msgStr)

					// NFT任务消息回调
					NftTaskCallBack(c.ctx, c.svcCtx, msgStr)
				} else if record.Topic == utils.UnifyTopic(kafka.UserCenterRegisterTopic) && c.groupId == kafka.DownLoadUserRegisterGroup {

					download.DownloadTaskRegisterCallBack(c.ctx, c.svcCtx, msgStr)
					RegTaskCallBack(c.ctx, c.svcCtx, msgStr)
				} else if record.Topic == utils.UnifyTopic(kafka.GrowthTaskCallTopic) && c.groupId == kafka.VipAirdropTaskGroup {
					VipAirdropTaskCallBack(c.ctx, c.svcCtx, msgStr)
				} else if record.Topic == utils.UnifyTopic(kafka.TaskInviteJoinTopic) && c.groupId == kafka.HelpTaskRegisterGroup {
					HelpTaskRegCallBack(c.ctx, c.svcCtx, msgStr)
				} else if record.Topic == utils.UnifyTopic(kafka.GrowthTaskCallTopic) && c.groupId == kafka.HelpTaskFinishGroup {
					HelpInviteeTaskCallBack(c.ctx, c.svcCtx, msgStr)
				} else if record.Topic == utils.UnifyTopic(kafka.UserCenterRegisterTopic) && c.groupId == kafka.HelpUserRegisterGroup {
					HelpUserRegCallBack(c.ctx, c.svcCtx, msgStr)
				}

				if err := c.client.CommitRecords(c.ctx, records...); err != nil {
					c.Logger.Errorf("consumeMessages 提交偏移量失败, err:%v", err)
				}
			}
		}

	}
}

// Close 关闭消费者客户端
func (c *KafkaConsumer) Close() {
	c.client.Close()
	c.Logger.Infof("消费者已关闭")
}

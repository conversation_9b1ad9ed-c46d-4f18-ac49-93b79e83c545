package kafka

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/risk"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_marketing_activity/internal/consts/kafka"
	"gateio_service_marketing_activity/internal/utils"
)

// TaskRecordReceiveMsg 给任务系统上报任务的消息体
type TaskRecordReceiveMsg struct {
	UserId       int64 `json:"user_id"`
	TaskId       int64 `json:"task_id"`
	ReceiveTime  int64 `json:"receive_time"`
	BusinessType int64 `json:"business_type"`
	BusinessId   int64 `json:"business_id"`
	IsDone       int   `json:"is_done"`
	IsAck        int   `json:"is_ack"`
}

// TaskRecordReceiveProducer 给任务系统上报任务消息-统一方法
func (p *KafkaProducer) TaskRecordReceiveProducer(ctx context.Context, userId int64, taskSysId int64, businessType int64, businessId int64, receiveTime int64, isDone int) error {
	// topic主题
	topicName := kafka.GrowthTaskRecordTopic

	// 发送的消息内容
	msg := TaskRecordReceiveMsg{
		UserId:       userId,
		TaskId:       taskSysId,
		ReceiveTime:  receiveTime,
		BusinessType: businessType,
		BusinessId:   businessId,
		IsDone:       isDone,
		IsAck:        1,
	}

	// 压缩消息体
	msgByte, err := json.Marshal(msg)
	if err != nil {
		logx.Infof("TaskRecordReceiveProducer failed, step: json.Marshal, Topic : %s, Msg: %s, err: %v", topicName, string(msgByte), err)
		return fmt.Errorf("kafka消息体序列化失败")
	}

	// 发送消息
	_, err = p.BaseProduceMsg(ctx, topicName, string(msgByte), "", 3)
	if err != nil {
		logx.Infof("TaskRecordReceiveProducer failed, step: p.BaseProduceMsg, Topic : %s, Msg: %s, err: %v", topicName, string(msgByte), err)
		// 上报任务失败，加入到补偿队列
		msgMap := utils.StructToMap(msg)
		param := &risk.ResetKafkaMessageRequest{
			Topic:   topicName,
			Message: msgMap,
			Key:     "",
		}
		err = risk.NewClient().ResetKafkaMessage(ctx, param)
		if err != nil {
			logx.Errorf("TaskRecordReceiveProducer failed, step: ResetKafkaMessage, param : %v, err: %v", param, err)
			return err
		}
	}
	return nil
}

type TaskRewardMsg struct {
	UserId       int64 `json:"user_id"`
	TaskId       int64 `json:"task_id"`
	BusinessId   int64 `json:"business_id"`
	BusinessType int64 `json:"business_type"`
	ReceiveTime  int64 `json:"receive_time"`
}

// TaskRewardProducer 给任务系统上报奖励-统一方法
func (p *KafkaProducer) TaskRewardProducer(ctx context.Context, userId int64, taskSysId int64, businessType int64, businessId int64, receiveTime int64) error {
	// topic主题
	topicName := kafka.GrowthTaskReceiveTopic

	// 发送的消息内容
	msg := TaskRewardMsg{
		UserId:       userId,
		TaskId:       taskSysId,
		ReceiveTime:  receiveTime,
		BusinessType: businessType,
		BusinessId:   businessId,
	}

	// 压缩消息体
	msgByte, err := json.Marshal(msg)
	if err != nil {
		logx.Infof("TaskRewardProducer failed, step: json.Marshal, Topic : %s, Msg: %s, err: %v", topicName, string(msgByte), err)
		return fmt.Errorf("kafka消息体序列化失败")
	}

	// 发送消息
	_, err = p.BaseProduceMsg(ctx, topicName, string(msgByte), "", 3)
	if err != nil {
		logx.Infof("TaskRewardProducer failed, step: p.BaseProduceMsg, Topic : %s, Msg: %s, err: %v", topicName, string(msgByte), err)
		// 上报任务失败，加入到补偿队列
		msgMap := utils.StructToMap(msg)
		param := &risk.ResetKafkaMessageRequest{
			Topic:   topicName,
			Message: msgMap,
			Key:     "",
		}
		err = risk.NewClient().ResetKafkaMessage(ctx, param)
		if err != nil {
			logx.Errorf("TaskRewardProducer failed, step: ResetKafkaMessage, param : %v, err: %v", param, err)
			return err
		}
	}
	return nil
}

package kafka

import (
	"context"
	"encoding/json"
	"time"

	"gateio_service_marketing_activity/internal/consts/activity"
	"gateio_service_marketing_activity/internal/consts/kafka"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/models/nft_user_invite_record"
	nft2 "gateio_service_marketing_activity/internal/pkg/nft"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/utils"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
)

func RegTaskCallBack(ctx context.Context, svcCtx *svc.ServiceContext, msgStr string) {
	logc.Infof(ctx, "nft 收到注册消息：%s", msgStr)

	// 解析消息内容
	taskCallBackMsg := kafka.UserRegisterCallBackMsg{}
	err := json.Unmarshal([]byte(msgStr), &taskCallBackMsg)
	if err != nil {
		logc.Infof(ctx, "download Unmarshal Msg err: %v", err)
		return
	}

	uid := taskCallBackMsg.Uid

	// 判断活动是否有效
	activityInfo, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetRunningActivitie(ctx, 1, 1, uid > 0)
	if err != nil {
		logc.Infof(ctx, "NftTaskList GetRunningActivitie  is err, err is:%v", err)
		return
	}
	if activityInfo == nil {
		logc.Infof(ctx, "NftTaskList activityInfo is null,")
		return
	}
	if activityInfo.SurpriseExtensionStartTime.IsZero() || activityInfo.SurpriseExtensionStartTime.After(time.Now()) || activityInfo.SurpriseExtensionEndTime.Before(time.Now()) {
		logc.Infof(ctx, "NftTaskList The current period is not an superised period")
		return
	}

	// 判断用户的注册码是否是红牛nft的注册码
	redBullCh := utils.GetBullCH()
	uid2InfoMap, err := service.NewUserCenterCall(ctx).GetUserMap([]int64{uid})
	if err != nil {
		logx.WithContext(ctx).Errorf("[RegTaskCallBack]uid:%v, GetUserMap error: %v", uid, err)
		return
	}
	if _, ok := uid2InfoMap[uid]; !ok {
		logx.WithContext(ctx).Errorf("[RegTaskCallBack]GetUserMap uid: %v not existed", uid)
		return
	}

	// 获取用户的注册渠道和注册时间
	userInfo := uid2InfoMap[uid]

	// 如果不是红牛nft的渠道，直接返回

	ch := userInfo.UserInfo.FromChannel
	if ch != redBullCh {
		logc.Infof(ctx, "[RegTaskCallBack]userInfo:%v, channel:%v redbullch:%v", userInfo, ch, redBullCh)
		return
	}

	// 直接保存用户的邀请记录
	inviteRecordModel := nft_user_invite_record.NewNftUserInviteRecordModel(svcCtx.DBMarketingActivities)
	err = inviteRecordModel.Insert(ctx, &nft_user_invite_record.NftUserInviteRecord{
		Uid:     uid,
		RefUid:  taskCallBackMsg.RefUid,
		Ch:      ch,
		RegTime: time.Now().Unix(),
	})
	if err != nil {
		logx.WithContext(ctx).Errorf("[RegTaskCallBack]inviteRecordModel.Insert err: %v", err)
		return
	}

	// 给用户创建一个首次注册的sub_record_task& 5个邀请用户的记录
	taskModel := nft.NewNftTaskModel(svcCtx.DBMarketingActivities)
	taskList, err := taskModel.GetTaskList(ctx, 1, nft.SceneTypeSurpriseExtension)
	if err != nil {
		logx.WithContext(ctx).Errorf("[RegTaskCallBack]GetTaskListBySceneType err: %v", err)
		return
	}

	filteredTaskList := make([]*nft.NftTask, 0)
	var inviteTask *nft.NftTask
	for _, task := range taskList {
		if task.TaskType == nft.TaskTypeSurpriseExtensionInviteeNoCh {
			continue
		}
		filteredTaskList = append(filteredTaskList, task)
		if task.TaskType == nft.TaskTypeSurpriseExtensionInviter {
			inviteTask = task
		}
	}
	if inviteTask == nil {
		logc.Warnf(ctx, "[RegTaskCallBack]GetTaskListBySceneType failed, inviteTask is nil")
		return
	}
	taskSubRecordList, err := nft2.GenerateAndSaveUserSubTask(ctx, svcCtx, activityInfo.ActivityId, uid, filteredTaskList)
	if err != nil {
		logx.WithContext(ctx).Errorf("[RegTaskCallBack]GenerateAndSaveUserSubTask err: %v", err)
		return
	}

	userSubTaskModel := nft.NewNftUserSubTaskModel(svcCtx.DBMarketingActivities)

	// 生成被邀请人任务
	for _, subRecord := range taskSubRecordList {
		if subRecord.TaskType != nft.TaskTypeSurpriseExtensionInviter {
			continue
		}
		err = SubmitTaskRecord(ctx, svcCtx, uid, subRecord.Id, inviteTask.TaskId)
		if err != nil {
			logx.WithContext(ctx).Errorf("[RegTaskCallBack]submitTaskRecord err: %v", err)
			continue
		}
		// 最后还要把用户任务状态更新成已上报
		subRecord.Status = nft.TaskStatusReported
		err = userSubTaskModel.UpdateOne(ctx, subRecord.Id, []string{"status"}, subRecord)
		if err != nil {
			logx.WithContext(ctx).Errorf("[RegTaskCallBack]UpdateOne err: %v", err)
			continue
		}
	}
}

func SubmitTaskRecord(ctx context.Context, svcCtx *svc.ServiceContext, uid, businessId, taskID int64) error {
	// 把业务的任务上报给任务系统
	getTaskTime := time.Now().Unix()
	kafkaProducer, err := NewKafkaProducer(svcCtx.UploadKafkaConf)
	if err != nil {
		logx.Infof("submitTaskRecord failed, step: kafka.NewKafkaProducer, UserId : %d, BusinessId: %d, err: %v", uid, businessId, err)
		return err
	}
	defer kafkaProducer.Close()

	err = kafkaProducer.TaskRecordReceiveProducer(ctx, uid, taskID, activity.NftTaskBusinessType, businessId, getTaskTime, 0)
	logx.Infof("submitTaskRecord, step: 上报任务系统的消息：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d", uid, businessId, taskID, activity.NftTaskBusinessType)
	if err != nil {
		logx.Infof("submitTaskRecord failed, step: Producer.TaskRecordReceiveProducer, UserId : %d, BusinessId: %d, err: %v", uid, businessId, err)
		return err
	}
	return nil
}

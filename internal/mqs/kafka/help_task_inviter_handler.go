package kafka

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"gateio_service_marketing_activity/internal/consts"
	kafka2 "gateio_service_marketing_activity/internal/consts/kafka"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/help_invite_task"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"github.com/spf13/cast"
)

func HelpInviterTaskCallBack(ctx context.Context, svcCtx *svc.ServiceContext, item kafka2.TaskCallBackMsgItem) {
	data, _ := json.Marshal(item)
	logx.Infof("[Kafka-Help-Inviter-Handler]，消息解析成功，收到任务系统回调消息：%s", string(data))
	businessId := cast.ToInt64(item.BusinessId)
	status := cast.ToInt64(item.Status)

	// 查询任务数据
	task, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).FindTaskById(ctx, businessId)
	if err != nil {
		logx.Infof("[Kafka-Help-Inviter-Handler] FindTaskById error: %v, businessId: %d", err, businessId)
		return
	}
	if task == nil {
		logx.Infof("[Kafka-Help-Inviter-Handler] task == nil, businessId: %d", businessId)
		return
	}

	if task.IsInviterTask == consts.IsInviteeTask {
		return
	}

	// 只处理已完成和已失效状态
	if status != kafka2.StatusCompleted && status != kafka2.StatusInvalid {
		logx.Infof("[Kafka-Help-Inviter-Handler] status != consts.StatusCompleted && status != consts.StatusInvalid, status: %d, businessId: %d", status, businessId)
		return
	}

	// 更新任务状态
	if status == kafka2.StatusCompleted {

		// 把用户任务状态更新
		invitees := item.InvitedUserIds
		// 判断被邀请人是否有风控
		isRisk := false
		for _, invitee := range invitees {
			inviteeTask, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).FindInviteeTaskByUid(ctx, int64(invitee))
			if err != nil {
				logx.WithContext(ctx).Errorf("[Kafka-Help-Inviter-Handler]FindInviteeTaskByUid err: %v", err)
			}
			if inviteeTask.Status == consts.InviteeStatusRisk {
				isRisk = true
			}
		}

		data, _ := json.Marshal(invitees)
		update := &help_invite_task.HelpInviteTask{
			Id:           businessId,
			ReportStatus: consts.TaskStatusCompleted,
			ExtraData:    sql.NullString{String: string(data), Valid: true},
		}
		if isRisk {
			update.Status = consts.HelpStatusRisk
			update.Valid = 0
		} else {
			update.Status = consts.HelpStatusSuccess
			update.Valid = 1
			update.FinishTime = time.Now().Unix()
		}

		_, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).UpdateHelpInviteTask(ctx, update)
		if err != nil {
			logx.WithContext(ctx).Errorf("[Kafka-Help-Inviter-Handler]UpdateHelpInviteTask err: %v", err)
			return
		}

		if !isRisk {
			// 发站内信
			_, err = service.SendHeraldMessages(svcCtx, ctx, consts.HeraldTaskComplete, []string{"mobile"}, []int64{task.Uid}, "", []string{})
			if err != nil {
				logc.Infof(ctx, fmt.Sprintf("[Kafka-Help-Inviter-Handler] SendHeraldMessages failed, uid:%d, err:%v", task.Uid, err))
			} else {
				logc.Infof(ctx, fmt.Sprintf("[Kafka-Help-Inviter-Handler] SendHeraldMessages success, uid:%d", task.Uid))
			}
		}

	}

	logx.Infof("[Kafka-Help-Inviter-Handler] Done，回调消息：%s", string(data))
}

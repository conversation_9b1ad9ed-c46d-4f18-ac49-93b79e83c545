package kafka

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"context"
	"encoding/json"
	"gateio_service_marketing_activity/internal/consts/activity"
	kafka2 "gateio_service_marketing_activity/internal/consts/kafka"
	"gateio_service_marketing_activity/internal/models/nft"
	"gateio_service_marketing_activity/internal/models/nft_user_invite_record"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/utils"
	"github.com/spf13/cast"
	"time"
)

// NftTaskCallBack nft任务 - 接受任务回调消息处理方法
func NftTaskCallBack(ctx context.Context, svcCtx *svc.ServiceContext, msgStr string) {
	logx.Infof("NftTaskCallBack，消息解析成功，收到任务系统回调消息：%s", msgStr)

	// 解析消息内容
	taskCallBackMsg := kafka2.TaskCallBackMsg{}
	err := json.Unmarshal([]byte(msgStr), &taskCallBackMsg)
	if err != nil {
		logx.Infof("NftTaskCallBack Unmarshal Msg err: %v", err)
		return
	}

	// 只处理NFT消息
	if taskCallBackMsg.BusinessType != activity.NftTaskBusinessType {
		return
	}

	// 消息内容为空不处理
	if len(taskCallBackMsg.List) == 0 {
		return
	}

	// 处理消息
	subTaskDataModel := nft.NewNftUserSubTaskModel(svcCtx.DBMarketingActivities)
	for _, item := range taskCallBackMsg.List {
		businessId := cast.ToInt64(item.BusinessId)
		status := cast.ToInt64(item.Status)

		// 查询任务数据
		subTask, err := subTaskDataModel.GetSubTaskByID(ctx, businessId)
		if err != nil {
			logx.Infof("NftTaskCallBack GetSubTaskByID error: %v, businessId: %d", err, businessId)
			continue
		}
		if subTask == nil {
			logx.Infof("NftTaskCallBack subTask == nil, businessId: %d", businessId)
			continue
		}

		// 只处理已完成和已失效状态
		if status != kafka2.StatusCompleted && status != kafka2.StatusInvalid {
			logx.Infof("NftTaskCallBack status != consts.StatusCompleted && status != consts.StatusInvalid, status: %d, businessId: %d", status, businessId)
			continue
		}

		// 表状态验证（非任务上报成功，不处理）
		if subTask.Status != nft.TaskStatusReported {
			logx.Infof("NftTaskCallBack subTask.Status != consts.TaskStatusInitial, subTask.Status: %d, businessId: %d", subTask.Status, businessId)
			continue
		}

		// 更新任务状态
		var setStatus int64
		if status == kafka2.StatusCompleted {
			// 任务已完成
			setStatus = nft.TaskStatusCompleted
		} else if status == kafka2.StatusInvalid {
			// 任务已失效
			setStatus = nft.TaskStatusInvalid
		}
		err = subTaskDataModel.UpdateOne(ctx, businessId, []string{
			"status",
			"updated_at",
		}, &nft.NftUserSubTask{
			Status:    setStatus,
			UpdatedAt: time.Now(),
		})
		if err != nil {
			logx.Infof("NftTaskCallBack UpdateOne error: %v, businessId: %d", err, businessId)
			continue
		}

		// 判断一下子任务的类型，如果是被邀请人任务的话，并且任务已完成的情况下，那就还需要判断有没有邀请人,如果有邀请人，需要去更新邀请人的任务状态
		if subTask.TaskType == nft.TaskTypeSurpriseExtensionInviter && status == kafka2.StatusCompleted {
			// 上传奖励信息
			taskInfo, err := nft.NewNftTaskModel(svcCtx.DBMarketingActivities).FindOne(ctx, subTask.TaskId)
			if err == nil && taskInfo != nil {
				mqErr := submitTaskReward(ctx, svcCtx, subTask.UserId, businessId, taskInfo.TaskId)
				if mqErr != nil {
					logx.Infof("NftTaskCallBack submitTaskReward err: %v", mqErr)
				}
			} else {
				logx.Infof("NftTaskCallBack FindOne err: %v taskInfo:%v", err, taskInfo)
			}

			if err != nil {
				// 上报奖励失败，不影响正常业务逻辑
				logx.WithContext(ctx).Errorf("[NftTaskCallBack] submitTaskReward err: %v", err)
			}

			logc.Infof(ctx, "[NftTaskCallBack] businessid:%v is inviter task", businessId)
			inviteRecordModel := nft_user_invite_record.NewNftUserInviteRecordModel(svcCtx.DBMarketingActivities)
			inviteRecord, err := inviteRecordModel.FindByUID(ctx, subTask.UserId)
			if err != nil {
				logx.Infof("[NftTaskCallBack] userID:%v find invite relation error: %v, businessId: %d", subTask.UserId, err, businessId)
				continue
			}
			// 没有邀请人或者邀请记录
			if inviteRecord == nil || inviteRecord.RefUid == 0 {
				logc.Infof(ctx, "[NftTaskCallBack]uid:%v, invited record is nil, businessId: %d inviteRecord:%v", subTask.UserId, businessId, inviteRecord)
				continue
			}

			// 获取邀请人的邀请任务的未完成子任务
			refRecords, err := subTaskDataModel.ListTaskRecordWithStatus(ctx, subTask.ActivityId, inviteRecord.RefUid, []int64{nft.TaskTypeSurpriseExtensionInviteeCh, nft.TaskTypeSurpriseExtensionInviteeNoCh}, nft.TaskStatusInitial)
			if err != nil {
				logx.WithContext(ctx).Errorf("[NftTaskCallBack]ListTaskRecordWithStatus err: %v", err)
				continue
			}

			// 只有邀请人用户还是不够的情况下，才去完成任务
			if len(refRecords) != 0 {
				userResp, err := service.NewUserCenterCall(ctx).GetUserInfo(subTask.UserId)
				var helpUserAvatar string
				if err != nil || userResp == nil || len(userResp.UserInfo.Avatar) == 0 {
					helpUserAvatar = utils.GetImageUrlByWallet(cast.ToString(subTask.UserId))
				} else {
					helpUserAvatar = userResp.UserInfo.Avatar
				}
				// 最后还要把用户任务更新成已完成、邀请人信息更新
				for _, refRecord := range refRecords {
					refRecord.Status = nft.TaskStatusCompleted
					affectedRows, err := subTaskDataModel.UpdateOneReturnRows(ctx, refRecord.Id, []string{
						"status",
						"updated_at",
						"help_user_id",
						"help_user_avatar",
					}, &nft.NftUserSubTask{
						Status:         nft.TaskStatusCompleted,
						UpdatedAt:      time.Now(),
						HelpUserId:     cast.ToString(subTask.UserId),
						HelpUserAvatar: helpUserAvatar,
					})
					if err != nil {
						logx.WithContext(ctx).Errorf("[NftTaskCallBack]UpdateOneReturnRows err: %v", err)
						continue
					}
					//
					if affectedRows > 0 {
						logc.Infof(ctx, "[NftTaskCallBack] affected rows: %v update id:%v", affectedRows, refRecord.Id)
						break
					}
				}

			} else {
				logc.Infof(ctx, "[NftTaskCallBack]uid:%v already completed", inviteRecord.RefUid)
			}

		}

	}

	logx.Infof("NftTaskCallBack Done，回调消息：%s", msgStr)
}

func submitTaskReward(ctx context.Context, svcCtx *svc.ServiceContext, uid, businessId, taskID int64) error {
	// 把业务的任务奖励上报给任务系统
	getTaskTime := time.Now().Unix()
	kafkaProducer, err := NewKafkaProducer(svcCtx.UploadKafkaConf)
	if err != nil {
		logx.Infof("submitTaskReward failed, step: kafka.NewKafkaProducer, UserId : %d, BusinessId: %d, err: %v", uid, businessId, err)
		return err
	}
	defer kafkaProducer.Close()

	err = kafkaProducer.TaskRewardProducer(ctx, uid, taskID, activity.NftTaskBusinessType, businessId, getTaskTime)
	logx.Infof("submitTaskReward, step: 上报任务系统的消息：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d", uid, businessId, taskID, activity.NftTaskBusinessType)
	if err != nil {
		logx.Infof("submitTaskReward failed, step: Producer.TaskRecordReceiveProducer, UserId : %d, BusinessId: %d, err: %v", uid, businessId, err)
		return err
	}
	return nil
}

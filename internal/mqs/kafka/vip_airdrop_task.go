package kafka

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-base-go/gconv"
	"context"
	"encoding/json"
	"gateio_service_marketing_activity/internal/consts/activity"
	kafka2 "gateio_service_marketing_activity/internal/consts/kafka"
	"gateio_service_marketing_activity/internal/models/vip_airdrop"
	"gateio_service_marketing_activity/internal/svc"
	"github.com/spf13/cast"
)

func VipAirdropTaskCallBack(ctx context.Context, svcCtx *svc.ServiceContext, msgStr string) {
	logx.Infof("VipAirdropTaskCallBack，消息解析成功，收到任务系统回调消息：%s", msgStr)

	// 解析消息内容
	taskCallBackMsg := kafka2.TaskCallBackMsg{}
	err := json.Unmarshal([]byte(msgStr), &taskCallBackMsg)
	if err != nil {
		logx.Infof("VipAirdropTaskCallBack Unmarshal Msg err: %v", err)
		return
	}
	if taskCallBackMsg.BusinessType != activity.VipAirdropBusinessType {
		return
	}
	// 消息内容为空不处理
	if len(taskCallBackMsg.List) == 0 {
		return
	}
	ts := make([]vip_airdrop.CcVipAirdropTaskRecord, 0)
	applyModel := vip_airdrop.NewCcVipAirdropUserModel(svcCtx.DBMarketingActivities)
	taskRecordModel := vip_airdrop.NewCcVipAirdropTaskRecordModel(svcCtx.DBMarketingActivities)
	for _, item := range taskCallBackMsg.List {
		status := cast.ToInt64(item.Status)
		if status == 3 || status == 5 || status == 6 {
			businessId := cast.ToInt64(item.BusinessId)
			applyInfo, err := applyModel.FindOne(ctx, businessId)
			if err != nil {
				logx.Infof("VipAirdropTaskCallBack FindOne err: %v", err)
				continue
			}
			ts = append(ts, vip_airdrop.CcVipAirdropTaskRecord{
				TaskId:        item.TaskId,
				Uid:           item.UserId,
				BusinessId:    gconv.ToInt64(item.BusinessId),
				Status:        gconv.ToInt64(status),
				CompletedTime: item.DoneTime,
				Season:        applyInfo.Season,
				OnlyId:        item.OnlyId,
			})
		}
	}
	if len(ts) > 0 {
		if err := taskRecordModel.BatchUpDateInsert(ctx, ts); err != nil {
			logx.Infof("VipAirdropTaskCallBack BatchUpDateInsert err: %v", err)
		}
	}
}

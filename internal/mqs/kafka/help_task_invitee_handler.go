package kafka

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gateio_service_marketing_activity/internal/consts"
	kafka2 "gateio_service_marketing_activity/internal/consts/kafka"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/help_invite_task"
	"gateio_service_marketing_activity/internal/models/help_user_prize"
	"gateio_service_marketing_activity/internal/pkg/common"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/types"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"github.com/spf13/cast"
)

func HelpInviteeTaskCallBack(ctx context.Context, svcCtx *svc.ServiceContext, msgStr string) {
	logx.Infof("HelpInviteeTaskCallBack，消息解析成功，收到任务系统回调消息：%s", msgStr)

	// 解析消息内容
	taskCallBackMsg := kafka2.TaskCallHelpBackMsg{}
	err := json.Unmarshal([]byte(msgStr), &taskCallBackMsg)
	if err != nil {
		logx.Infof("[Help-Task-Invitee] Unmarshal Msg err: %v", err)
		return
	}

	// 只处理助力领券消息
	if taskCallBackMsg.BusinessType != consts.HelpTaskBusinessType {
		return
	}

	// 消息内容为空不处理
	if len(taskCallBackMsg.List) == 0 {
		return
	}

	// 处理消息
	for _, item := range taskCallBackMsg.List {
		businessId := item.BusinessId
		status := cast.ToInt64(item.Status)
		userId := item.UserId
		taskCenterId := item.TaskId

		isInviteeMsg := consts.IfInviteeTask(taskCenterId)

		var task *help_invite_task.HelpInviteTask
		if isInviteeMsg {
			task, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).FindInviteeTaskByUid(ctx, userId)
			if err != nil {
				logx.WithContext(ctx).Errorf("FindInviteeTaskByUid failed: %v", err)
				continue
			}
		} else {
			// 查询任务数据
			businessIdInt, parseErr := strconv.ParseInt(businessId, 10, 64)
			if parseErr != nil {
				logx.WithContext(ctx).Errorf("[Help-Task-Invitee] ParseInt error: %v, businessId: %s", parseErr, businessId)
				continue
			}
			task, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).FindTaskById(ctx, businessIdInt)
			if err != nil {
				logx.WithContext(ctx).Errorf("[Help-Task-Invitee] FindTaskById error: %v, businessId: %s", err, businessId)
				continue
			}
		}

		// 只处理已完成和已失效状态
		if status != kafka2.StatusCompleted && status != kafka2.StatusInvalid {
			logx.Infof("[Help-Task-Invitee] status != consts.StatusCompleted && status != consts.StatusInvalid, status: %d, businessId: %s", status, businessId)
			continue
		}

		// 更新任务状态
		if status == kafka2.StatusCompleted {
			// 邀请人任务完成在另一个文件处理
			if task.IsInviterTask == consts.IsInviterTask {
				HelpInviterTaskCallBack(ctx, svcCtx, item)
				continue
			}

			// 被邀请人处理
			var steps []*types.Step
			err = json.Unmarshal([]byte(task.ExtraData.String), &steps)
			if err != nil {
				continue
			}

			for _, step := range steps {
				if step.TaskId == item.TaskId {
					if step.Completed {
						continue
					}
					step.Completed = true
				}
			}
			data, _ := json.Marshal(steps)
			newSteps := string(data)
			logx.Infof("[Help-Task-Invitee] FindTasMarshal steps: %v", newSteps)

			depositStatus := false
			tradeStatus := false
			for _, step := range steps {
				if step.Label == consts.TaskTypeDeposit {
					depositStatus = step.Completed
				} else if step.Label == consts.TaskTypeTrade {
					tradeStatus = step.Completed
				}
			}

			finish := depositStatus && tradeStatus
			reportStatus := task.ReportStatus
			if finish {
				reportStatus = consts.TaskStatusCompleted
			}
			// 把用户任务状态更新
			inviteeUpdate := &help_invite_task.HelpInviteTask{
				Id:             task.Id,
				TaskBusinessId: businessId,
				ExtraData:      sql.NullString{String: newSteps, Valid: true},
				ReportStatus:   reportStatus,
			}
			_, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).UpdateHelpInviteTask(ctx, inviteeUpdate)
			if err != nil {
				logx.WithContext(ctx).Errorf("[Help-Task-Invitee]UpdateHelpInviteTask err: %v", err)
				continue
			}
			// 更新邀请人数量
			refHelpTaskId := task.RefInviteTaskId
			refTask, _ := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).FindTaskById(ctx, refHelpTaskId)

			processNum := refTask.ProcessNum
			inLimit2 := true
			if processNum >= 2 {
				inLimit2 = false
			}

			inviteeUpdate = &help_invite_task.HelpInviteTask{
				Id:         task.Id,
				FinishTime: time.Now().Unix(),
			}
			if finish && !inLimit2 {
				inviteeUpdate.Valid = 0
				_, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).UpdateHelpInviteTask(ctx, inviteeUpdate)
				if err != nil {
					logx.WithContext(ctx).Errorf("[Help-Task-Invitee]UpdateHelpInviteTask err: %v", err)
					continue
				}
			} else if finish && inLimit2 {
				if processNum < 2 {
					processNum = processNum + 1
					inviterUpdate := &help_invite_task.HelpInviteTask{
						Id:         refHelpTaskId,
						ProcessNum: processNum,
					}
					_, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).UpdateHelpInviteTask(ctx, inviterUpdate)
					if err != nil {
						logx.WithContext(ctx).Errorf("[Help-Task-Invitee]UpdateHelpInviterTask err: %v", err)
						continue
					}
				}

				dataMap := make(map[string]interface{})
				dataMap["user_id"] = item.UserId
				dataMap["ip"] = ""
				dataMap["const_id"] = ""
				dataMap["action_code"] = "invitee_get_reward"
				dataMap["inviter_uid"] = refTask.Uid
				dataMap["invitee_uid"] = item.UserId
				isRisk, err := service.GetUserRiskData(svcCtx, ctx, int(item.UserId), "", consts.RiskEventCodeInviteRisk, dataMap, nil)
				if err != nil {
					logx.WithContext(ctx).Errorf("[Help-Task-Invitee] CheckRisk is err,params is:%d，%d, err is：%v", item.UserId, task.Id, err)
					continue
				}
				// 命中风控
				if isRisk {
					logx.Infof("[Help-Task-Invitee]GetAdvancedTasks user is risk,params is:%d, %d", item.UserId, task.Id)
					// 命中风控 把用户任务状态更新
					inviteeUpdate.Valid = 0
					inviteeUpdate.Status = consts.InviteeStatusRisk
					_, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).UpdateHelpInviteTask(ctx, inviteeUpdate)
					if err != nil {
						logx.WithContext(ctx).Errorf("[Help-Task-Invitee]UpdateHelpInviteTask err: %v", err)
					}
					continue
				}

				inviteeUpdate.Valid = 1
				inviteeUpdate.Status = consts.InviteeStatusFinish
				_, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).UpdateHelpInviteTask(ctx, inviteeUpdate)
				if err != nil {
					logx.WithContext(ctx).Errorf("[Help-Task-Invitee]UpdateHelpInviteTask err: %v", err)
					continue
				}

				// 插入领奖记录
				couponInfo, _ := service.GetCouponInfo(svcCtx, ctx, task.PrizeId, task.PrizeExtraInfo.String)
				amount := couponInfo.Amount
				newPrize := &help_user_prize.HelpUserPrize{
					Uid:             item.UserId,
					TaskId:          task.Id,
					IsInviterReward: consts.IsInviteeTask,
					PrizeId:         task.PrizeId,
					PrizeType:       task.PrizeType,
					PrizeValue:      amount,
					PrizeStatus:     consts.HelpUserPrizeStatusGetting,
					BusinessType:    consts.BusinessTypeHelp,
				}
				userPrizeId, err := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).InsertHelpUserPrize(ctx, newPrize)
				if err != nil {
					logx.WithContext(ctx).Errorf("[Help-Task-Invitee] Help-GetUserInfo err:%v, id: %d", err, task.PrizeId)
					continue
				}

				// 发放卡券 被邀请人调用卡券发奖
				var status int64
				_, err = common.NewCouponCall(ctx).SendCouponById(item.UserId, task.PrizeId, task.PrizeExtraInfo.String, amount, fmt.Sprintf("%d_%d", item.UserId, userPrizeId))
				if err != nil {
					logx.WithContext(ctx).Errorf("[Kafka-Help-Task-Invitee]助力领券调用卡券接口发卡券失败. taskId=%d, userId=%d, couponId=%d, source=%s, amount=%s, requestId=%s, err=%v",
						task.Id, item.UserId, task.PrizeId, task.PrizeExtraInfo.String, amount, fmt.Sprintf("%d_%d", item.UserId, userPrizeId), err)
					status = consts.HelpUserPrizeStatusFailed
				} else {
					status = consts.HelpUserPrizeStatusSuccess
				}

				// 修改状态
				helpUserPrize := &help_user_prize.HelpUserPrize{
					Id:              userPrizeId,
					PrizeStatus:     status,
					PrizeIssuedTime: time.Now().Unix(),
				}
				_, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).UpdateHelpUserPrize(ctx, helpUserPrize)
				if err != nil {
					logx.WithContext(ctx).Errorf("[Help-Task-Invitee] Help-UpdateHelpUserPrize err: %v, id: %d", err, userPrizeId)
					continue
				}

				// 发站内信给邀请人
				relation, _ := dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).GetUserInfo(ctx, item.UserId)
				_, err = service.SendHeraldMessages(svcCtx, ctx, consts.HeraldRefereeTaskComplete, []string{"mobile"}, []int64{relation.RefUid}, "", []string{relation.Name})
				if err != nil {
					logc.Infof(ctx, fmt.Sprintf("[Help-Task-Invitee] SendHeraldMessages failed, uid:%d, err:%v", relation.RefUid, err))
				} else {
					logc.Infof(ctx, fmt.Sprintf("[Help-Task-Invitee] SendHeraldMessages success, uid:%d", relation.RefUid))
				}
				// 发站内信给被邀请人 参数 被邀请人奖励
				depositRewardValue := service.GetTaskRewardDetail(svcCtx, ctx, consts.GetHelpInviteeDepositTaskId()).RewardNum
				tradeRewardValue := service.GetTaskRewardDetail(svcCtx, ctx, consts.GetHelpInviteeTradeTaskId()).RewardNum
				inviteeRewardValue := depositRewardValue + tradeRewardValue
				inviteeRewardValueStr := strconv.FormatInt(inviteeRewardValue, 10)
				_, err = service.SendHeraldMessages(svcCtx, ctx, consts.HeraldRefereeRewardRelease, []string{"mobile"}, []int64{item.UserId}, "", []string{inviteeRewardValueStr})
				if err != nil {
					logc.Error(ctx, fmt.Sprintf("[Help-Task-Invitee] SendHeraldMessages failed for uid:%d, err:%v", item.UserId, err))
				} else {
					logc.Infof(ctx, fmt.Sprintf("[Help-Task-Invitee] Invite notification sent successfully for uid:%d", item.UserId))
				}

				// 删除邀请人的席位数据缓存
				_, err = svcCtx.Redis.DelCtx(ctx, consts.HelpPageInitUsersKey(relation.RefUid))
				if err != nil {
					logc.Error(ctx, "[help_get_coupon]删除邀请人席位缓存失败:", err, relation.RefUid)
				}
			}

		} else if status == kafka2.StatusInvalid {
			helpInviteTask := &help_invite_task.HelpInviteTask{
				Id:           task.Id,
				ReportStatus: consts.TaskStatusInvalid,
			}
			if task.IsInviterTask == consts.IsInviterTask && task.Status == consts.HelpStatusInProgress {
				helpInviteTask.Status = consts.HelpStatusFailed
			}
			_, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).UpdateHelpInviteTask(ctx, helpInviteTask)
			if err != nil {
				logx.WithContext(ctx).Errorf("[Help-Task-Invitee] Help-UpdateHelpInviteTask err: %v, id: %d", err, helpInviteTask)
				continue
			}
		}
	}
	logx.Infof("HelpInviteeTaskCallBack Done，回调消息：%s", msgStr)
}

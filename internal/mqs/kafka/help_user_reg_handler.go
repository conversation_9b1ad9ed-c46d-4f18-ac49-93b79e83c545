package kafka

import (
	"context"
	"encoding/json"
	"time"

	"gateio_service_marketing_activity/internal/consts"
	"gateio_service_marketing_activity/internal/consts/kafka"
	"gateio_service_marketing_activity/internal/dao"
	"gateio_service_marketing_activity/internal/models/help_relation_ship"
	"gateio_service_marketing_activity/internal/service"
	"gateio_service_marketing_activity/internal/svc"

	"bitbucket.org/gatebackend/go-zero/core/logx"
)

func HelpUserRegCallBack(ctx context.Context, svcCtx *svc.ServiceContext, msgStr string) {
	logx.Infof("HelpUserRegCallBack，收到任务系统回调消息：%s", msgStr)
	// 解析消息内容
	taskCallBackMsg := kafka.UserRegisterCallBackMsg{}
	err := json.Unmarshal([]byte(msgStr), &taskCallBackMsg)
	if err != nil {
		logx.Infof("HelpUserRegCallBack Unmarshal Msg err: %v", err)
		return
	}
	uid := taskCallBackMsg.Uid
	registerTime, _ := time.Parse(time.DateTime, taskCallBackMsg.Timest)
	refUid := taskCallBackMsg.RefUid

	// 只处理助力领券渠道
	if taskCallBackMsg.UtmCmp != consts.GetHelpGetCouponUtmCmp() {
		return
	}

	// 获取用户信息
	userResp, err := service.NewUserCenterCall(ctx).GetUserInfo(uid)
	if err != nil || userResp == nil {
		logx.WithContext(ctx).Errorf("[Help-RegTaskCallBack] Help-GetUserInfo uid:%v, GetUserInfo err: %v", uid, err)
		return
	}

	// 直接保存用户的邀请记录
	relation := &help_relation_ship.HelpRelationShip{
		Uid:          uid,
		Name:         userResp.UserInfo.Nick,
		Avatar:       userResp.UserInfo.Avatar,
		RefUid:       refUid,
		RegisterTime: registerTime.Unix(),
		BusinessType: consts.BusinessTypeHelp,
	}
	_, err = dao.NewMarketingActivityMysqlDBUtil(svcCtx.DBMarketingActivities).InsertHelpRelationShip(ctx, relation)
	if err != nil {
		logx.WithContext(ctx).Errorf("[Help-RegTaskCallBack]inviteRecordModel.Insert err: %v", err)
		return
	}
}

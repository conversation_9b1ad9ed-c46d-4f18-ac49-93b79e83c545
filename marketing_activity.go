package main

import (
	"flag"
	"fmt"
	"os"
	"strconv"

	"gateio_service_marketing_activity/internal/config"
	"gateio_service_marketing_activity/internal/handler"
	"gateio_service_marketing_activity/internal/mqs/kafka"
	"gateio_service_marketing_activity/internal/svc"
	"gateio_service_marketing_activity/internal/task"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/core/service"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"bitbucket.org/gatebackend/go-zero/rest"
	"bitbucket.org/gatebackend/go-zero/rest/httpx"
	"bitbucket.org/gateio/gateio-lib-base-go/commonconfig"
	"bitbucket.org/gateio/gateio-lib-base-go/health"
	"bitbucket.org/gateio/gateio-lib-base-go/security/inputguard"
	"bitbucket.org/gateio/gateio-lib-common-go/language"
	"bitbucket.org/gateio/gateio-lib-common-go/middleware"
	"bitbucket.org/gateio/gateio-lib-common-go/validate"

	"bitbucket.org/gatebackend/go-zero/core/conf"
)

var configFile = flag.String("f", "etc/marketing_activity.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c, conf.UseEnv())

	if len(c.Nacos.Ip) != 0 {
		cc, err := config.LoadConfigFromNacos(c.Nacos)
		if err != nil {
			logx.Must(err)
		}
		c = *cc
	}

	c.OuterServiceConf.MustSetUp()

	// 使用 validator/v10 来验证请求参数
	// see: https://gtglobal.jp.larksuite.com/wiki/M0SIw2WWMis1m8kEtg5jswuopue#XFyMdCAbgoOWOjxtvkxj8OmhpCd
	httpx.SetValidator(validate.NewGoZeroValidator())

	// 用来初始化基础组件需要的配置
	// see: https://gtglobal.jp.larksuite.com/wiki/UOjuw7KZKiCG3tkmQA7jgGLwpXd#Tbn8dRDqkoHJO7xPmIHj0QuRpqg
	logx.Must(commonconfig.Init())
	// 初始化语言包
	language.MustInit()

	// 外部服务, 使用 3460 端口
	outerServer := rest.MustNewServer(c.OuterServiceConf.RestConf)
	outerServer.Use(inputguard.NewInputGuard(inputguard.Reject).GoZeroInterceptor())

	// 内部服务, 使用 9580 端口, 需要自行创建 handlers 并注册
	innerServer := rest.MustNewServer(c.InnerServiceConf.RestConf)
	innerServer.Use(inputguard.NewInputGuard(inputguard.Reject).GoZeroInterceptor())

	svcCtx := svc.NewServiceContext(&c)
	serviceGroup := service.NewServiceGroup()
	serviceGroup.Add(outerServer)
	health.RegisterHandlers(outerServer)

	if isOffLine() {
		srv := xxljob.NewServer(c.XXLJob)
		task.RegisterTasks(srv, svcCtx)
		serviceGroup.Add(srv)

		fmt.Printf("Starting health server at %s:%d\n", c.OuterServiceConf.Host, c.OuterServiceConf.Port)
		outerServer.PrintRoutes()
	} else {
		// 仅在线服务时才会注册路由, 离线任务虽然启动了 HTTP 服务, 但不注册任何接口
		handler.NewRegisterInnerHandlers(innerServer, svcCtx)
		handler.RegisterHandlers(outerServer, svcCtx)
		// innerServer.Use(middleware.Authorize()) // 需要全局使用的中间件，可以这样使用
		outerServer.Use(middleware.RequestCarrier())
		innerServer.Use(middleware.RequestCarrier())

		serviceGroup.Add(innerServer)

		fmt.Printf("Starting outer server at %s:%d\n", c.OuterServiceConf.Host, c.OuterServiceConf.Port)
		outerServer.PrintRoutes()
		fmt.Printf("Starting inner server at %s:%d\n", c.InnerServiceConf.Host, c.InnerServiceConf.Port)
		innerServer.PrintRoutes()

		// 启动kafka消费者
		kafka.StartAllConsumer(svcCtx, c.KafkaConf)

		kafka.StartAllConsumer(svcCtx, c.UploadKafkaConf)

	}

	serviceGroup.Start()
}

func isOffLine() bool {
	v, ok := os.LookupEnv("IS_OFFLINE")
	if !ok {
		return false
	}

	vv, err := strconv.ParseBool(v)
	if err != nil {
		return false
	}
	return vv
}

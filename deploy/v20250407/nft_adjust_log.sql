-- 用户参加活动上报归因记录表
CREATE TABLE `nft_adjust_log`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '数据ID',
    `uid`                  bigint unsigned NOT NULL DEFAULT '0' COMMENT 'uid 中心化领取或购买的用户ID',
    `activity_type`        tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '活动类型 1:红牛nft落地页',
    `created_at`           timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                    `idx_uid` (`uid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户参加活动上报归因记录表';
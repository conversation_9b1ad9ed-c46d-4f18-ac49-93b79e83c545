-- nft信息
CREATE TABLE `nft_detail`
(
    `id`                  bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '数据ID',
    `nft_id`              bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'nft id',
    `parent_nft_id`       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '上级ID 用于分层',
    `nft_name`            varchar(100)   NOT NULL DEFAULT '' COMMENT 'nft名称',
    `activity_id`         bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '活动ID 每个NFT都有对应活动',
    `nft_type`            tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT 'nft类型 1普通nft 2白银nft 3黄金nft 4特殊活动对应的NFT',
    `campaign_day`        tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '所在活动可领取期的天数 1:可领取期的第一天',
    `campaign_start_time` timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '活动日开始时间 NFT可领取或购买的开始时间',
    `campaign_end_time`   timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '活动日结束时间 NFT可领取或购买的结束时间',
    `nft_icon_url`        varchar(255)   NOT NULL DEFAULT '' COMMENT 'NFT图片',
    `nft_gif_url`         varchar(255)   NOT NULL DEFAULT '' COMMENT '黄金，白银等级的动图 用于过程中查看展示，领取期购买结果可能是黄金等级也可能是白银等级',
    `cen_nft_price`       decimal(20, 6) NOT NULL COMMENT '中心化nft价格',
    `dec_nft_price`       decimal(20, 6) NOT NULL COMMENT '去中心化nft价格',
    `created_at`          timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`          timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                   `idx_activity_id` (`activity_id`,`nft_id`) USING BTREE,
    KEY                   `idx_nft_type` (`nft_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='nft表';
-- 用户nft铸造记录表
CREATE TABLE `nft_mint_log`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '数据ID',
    `nft_id`               bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'nft id',
    `parent_nft_id`        bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '上级ID 用于分层 一期6个',
    `nft_user_sub_task_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '任务记录ID 用于判断',
    `wallet_address`       varchar(255)   NOT NULL DEFAULT '' COMMENT '钱包标识 去中心化领取或购买的钱包ID 用于判断用户重复领取或购买',
    `uid`                  bigint unsigned NOT NULL DEFAULT '0' COMMENT 'uid 中心化领取或购买的用户ID',
    `activity_id`          bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '活动ID',
    `token_url`            varchar(255)   NOT NULL DEFAULT '' COMMENT '回调传回的参数 https://data.gatenft.io/redbullNFT/1 1对应的就是nft ID',
    `token_id`             varchar(255)   NOT NULL DEFAULT '' COMMENT '回调传回的参数',
    `operation_id`         bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作ID 用于透传 方便判断重复铸造相同nft的操作',
    `mid`                  bigint unsigned NOT NULL DEFAULT '0' COMMENT '中心化铸造表ID',
    `mint_status`          tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '铸造状态 1初始化 2进行中 3成功 4失败 ',
    `mint_status_cause`    text           NOT NULL COMMENT '铸造失败状态对应的原因',
    `pay_type`             tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '支付类型 1中心化 2去中心化',
    `expenditure`          decimal(20, 6) NOT NULL COMMENT '支出金额',
    `created_at`           timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                    `idx_uid` (`uid`,`activity_id`,`parent_nft_id`) USING BTREE,
    KEY                    `idx_wallet_address` (`wallet_address`,`activity_id`,`parent_nft_id`) USING BTREE,
    KEY                    `idx_operation_id` (`operation_id`) USING BTREE,
    KEY                    `idx_nft_id` (`nft_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户nft铸造记录表';
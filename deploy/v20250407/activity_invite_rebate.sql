CREATE TABLE `activity_invite_rebate` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` int unsigned NOT NULL DEFAULT '0' COMMENT '新注册用户',
  `ref_uid` int unsigned NOT NULL DEFAULT '0' COMMENT '邀请人',
  `reg_time` int unsigned NOT NULL DEFAULT '0' COMMENT '注册时间',
  `valid` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1：有效，0：无效（邀请关系不符合）',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '任务上报状态，0：未上报，1：已上报，2：上报中断',
  `text` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_uid_ref_uid` (`uid`,`ref_uid`) USING BTREE,
  KEY `idx_ref_uid` (`ref_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;


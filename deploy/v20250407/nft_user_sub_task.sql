CREATE TABLE `nft_user_sub_task`
(
    `id`               int          NOT NULL AUTO_INCREMENT,
    `activity_id`      bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '活动ID',
    `user_id`          int unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `wallet_address`   varchar(255) NOT NULL DEFAULT '' COMMENT '钱包标识',
    `task_id`          int unsigned NOT NULL DEFAULT '0' COMMENT '任务表ID',
    `task_type`        tinyint unsigned NOT NULL DEFAULT '1' COMMENT '任务类型：1-转发Gateway to Greatness 的 X推文、2-邀请好友注册Gate.io账户并完成KYC、3-邀请好友助力你获得NFT、101-转发Gateway to Greatness 的 X推文、102-关注Gate X账户、103-加入Gate.io TG群组、104-邀请好友助力你获得NFT',
    `status`           tinyint unsigned NOT NULL DEFAULT '0' COMMENT '任务状态：0: 初始状态, 1: 任务上报成功, 2: 任务已完成, 3: 任务已失效',
    `help_user_id`     varchar(100) NOT NULL DEFAULT '' COMMENT '帮助完成任务用户ID（uid、wallet_address）',
    `help_user_avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '帮助完成任务用户头像',
    `media_id`         varchar(100) NOT NULL DEFAULT '' COMMENT '任务对应的社媒账号',
    `extra_data`       text COMMENT '扩展信息',
    `created_at`       timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`       timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                `idx_uid` (`user_id`) USING BTREE,
    KEY                `idx_wallet_address` (`wallet_address`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户nft子任务记录';
CREATE TABLE `nft_activity`
(
    `id`                 bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '数据ID',
    `activity_id`        bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '活动ID',
    `base_type`          tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '1 红牛NFT活动',
    `activity_type`      tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '1正常4期NFT活动 2特殊的3期活动',
    `activity_name`      varchar(100) NOT NULL DEFAULT '' COMMENT '活动名称',
    `start_time`         timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '活动开始时间',
    `end_time`           timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '活动结束时间',
    `receive_start_time` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '领取开始时间',
    `receive_end_time`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '领取结束时间',
    `task_start_time`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '额外活动开始时间 可以通过做任务领取NFT的时间段',
    `task_end_time`      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '额外活动结束时间 可以通过做任务领取NFT的时间段',
    `draw_end_time`      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '抽奖结束时间',
    `activity_icon`      varchar(255) NOT NULL DEFAULT '' COMMENT '活动图片',
    `activity_title`     varchar(255) NOT NULL DEFAULT '' COMMENT '活动标题',
    `created_at`         timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                  `idx_activity_id` (`activity_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动信息表';

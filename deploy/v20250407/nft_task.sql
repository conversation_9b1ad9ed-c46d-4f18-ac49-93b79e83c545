CREATE TABLE `nft_task`
(
    `id`                int          NOT NULL AUTO_INCREMENT,
    `task_id`           bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '任务ID',
    `task_name`         varchar(255) NOT NULL DEFAULT '' COMMENT '任务名称',
    `task_type`         tinyint unsigned NOT NULL DEFAULT '1' COMMENT '任务类型：1-转发Gateway to Greatness 的 X推文、2-邀请好友注册Gate.io账户并完成KYC、3-邀请好友助力你获得NFT、101-转发Gateway to Greatness 的 X推文、102-关注Gate X账户、103-加入Gate.io TG群组、104-邀请好友助力你获得NFT',
    `completion_limit`  tinyint unsigned NOT NULL DEFAULT '1' COMMENT '允许完成次数',
    `is_centralization` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否中心化任务',
    `task_config`       json                  DEFAULT '' COMMENT '任务类型-独有配置',
    `media_id`          varchar(100) NOT NULL DEFAULT '' COMMENT '任务对应的社媒账号',
    `created_at`        timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`        timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                 `idx_task_id` (`task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='nft任务表';
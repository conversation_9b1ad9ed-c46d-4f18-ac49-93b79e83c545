CREATE TABLE `contract_transform_user_task` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
    `task_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '任务id',
    `sys_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '任务系统id',
    `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '任务状态: 0-未完成,1-进行中,2-已完成,3-结算中,4-已结算,5-已过期',
    `source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务来源',
    `finish_num` int unsigned NOT NULL DEFAULT '0' COMMENT '任务完成次数',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id_task_id` (`user_id`,`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='合约定向转化活动-用户任务状态记录表';
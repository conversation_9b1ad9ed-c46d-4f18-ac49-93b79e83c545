CREATE TABLE `activity_check_in_records`
(
    `id`             bigint(20) unsigned                    NOT NULL AUTO_INCREMENT,
    `activity_id`    int(10) unsigned                       NOT NULL DEFAULT '0' COMMENT '活动id',
    `uid`            bigint(20) unsigned                    NOT NULL DEFAULT '0' COMMENT 'uid',
    `day`            int(10) unsigned                       NOT NULL DEFAULT '0' COMMENT 'Ymd格式',
    `prize_id`       int(10) unsigned                       NOT NULL DEFAULT '0' COMMENT '奖品id',
    `prize_type`     int(10) unsigned                       NOT NULL DEFAULT '0' COMMENT '奖品类型 1无奖励 2卡券',
    `prize_type_num` int(10) unsigned                       NOT NULL DEFAULT '0' COMMENT '奖品数量',
    `status`         varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'INIT' COMMENT '状态 INIT:初始化;SUCCESS:成功; FAILED:失败',
    `detail`         text COLLATE utf8mb4_unicode_ci        NOT NULL COMMENT '详情json',
    `created_at`     timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`     timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_uid` (`activity_id`, `uid`, `day`) USING BTREE
) ENGINE = InnoDB  DEFAULT CHARSET = utf8mb4  COLLATE = utf8mb4_unicode_ci COMMENT ='签到记录表';
CREATE TABLE `nft_user_invite_record`
(
    `id`         int       NOT NULL AUTO_INCREMENT,
    `uid`        int unsigned NOT NULL DEFAULT '0' COMMENT '新注册用户',
    `ref_uid`    int unsigned NOT NULL DEFAULT '0' COMMENT '邀请人',
    `ch`         varchar(100)   NOT NULL COMMENT '注册码',
    `reg_time`   int unsigned NOT NULL DEFAULT '0' COMMENT '注册时间',
    `extra_info` text COMMENT '额外信息',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_uid_ref_uid` (`uid`,`ref_uid`) USING BTREE,
    KEY          `idx_ref_uid` (`ref_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户邀请记录表';
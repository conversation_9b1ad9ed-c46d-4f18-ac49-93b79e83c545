CREATE TABLE `download_invitation_relation`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `uid`        int(11) NOT NULL DEFAULT 0  COMMENT '用uid',
    `ref_uid`    int(11) NOT NULL DEFAULT 0  COMMENT '邀请人uid',
    `status`     int(1) NOT NULL DEFAULT 0  COMMENT '任务状态 1 进行中, 2 完成',
    `progress`   int(1) NOT NULL DEFAULT 0 COMMENT '任务进度,任务自行判定',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    KEY          `idx_uid` (`ref_uid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户参加任务记录';
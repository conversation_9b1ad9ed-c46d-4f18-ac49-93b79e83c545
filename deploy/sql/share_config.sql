create table `share_config`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT COMMENT '活动id',
    `title`           varchar(1024) not null comment '标题',
    `source`          varchar(512)  not null comment '关联对应的场景',
    `base_image`      varchar(4096) not null default '{}' comment '底图，需要支持配置多个语言',
    `union_logo`      varchar(1024) not null default '{}' comment '联合logo',
    `tip_desc`        varchar(1024) not null default '{}' comment '底图营销文案，仅作为后台文案展示',
    `tip_key`         varchar(1024) not null default '{}' comment '海报底部营销文案翻译Key',
    `status`          int           not null comment '1:未生效 2：生效中',
    `edit_info`       text          not null  comment '未生效的配置',
    `approval_status` int           not null comment '审批状态，1:待审批 2：审批通过 3：审批驳回',
    `op_id`           int unsigned NOT NULL COMMENT '操作人ID',
    `op_name`         varchar(50)   NOT NULL COMMENT '操作人姓名',
    `created_at`      timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`      timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    unique uniq_source (`source`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享组件配置';
CREATE TABLE `brand_partner` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '合作商ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '合作商名称',
  `photo_wall_config` text DEFAULT NULL COMMENT '照片墙配置',
  `video_config` text DEFAULT NULL COMMENT '视频配置',
  `competition_config` text DEFAULT NULL COMMENT '赛事配置',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '合作商描述',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认展示：0否、1是',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0未删除、1已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='品牌合作商表';


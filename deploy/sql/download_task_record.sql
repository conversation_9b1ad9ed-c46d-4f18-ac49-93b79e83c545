CREATE TABLE `download_task_record`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `aid`        int(11) NOT NULL DEFAULT 0 COMMENT '活动id',
    `uid`        int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
    `nickname`   varchar(100) NOT NULL DEFAULT '' COMMENT '用户昵称',
    `task_id`    int(11) NOT NULL DEFAULT 0 COMMENT '任务id',
    `status`     tinyint NOT NULL DEFAULT 0 COMMENT '任务状态 1 进行中, 2 完成 3失败',
    `progress`   tinyint NOT NULL DEFAULT 0 COMMENT '任务进度,任务自行判定',
    `coupon_type` varchar(128) NOT NULL DEFAULT '' COMMENT '券类型。contract_bonus_new:合约体验券、commission_rebate:手续费返现券',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    KEY          `idx_aid_uid` (`aid`, `uid`) USING BTREE,
    KEY          `idx_uid` (`uid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户参加任务记录';
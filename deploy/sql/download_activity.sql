CREATE TABLE `download_activity`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT COMMENT '活动id',
    `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间时间',
    `end_time`   int(11) NOT NULL DEFAULT '0' COMMENT '结束时间时间',
    `extra_user_num` int  NOT NULL DEFAULT 327 COMMENT '额外的人数',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    KEY          `idx_start_time` (`start_time`) USING BTREE,
    KEY          `idx_end_time` (`end_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='下载专线活动记录表';
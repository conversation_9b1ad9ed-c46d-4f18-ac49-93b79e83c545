CREATE TABLE `general_approval`
(
    `id`               int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `business_type`    tinyint unsigned NOT NULL COMMENT '业务类型：1分享组件配置',
    `business_id`      int unsigned NOT NULL COMMENT '业务ID',
    `title`            varchar(50)   not null comment '申请名称',
    `source`           varchar(512)  not null default '' comment '关联对应的场景',
    `applicant_id`     int unsigned NOT NULL COMMENT '申请人id',
    `applicant_name`   varchar(50)   NOT NULL COMMENT '申请人姓名',
    `approval_status`  tinyint unsigned NOT NULL DEFAULT '1' COMMENT '审核状态：1=待审批, 2=审批通过, 3=审批驳回',
    `approval_type`    varchar(64)   NOT NULL DEFAULT '' COMMENT '审批类型：new:新建 modify:更改',
    `approval_content` varchar(8192) NOT NULL DEFAULT '' COMMENT '审批内容',
    `reviewer_id`      int unsigned NOT NULL DEFAULT '0' COMMENT '审核人',
    `reviewer_name`    varchar(50)   NOT NULL DEFAULT '' COMMENT '审核人姓名',
    `reviewer_time`    datetime               DEFAULT NULL COMMENT '审批时间',
    `version`          int           not null Default 1 COMMENT '当前修改的版本',
    `extra_data`       text                   DEFAULT NULL COMMENT '扩展字段json',
    `create_time`      timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY                `idx_business` (`business_id`, `business_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通用-审批表';
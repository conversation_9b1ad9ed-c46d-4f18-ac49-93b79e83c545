CREATE TABLE `marketing_activities`.`help_user_prize` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` bigint NOT NULL DEFAULT '0' COMMENT '用户ID',
  `task_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '任务ID',
  `is_inviter_reward` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否邀请人奖励:0被邀请人,1邀请人',
  `prize_id` int unsigned NOT NULL DEFAULT '0' COMMENT '奖品id',
  `prize_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '奖励类型',
  `prize_value` varchar(50) NOT NULL DEFAULT '0' COMMENT '奖品规格',
  `prize_status` int unsigned NOT NULL DEFAULT '0' COMMENT  '初始化/未领取:0,已发放:1,发放失败:2',
  `prize_issued_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '奖励发送时间',
  `extra_data` text COMMENT '扩展信息',
  `business_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '业务类型',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uid_task_id` (`uid`,`task_id`) USING BTREE,
  KEY `idx_ft_id` (`task_id`),
  KEY `idx_uid` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邀请奖励表';
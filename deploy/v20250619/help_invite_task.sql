CREATE TABLE `marketing_activities`.`help_invite_task` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int unsigned NOT NULL DEFAULT '0' COMMENT '任务系统-任务id',
  `relation_id` int unsigned NOT NULL DEFAULT '0' COMMENT '邀请记录表id',
  `uid` int unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `ref_invite_task_id` int unsigned NOT NULL DEFAULT '0' COMMENT '邀请人任务ID',
  `task_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '任务类型:1: 单次任务, 2: 多次领取任务',
  `is_inviter_task` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否邀请人任务:0被邀请人, 1邀请人任务',
  `conf_id` int unsigned NOT NULL DEFAULT '0' COMMENT '配置ID',
  `task_business_id` varchar(50) NOT NULL DEFAULT '' COMMENT '任务中心bussinessID',
  `report_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '0: 初始状态, 1: 任务上报成功, 2: 任务已完成, 3: 任务已失效',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '邀请人任务状态 1未报名 2待助力 3助力成功 4助力未完成 5已领奖 6命中风控;被邀请人状态 0未创建 1注册 2入金/交易 3已完成 4已结束 5命中风控',
  `extra_data` text COMMENT '扩展信息 被邀请人的几个进度的状态',
  `process_num` int unsigned NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `valid` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否有效',
  `prize_id` int unsigned NOT NULL DEFAULT '0' COMMENT '奖励id',
  `prize_extra_info`  text COMMENT '奖励扩展信息',
  `prize_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '奖励类型',
  `business_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '业务类型',
  `end_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '结束时间',
  `finish_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_uid` (`uid`) USING BTREE,
  KEY `idx_created_at` (`created_at`),
  KEY `idx_ref_invite_task_id` (`ref_invite_task_id`),
  KEY `idx_task_business_id` (`task_business_id`),
  KEY `idx_finish_time` (`finish_time`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邀请任务表';
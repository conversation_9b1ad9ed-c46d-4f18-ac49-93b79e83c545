CREATE TABLE `marketing_activities`.`help_relation_ship` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` int unsigned NOT NULL DEFAULT '0' COMMENT '新注册用户',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '用户名',
  `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '用户头像',
  `ref_uid` int unsigned NOT NULL DEFAULT '0' COMMENT '邀请人',
  `register_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '注册时间',
  `valid` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:有效, 0:无效（邀请关系不符合），备用字段',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态，备用字段',
  `extra_data` text COMMENT '扩展信息',
  `business_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '业务类型',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_uid_ref_uid` (`uid`,`ref_uid`) USING BTREE,
  KEY `idx_ref_uid` (`ref_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邀请关系表';
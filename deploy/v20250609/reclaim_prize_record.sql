create table `reclaim_prize_record`
(
    `id`             int unsigned NOT NULL AUTO_INCREMENT,
    `prize_info_id`  int          not null comment '中奖信息表的id',
    `uid`            int          NOT NULL DEFAULT 0 COMMENT '兑奖用户',
    `wallet_address` varchar(255) NOT NULL DEFAULT '' COMMENT '兑奖用户钱包地址',
    `prize_category` int          NOT NULL comment '1: 实物奖励 2：虚拟奖励',
    `prize_type`     int          NOT NULL comment '奖品类型',
    `reclaim_info`   text comment '兑奖信息',
    `created_at`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    unique uniq_prize (prize_info_id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='兑奖信息记录表';
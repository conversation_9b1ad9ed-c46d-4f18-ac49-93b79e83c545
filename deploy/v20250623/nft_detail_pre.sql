create table nft_detail_pre
(
    id                  bigint unsigned auto_increment comment '数据ID',
    nft_id              bigint unsigned  default '0'               not null comment 'nft id',
    parent_nft_id       bigint unsigned  default '0'               not null comment '上级ID 用于分层',
    nft_name            varchar(100)     default ''                not null comment 'nft名称',
    activity_id         bigint unsigned  default '0'               not null comment '活动ID 每个NFT都有对应活动',
    nft_type            tinyint unsigned default '0'               not null comment 'nft类型 1普通nft 2白银nft 3黄金nft 4特殊活动对应的NFT',
    campaign_day        tinyint unsigned default '0'               not null comment '所在活动可领取期的天数 1:可领取期的第一天',
    campaign_start_time timestamp        default CURRENT_TIMESTAMP not null comment '活动日开始时间 NFT可领取或购买的开始时间',
    campaign_end_time   timestamp        default CURRENT_TIMESTAMP not null comment '活动日结束时间 NFT可领取或购买的结束时间',
    nft_icon_url        varchar(255)     default ''                not null comment 'NFT图片',
    nft_gif_url         varchar(255)     default ''                not null comment '黄金，白银等级的动图 用于过程中查看展示，领取期购买结果可能是黄金等级也可能是白银等级',
    cen_nft_price       decimal(20, 6)                             not null comment '中心化nft价格',
    dec_nft_price       decimal(20, 6)                             not null comment '去中心化nft价格',
    created_at          timestamp        default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    primary key (id),
    index idx_activity_id (activity_id, nft_id),
    index idx_nft_type (nft_type)
)
    comment 'nft表-预发环境表' collate = utf8mb4_unicode_ci;




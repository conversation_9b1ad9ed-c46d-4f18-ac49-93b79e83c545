create table nft_activity_pre
(
    id                            bigint unsigned auto_increment comment '数据ID',
    activity_id                   bigint unsigned  default '0'               not null comment '活动ID',
    base_type                     tinyint unsigned default '0'               not null comment '1 红牛NFT活动',
    activity_type                 tinyint unsigned default '0'               not null comment '1正常4期NFT活动 2特殊的3期活动',
    activity_name                 varchar(100)     default ''                not null comment '活动名称',
    start_time                    timestamp        default CURRENT_TIMESTAMP not null comment '活动开始时间',
    end_time                      timestamp        default CURRENT_TIMESTAMP not null comment '活动结束时间',
    receive_start_time            timestamp        default CURRENT_TIMESTAMP not null comment '领取开始时间',
    receive_end_time              timestamp        default CURRENT_TIMESTAMP not null comment '领取结束时间',
    task_start_time               timestamp        default CURRENT_TIMESTAMP not null comment '额外活动开始时间 可以通过做任务领取NFT的时间段',
    task_end_time                 timestamp        default CURRENT_TIMESTAMP not null comment '额外活动结束时间 可以通过做任务领取NFT的时间段',
    draw_end_time                 timestamp        default CURRENT_TIMESTAMP not null comment '抽奖结束时间',
    activity_icon                 varchar(255)     default ''                not null comment '活动图片',
    activity_title                varchar(255)     default ''                not null comment '活动标题',
    created_at                    timestamp        default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at                    timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    surprise_extension_start_time timestamp        default CURRENT_TIMESTAMP not null comment '惊喜加时阶段-开始时间',
    surprise_extension_end_time   timestamp        default CURRENT_TIMESTAMP not null comment '惊喜加时阶段-结束时间',
    draw_start_time               timestamp        default CURRENT_TIMESTAMP not null comment '抽奖-开始时间',
    settlement_start_time         timestamp        default CURRENT_TIMESTAMP not null comment '结算-开始时间',
    settlement_end_time           timestamp        default CURRENT_TIMESTAMP not null comment '结算-结束时间',
    announcement_start_time       timestamp        default CURRENT_TIMESTAMP not null comment '结果公布-开始时间',
    announcement_end_time         timestamp        default CURRENT_TIMESTAMP not null comment '结算-结束时间',
    primary key (id),
    index idx_activity_id (activity_id)
)
    comment '活动信息表-预发环境' collate = utf8mb4_unicode_ci;




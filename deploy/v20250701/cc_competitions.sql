CREATE TABLE `cc_competitions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` int unsigned NOT NULL DEFAULT '0',
  `lang` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'en',
  `type_id` int unsigned NOT NULL DEFAULT '0',
  `competition_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `start_at` int NOT NULL DEFAULT '0',
  `end_at` int NOT NULL DEFAULT '0',
  `sort` int NOT NULL DEFAULT '0',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '浅色模式入口图',
  `hot` int unsigned NOT NULL DEFAULT '0' COMMENT '火热度',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '1上架 0下架',
  `pinned` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '0未置顶，1已置顶',
  `pinned_at` timestamp NULL DEFAULT NULL,
  `listed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'all' COMMENT '全部：all，部分用逗号分割，如：182,36',
  `crowd` int NOT NULL DEFAULT '0' COMMENT '人群id',
  `img_dark` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '深色模式入口图',
  `master_one_line` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配图标题（第一行）',
  `master_two_line` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配图标题（第二行）',
  `slave_one_line` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配图副标题（第一行）',
  `slave_two_line` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配图副标题（第二行）',
  `competition_title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '赛事标题',
  `is_en_last` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否英文兜底',
  `is_hot` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否是热门活动，1-是，0-否',
  `show_in_app` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否在app端展示，1:是，0:否',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_lang_type_id_status` (`lang`,`type_id`,`status`),
  KEY `idx_query` (`status`,`parent_id`,`type_id`,`start_at`,`end_at`,`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1284 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
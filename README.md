# 营销活动服务

* 临时性营销活动专用服务

## 项目结构
[详细目录结构以及上下游调用链路参考文档](https://gtglobal.jp.larksuite.com/wiki/Xf9aw7e6jiVq2jkKl7Tjl1r7pTc?fromScene=spaceOverview)

具体目录结构如下：
```
.
├── api                           # 接口定义文件
├── deploy                        # 部署文件
│   ├── seed                # 最近上线执行的sql语句
│   │   └── 2024121201.sql
│   └── sql                       # 生成表model文件的建表sql文件
│       └── system.sql
├── etc                           # 配置文件
│   ├── marketing_activity-debug.yaml
├── internal                      # 项目内部文件
│   ├── config                    # 配置文件
│   │   └── config.go
│   ├── consts                    # 常量定义      
│   │   ├── err_codes.go          # 错误码
│   │   ├── language.go           # 语言相关
│   │   └── redis_keys.go         # redis Key
│   ├── handler                   # handler定义
│   │   ├── inner                 # 内部接口控制器文件
│   │   ├── outer                 # 外部接口控制器文件
│   │   │   └── routes.go   # 内部接口路由文件
│   │   │   └── routes_outer.go   # 外部接口路由文件
│   ├── logic                     # 业务逻辑文件logic
│   │   ├── inner                 # 内部接口逻辑文件
│   │   ├── outer                 # 外部接口逻辑文件
│   ├── middleware                # 接口中间件
│   ├── model                     # 数据库model文件
│   ├── pkg                       # 公共逻辑库
│   │   ├── migrate               # 数据库迁移
│   │   └── white_list            # 白名单
│   ├── svc                       # 服务全局上下文
│   ├── types                     # 类型定义
│   └── utils                     # 工具函数包
├── script                        # 脚本  
│
├── Dockerfile                    # dockerfile
├── Makefile                      
├── README.md                     # 项目说明文档
├── marketing_activity.api        # 接口定义入口文件
└── marketing_activity.go         # main函数入口文件
 
```


## 开发环境准备
* go v1.23
* goctl v1.1.12 (务必从 [gatebackend/go-zero](https://bitbucket.org/gatebackend/go-zero/src/master/) 编译安装)

### 本地开发
#### 环境变量
```shell
ENV_NAME=local
NACOS_DATA_ID=gateio-service-marketing-activity
NACOS_HOST=127.0.0.1
NACOS_NAMESPACE=gateio
NACOS_PASSWORD=nacos_gate
NACOS_PORT=28848
NACOS_USERNAME=nacos_gate
NACOS_SERVICE_CALL_USERNAME=nacos_gate
NACOS_SERVICE_CALL_PASSWORD=nacos_gate
NACOS_SERVICE_CALL_NAMESPACE=gateio

```

#### 隧道
[参考文档](https://gtglobal.jp.larksuite.com/wiki/YyDWwWqnxitx0OkU6EOjm8dppJh)


## 代码生成
将模板仓库 [skeleton_go](https://bitbucket.org/gateio/gateio_service_skeleton_go/src/master/) 放在与当前项目**同级目录**下

### api代码生成
```shell
goctl api go -api ./marketing_activity.api -dir .  -home ../gateio_service_skeleton_go/template  -style go_zero
```

### model代码生成 

```shell
goctl model mysql ddl -src ./deploy/sql/contract_transform_user_task.sql -dir ./internal/models  -style go_zero
```
#### 已有表结构生产model的方式
```shell
goctl model mysql datasource --dir internal/model/common/ --table article  --url "root:CsqdULOBvcxC&wxP@tcp(127.0.0.1:33066)/gateio" --style="go_zero"
```
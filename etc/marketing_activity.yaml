Basic:
  Name: gateio_service_marketing_activity-api
  Mode: "dev"

  Log:
    Encoding: "plain"
    Level: "debug"

  DevServer:
    Enabled: true
    Port: 9502

OuterServiceConf:
  Name: gateio_service_marketing_activity-api-outer
  Host: 0.0.0.0
  Port: 3460

InnerServiceConf:
  Name: gateio_service_marketing_activity-api-inner
  Host: 0.0.0.0
  Port: 9580

Nacos:
  Ip: ${NACOS_HOST}
  Port: ${NACOS_PORT}
  Configs:
    default:
      Username: ${NACOS_USERNAME}
      Password: ${NACOS_PASSWORD}
      DataId: ${NACOS_DATA_ID}
      NamespaceId: ${NACOS_NAMESPACE}
      TimeoutMs: 5000 # 请求 Nacos 服务器的超时时间（毫秒），默认为 5000ms
      NotLoadCacheAtStart: true # 启动时不加载本地缓存，默认为 true
      LogDir: "./nacos/log" # Nacos 日志目录，默认为 /tmp/nacos/log
      CacheDir: "./nacos/cache" # Nacos 本地缓存目录，默认为 /tmp/nacos/cache
      LogLevel: "debug" # Nacos 日志级别，默认为 debug
      Format: "yaml"
    gateio-service-call:
      Username: ${NACOS_SERVICE_CALL_USERNAME}
      Password: ${NACOS_SERVICE_CALL_PASSWORD}
      DataId: gateio-service-call
      NamespaceId: ${NACOS_SERVICE_CALL_NAMESPACE}
      TimeoutMs: 5000 # 请求 Nacos 服务器的超时时间（毫秒），默认为 5000ms
      NotLoadCacheAtStart: true # 启动时不加载本地缓存，默认为 true
      LogDir: "./nacos/log" # Nacos 日志目录，默认为 /tmp/nacos/log
      CacheDir: "./nacos/cache" # Nacos 本地缓存目录，默认为 /tmp/nacos/cache
      LogLevel: "debug" # Nacos 日志级别，默认为 debug
      Format: "json"
    sign_key_map:
      Username: ${NACOS_USERNAME}
      Password: ${NACOS_PASSWORD}
      DataId: ${NACOS_APP_SIGN_DATA_ID}
      NamespaceId: "gateio"
      TimeoutMs: 5000 # 请求 Nacos 服务器的超时时间（毫秒），默认为 5000ms
      NotLoadCacheAtStart: true # 启动时不加载本地缓存，默认为 true
      LogDir: "./nacos/log" # Nacos 日志目录，默认为 /tmp/nacos/log
      CacheDir: "./nacos/cache" # Nacos 本地缓存目录，默认为 /tmp/nacos/cache
      LogLevel: "debug" # Nacos 日志级别，默认为 debug
      Format: "json"
